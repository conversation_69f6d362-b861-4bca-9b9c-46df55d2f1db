# =============================================================================
# AI Email Outreach Tool - Production Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
DB_PASSWORD=your_secure_database_password_here
POSTGRES_DB=email_outreach_prod
POSTGRES_USER=email_user

# -----------------------------------------------------------------------------
# Redis Configuration (Optional - for caching and sessions)
# -----------------------------------------------------------------------------
REDIS_PASSWORD=your_secure_redis_password_here

# -----------------------------------------------------------------------------
# Application Security
# -----------------------------------------------------------------------------
SECRET_KEY=your_super_secure_secret_key_minimum_32_characters_long
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# -----------------------------------------------------------------------------
# CORS Configuration
# -----------------------------------------------------------------------------
ALLOWED_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]

# -----------------------------------------------------------------------------
# Frontend Configuration
# -----------------------------------------------------------------------------
FRONTEND_API_URL=https://api.yourdomain.com

# -----------------------------------------------------------------------------
# SMTP Email Configuration
# -----------------------------------------------------------------------------
# SendGrid Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=your_sendgrid_api_key_here

# AWS SES Configuration (Alternative)
# SMTP_HOST=email-smtp.us-east-1.amazonaws.com
# SMTP_PORT=587
# SMTP_USERNAME=your_aws_access_key_id
# SMTP_PASSWORD=your_aws_secret_access_key

# Gmail Configuration (Alternative - requires app password)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password

# Email Sender Information
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your Company Name

# -----------------------------------------------------------------------------
# AI Service Configuration
# -----------------------------------------------------------------------------
# OpenAI Configuration
OPENAI_API_KEY=sk-your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Claude Configuration (Alternative/Additional)
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_MODEL=claude-3-sonnet-20240229

# AI Features
AI_ENABLED=true
AI_FALLBACK_ENABLED=true
AI_RATE_LIMIT_PER_MINUTE=60

# -----------------------------------------------------------------------------
# Application Configuration
# -----------------------------------------------------------------------------
APP_NAME=AI Email Outreach Tool
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false

# -----------------------------------------------------------------------------
# Rate Limiting
# -----------------------------------------------------------------------------
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_ENABLED=true

# -----------------------------------------------------------------------------
# File Upload Configuration
# -----------------------------------------------------------------------------
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=/app/uploads
ALLOWED_FILE_TYPES=["csv","xlsx","txt","json"]

# -----------------------------------------------------------------------------
# Monitoring and Logging
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
LOG_FORMAT=json
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# Grafana Configuration (if using monitoring)
GRAFANA_PASSWORD=your_secure_grafana_password

# -----------------------------------------------------------------------------
# Domain Configuration
# -----------------------------------------------------------------------------
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com

# -----------------------------------------------------------------------------
# SSL Configuration
# -----------------------------------------------------------------------------
SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem

# -----------------------------------------------------------------------------
# Backup Configuration
# -----------------------------------------------------------------------------
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket-name
AWS_ACCESS_KEY_ID=your_aws_access_key_for_backups
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_for_backups

# -----------------------------------------------------------------------------
# Performance Configuration
# -----------------------------------------------------------------------------
# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Worker Configuration
WORKER_CONCURRENCY=4
WORKER_MAX_TASKS_PER_CHILD=1000

# Cache Configuration
CACHE_TTL=3600  # 1 hour
CACHE_MAX_SIZE=1000

# -----------------------------------------------------------------------------
# Security Headers
# -----------------------------------------------------------------------------
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true

# -----------------------------------------------------------------------------
# Analytics and Tracking (Optional)
# -----------------------------------------------------------------------------
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your_mixpanel_token
HOTJAR_ID=your_hotjar_id

# -----------------------------------------------------------------------------
# Third-party Integrations (Optional)
# -----------------------------------------------------------------------------
# Stripe for payments
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Slack for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# -----------------------------------------------------------------------------
# Feature Flags
# -----------------------------------------------------------------------------
FEATURE_AI_ASSISTANT=true
FEATURE_BULK_OPERATIONS=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_TEAM_MANAGEMENT=false
FEATURE_API_ACCESS=true

# -----------------------------------------------------------------------------
# Compliance and Privacy
# -----------------------------------------------------------------------------
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
PRIVACY_POLICY_URL=https://yourdomain.com/privacy
TERMS_OF_SERVICE_URL=https://yourdomain.com/terms

# -----------------------------------------------------------------------------
# Development and Testing (Production should be false)
# -----------------------------------------------------------------------------
ENABLE_SWAGGER_UI=false
ENABLE_CORS_ALL=false
ENABLE_DEBUG_TOOLBAR=false
MOCK_EMAIL_SENDING=false

# -----------------------------------------------------------------------------
# Custom Configuration
# -----------------------------------------------------------------------------
# Add any custom environment variables your deployment needs
CUSTOM_FEATURE_FLAG=true
CUSTOM_API_ENDPOINT=https://api.external-service.com
CUSTOM_TIMEOUT=30
