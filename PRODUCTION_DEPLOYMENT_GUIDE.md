# 🚀 Production Deployment Guide - AI Email Outreach Tool

## 📋 **Pre-Deployment Checklist**

### ✅ **System Requirements**
- **Node.js**: 18+ for frontend build
- **Python**: 3.11+ for backend
- **Database**: PostgreSQL 14+ (production) or SQLite (development)
- **Redis**: For caching and session management (optional)
- **SMTP Service**: SendGrid, AWS SES, or similar
- **Domain**: Custom domain with SSL certificate

### ✅ **Environment Setup**
- [ ] Production server provisioned (AWS EC2, DigitalOcean, etc.)
- [ ] Domain name configured with DNS
- [ ] SSL certificate obtained (Let's Encrypt recommended)
- [ ] Database server setup (PostgreSQL)
- [ ] SMTP service configured
- [ ] Environment variables prepared

---

## 🔧 **Backend Deployment**

### **1. Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.11
sudo apt install python3.11 python3.11-venv python3.11-dev -y

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Install Nginx (reverse proxy)
sudo apt install nginx -y

# Install Supervisor (process management)
sudo apt install supervisor -y
```

### **2. Application Setup**
```bash
# Create application directory
sudo mkdir -p /var/www/email-outreach
cd /var/www/email-outreach

# Clone repository
git clone <your-repo-url> .

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r backend/requirements.txt

# Create production environment file
cp backend/.env.example backend/.env.production
```

### **3. Environment Configuration**
Create `/var/www/email-outreach/backend/.env.production`:
```env
# Database
DATABASE_URL=postgresql+asyncpg://username:password@localhost/email_outreach_prod

# Security
SECRET_KEY=your-super-secure-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
ALLOWED_ORIGINS=["https://yourdomain.com"]

# SMTP Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=your-sendgrid-api-key
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your Company Name

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
CLAUDE_API_KEY=your-claude-api-key
AI_ENABLED=true

# Application
APP_NAME=AI Email Outreach Tool
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=/var/www/email-outreach/uploads
```

### **4. Database Setup**
```bash
# Create PostgreSQL database
sudo -u postgres psql
CREATE DATABASE email_outreach_prod;
CREATE USER email_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE email_outreach_prod TO email_user;
\q

# Run database migrations
cd /var/www/email-outreach/backend
source ../venv/bin/activate
alembic upgrade head
```

### **5. Supervisor Configuration**
Create `/etc/supervisor/conf.d/email-outreach.conf`:
```ini
[program:email-outreach-backend]
command=/var/www/email-outreach/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
directory=/var/www/email-outreach/backend
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/email-outreach/backend.log
environment=ENV_FILE="/var/www/email-outreach/backend/.env.production"
```

### **6. Nginx Configuration**
Create `/etc/nginx/sites-available/email-outreach`:
```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # API proxy
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://yourdomain.com";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    }

    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

---

## 🎨 **Frontend Deployment**

### **1. Build Process**
```bash
# Navigate to frontend directory
cd /var/www/email-outreach/frontend

# Install dependencies
npm install

# Create production environment file
cp .env.example .env.production
```

### **2. Environment Configuration**
Create `/var/www/email-outreach/frontend/.env.production`:
```env
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_NAME=AI Email Outreach Tool
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production
VITE_DEBUG=false
VITE_SENTRY_DSN=your-sentry-dsn-for-error-tracking
```

### **3. Build and Deploy**
```bash
# Build for production
npm run build

# Copy build files to web directory
sudo cp -r dist/* /var/www/html/
sudo chown -R www-data:www-data /var/www/html/
```

### **4. Frontend Nginx Configuration**
Create `/etc/nginx/sites-available/email-outreach-frontend`:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    root /var/www/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # React Router support
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy (if needed)
    location /api/ {
        proxy_pass https://api.yourdomain.com/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🔒 **Security Configuration**

### **1. SSL Certificate Setup**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificates
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **2. Firewall Configuration**
```bash
# Configure UFW
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 5432  # PostgreSQL (if external access needed)
sudo ufw enable
```

### **3. Database Security**
```bash
# Secure PostgreSQL
sudo -u postgres psql
ALTER USER postgres PASSWORD 'secure_postgres_password';
\q

# Edit PostgreSQL config
sudo nano /etc/postgresql/14/main/postgresql.conf
# Set: listen_addresses = 'localhost'

sudo nano /etc/postgresql/14/main/pg_hba.conf
# Ensure local connections use md5 authentication
```

---

## 📊 **Monitoring and Logging**

### **1. Log Configuration**
```bash
# Create log directories
sudo mkdir -p /var/log/email-outreach
sudo chown www-data:www-data /var/log/email-outreach

# Logrotate configuration
sudo nano /etc/logrotate.d/email-outreach
```

Add to logrotate config:
```
/var/log/email-outreach/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        supervisorctl restart email-outreach-backend
    endscript
}
```

### **2. Health Monitoring**
Create `/var/www/email-outreach/scripts/health-check.sh`:
```bash
#!/bin/bash
# Health check script

# Check backend
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "Backend: OK"
else
    echo "Backend: FAILED"
    supervisorctl restart email-outreach-backend
fi

# Check database
if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "Database: OK"
else
    echo "Database: FAILED"
    # Alert administrators
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage: WARNING ($DISK_USAGE%)"
fi
```

---

## 🚀 **Deployment Commands**

### **Final Deployment Steps**
```bash
# Enable Nginx sites
sudo ln -s /etc/nginx/sites-available/email-outreach /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/email-outreach-frontend /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Start services
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start email-outreach-backend
sudo systemctl restart nginx

# Verify deployment
curl https://api.yourdomain.com/health
curl https://yourdomain.com
```

### **Post-Deployment Verification**
- [ ] Frontend loads correctly
- [ ] API endpoints respond
- [ ] Authentication works
- [ ] Database connections successful
- [ ] Email sending functional
- [ ] SSL certificates valid
- [ ] Monitoring active

---

## 🔄 **Maintenance and Updates**

### **Update Process**
```bash
# Backup database
pg_dump email_outreach_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# Pull latest code
git pull origin main

# Update backend
source venv/bin/activate
pip install -r backend/requirements.txt
alembic upgrade head
supervisorctl restart email-outreach-backend

# Update frontend
npm install
npm run build
sudo cp -r dist/* /var/www/html/
```

### **Monitoring Commands**
```bash
# Check service status
supervisorctl status
systemctl status nginx
systemctl status postgresql

# View logs
tail -f /var/log/email-outreach/backend.log
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

---

## 📞 **Support and Troubleshooting**

### **Common Issues**
1. **502 Bad Gateway**: Check backend service status
2. **Database Connection**: Verify PostgreSQL credentials
3. **CORS Errors**: Check allowed origins configuration
4. **SSL Issues**: Verify certificate renewal

### **Performance Optimization**
- Enable Redis for caching
- Configure CDN for static assets
- Implement database connection pooling
- Set up load balancing for high traffic

---

**🎉 Your AI Email Outreach Tool is now production-ready!**
