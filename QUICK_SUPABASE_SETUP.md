# ⚡ Quick Supabase Setup - AI Email Outreach Tool

## 🚀 **5-Minute Supabase Integration**

### **Step 1: Create Supabase Project (2 minutes)**

1. **Go to [supabase.com](https://supabase.com)** and sign up
2. **Click "New Project"**
3. **Fill in details:**
   - Organization: Your organization
   - Name: `ai-email-outreach`
   - Database Password: Generate a strong password (save it!)
   - Region: Choose closest to your users
4. **Click "Create new project"**
5. **Wait for setup to complete** (1-2 minutes)

### **Step 2: Get Your Credentials (30 seconds)**

From your Supabase dashboard, go to **Settings → API**:

```env
# Copy these values:
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres
```

### **Step 3: Update Environment Files (1 minute)**

**Backend `.env`:**
```env
# Replace your existing database config with:
DATABASE_URL=postgresql+asyncpg://postgres:YOUR_PASSWORD@db.YOUR_PROJECT_ID.supabase.co:5432/postgres

# Add Supabase config:
SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
SUPABASE_ANON_KEY=YOUR_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY
USE_SUPABASE_AUTH=false  # Keep false to use existing auth system
```

**Frontend `.env`:**
```env
# Add these lines:
VITE_SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
VITE_SUPABASE_ANON_KEY=YOUR_ANON_KEY
```

### **Step 4: Create Database Schema (1 minute)**

1. **Go to Supabase Dashboard → SQL Editor**
2. **Click "New Query"**
3. **Paste this SQL and click "Run":**

```sql
-- Create tables matching your existing schema
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS campaigns (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    campaign_type VARCHAR(50) NOT NULL DEFAULT 'outreach',
    status VARCHAR(50) DEFAULT 'draft',
    from_name VARCHAR(255) NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    reply_to_email VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, email)
);

CREATE TABLE IF NOT EXISTS email_sequences (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    subject_line TEXT NOT NULL,
    email_content TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    delay_days INTEGER DEFAULT 0,
    delay_hours INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    emails_replied INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS email_logs (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id) ON DELETE CASCADE,
    sequence_id INTEGER REFERENCES email_sequences(id) ON DELETE CASCADE,
    contact_id INTEGER REFERENCES contacts(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    to_email VARCHAR(255) NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_campaigns_user_id ON campaigns(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_email_sequences_campaign_id ON email_sequences(campaign_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_campaign_id ON email_logs(campaign_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_contact_id ON email_logs(contact_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Create function for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_sequences_updated_at BEFORE UPDATE ON email_sequences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_logs_updated_at BEFORE UPDATE ON email_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **Step 5: Test the Connection (30 seconds)**

**Start your backend:**
```bash
cd backend
python -m uvicorn app.main:app --reload
```

**Check the logs** - you should see:
```
INFO:     Database connected successfully
INFO:     Application startup complete.
```

**Test the API:**
```bash
curl http://localhost:8000/health
# Should return: {"status": "healthy"}
```

---

## 🎉 **That's It! You're Connected to Supabase!**

### **What You Get:**

✅ **Managed PostgreSQL Database** - No server maintenance needed
✅ **Automatic Backups** - Your data is safe
✅ **Scalable Infrastructure** - Handles growth automatically
✅ **Real-time Dashboard** - Monitor your database visually
✅ **Built-in Analytics** - See usage metrics
✅ **Global CDN** - Fast access worldwide

### **Supabase Dashboard Features:**

1. **Table Editor** - Visual database management
2. **SQL Editor** - Run custom queries
3. **API Docs** - Auto-generated REST API
4. **Auth Management** - User authentication (if you want to switch later)
5. **Storage** - File uploads and management
6. **Edge Functions** - Serverless functions
7. **Logs** - Real-time application logs

### **Next Steps (Optional):**

1. **Enable Real-time** - Get live updates in your UI
2. **Add Row Level Security** - Secure data access
3. **Use Supabase Auth** - Replace custom auth system
4. **Add File Storage** - Handle file uploads
5. **Set up Edge Functions** - Custom serverless logic

### **Monitoring Your Database:**

- **Dashboard**: https://app.supabase.com/project/YOUR_PROJECT_ID
- **Database**: Go to "Database" tab to see your tables
- **Logs**: Go to "Logs" tab to see real-time activity
- **API**: Go to "API" tab to see auto-generated endpoints

---

## 🔧 **Troubleshooting:**

**Connection Issues:**
```bash
# Test direct database connection
psql "***********************************************************************/postgres"
```

**Environment Variables:**
```bash
# Check if variables are loaded
python -c "import os; print(os.getenv('DATABASE_URL'))"
```

**Firewall Issues:**
- Supabase uses standard PostgreSQL port 5432
- Make sure your firewall allows outbound connections

---

## 💡 **Pro Tips:**

1. **Use Connection Pooling** - Supabase handles this automatically
2. **Monitor Usage** - Check dashboard for performance metrics
3. **Set up Alerts** - Get notified of issues
4. **Use Staging Environment** - Create separate project for testing
5. **Backup Strategy** - Supabase auto-backups, but consider additional backups for critical data

**Your AI Email Outreach Tool is now powered by Supabase! 🚀**

**Total setup time: ~5 minutes**
**Maintenance time: 0 minutes (it's managed!)**
