# 🚀 Supabase Integration Guide - AI Email Outreach Tool

## 📋 **Why Supabase?**

Supabase provides:
- ✅ **Managed PostgreSQL Database** - No server maintenance
- ✅ **Built-in Authentication** - User management out of the box
- ✅ **Real-time Subscriptions** - Live updates for your UI
- ✅ **Auto-generated APIs** - REST and GraphQL endpoints
- ✅ **Dashboard & Analytics** - Visual database management
- ✅ **Edge Functions** - Serverless functions for custom logic
- ✅ **Storage** - File uploads and management

---

## 🔧 **Step 1: Setup Supabase Project**

### **1.1 Create Supabase Account**
1. Go to [supabase.com](https://supabase.com)
2. Sign up for a free account
3. Create a new project
4. Choose a region close to your users
5. Set a strong database password

### **1.2 Get Connection Details**
From your Supabase dashboard:
- **Project URL**: `https://your-project-id.supabase.co`
- **API Key (anon)**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **API Key (service_role)**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Database URL**: `postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres`

## 🎯 **Why Supabase?**

Supabase provides:
- **PostgreSQL Database**: Managed, scalable database
- **Real-time Subscriptions**: Live updates for your UI
- **Authentication**: Built-in auth with social providers
- **Storage**: File uploads and management
- **Edge Functions**: Serverless functions
- **Dashboard**: Visual database management

---

## 📋 **Setup Supabase Project**

### **1. Create Supabase Project**
1. Go to [supabase.com](https://supabase.com)
2. Sign up/Login and create a new project
3. Choose your region and set a strong database password
4. Wait for project initialization (2-3 minutes)

### **2. Get Project Credentials**
From your Supabase dashboard, note down:
- **Project URL**: `https://your-project-id.supabase.co`
- **API Key (anon)**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Database URL**: `postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres`
- **Service Role Key**: For server-side operations

---

## 🔧 **Backend Integration**

### **1. Update Backend Dependencies**
Add to `backend/requirements.txt`:
```txt
supabase==2.0.2
postgrest==0.13.0
realtime==1.0.0
```

### **2. Create Supabase Configuration**
Create `backend/app/core/supabase.py`:
```python
import os
from supabase import create_client, Client
from typing import Optional

class SupabaseConfig:
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.key = os.getenv("SUPABASE_ANON_KEY")
        self.service_key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not self.url or not self.key:
            raise ValueError("Supabase URL and API key must be provided")
    
    def get_client(self, use_service_key: bool = False) -> Client:
        """Get Supabase client"""
        key = self.service_key if use_service_key else self.key
        return create_client(self.url, key)

# Global instance
supabase_config = SupabaseConfig()

# Client instances
supabase: Client = supabase_config.get_client()
supabase_admin: Client = supabase_config.get_client(use_service_key=True)
```

### **3. Update Database Configuration**
Update `backend/app/core/database.py`:
```python
import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

# Supabase database URL
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
)

# Create async engine
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600,
)

# Create session factory
AsyncSessionLocal = sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

Base = declarative_base()

async def get_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
```

### **4. Update Environment Variables**
Update `backend/.env`:
```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_KEY=your-service-role-key-here

# Database (Supabase PostgreSQL)
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres

# Optional: Use Supabase Auth instead of custom JWT
USE_SUPABASE_AUTH=true
```

### **5. Create Database Schema in Supabase**
Run this SQL in Supabase SQL Editor:
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    subscription_plan TEXT DEFAULT 'free',
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaigns table
CREATE TABLE public.campaigns (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    campaign_type VARCHAR(50) DEFAULT 'outreach',
    status VARCHAR(50) DEFAULT 'draft',
    from_name VARCHAR(255),
    from_email VARCHAR(255),
    reply_to_email VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contacts table
CREATE TABLE public.contacts (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email sequences table
CREATE TABLE public.email_sequences (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES public.campaigns(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    subject_line TEXT,
    email_content TEXT,
    order_index INTEGER DEFAULT 0,
    delay_days INTEGER DEFAULT 0,
    delay_hours INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email sends table (for tracking)
CREATE TABLE public.email_sends (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES public.campaigns(id),
    sequence_id INTEGER REFERENCES public.email_sequences(id),
    contact_id INTEGER REFERENCES public.contacts(id),
    user_id UUID REFERENCES auth.users(id),
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security (RLS) Policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_sequences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_sends ENABLE ROW LEVEL SECURITY;

-- Policies for user_profiles
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Policies for campaigns
CREATE POLICY "Users can view own campaigns" ON public.campaigns
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own campaigns" ON public.campaigns
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own campaigns" ON public.campaigns
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own campaigns" ON public.campaigns
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for contacts
CREATE POLICY "Users can view own contacts" ON public.contacts
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own contacts" ON public.contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own contacts" ON public.contacts
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own contacts" ON public.contacts
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for email_sequences
CREATE POLICY "Users can view own sequences" ON public.email_sequences
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user_id FROM public.campaigns WHERE id = campaign_id
        )
    );
CREATE POLICY "Users can insert own sequences" ON public.email_sequences
    FOR INSERT WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM public.campaigns WHERE id = campaign_id
        )
    );
CREATE POLICY "Users can update own sequences" ON public.email_sequences
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM public.campaigns WHERE id = campaign_id
        )
    );
CREATE POLICY "Users can delete own sequences" ON public.email_sequences
    FOR DELETE USING (
        auth.uid() IN (
            SELECT user_id FROM public.campaigns WHERE id = campaign_id
        )
    );

-- Policies for email_sends
CREATE POLICY "Users can view own email sends" ON public.email_sends
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own email sends" ON public.email_sends
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own email sends" ON public.email_sends
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON public.campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON public.contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_sequences_updated_at BEFORE UPDATE ON public.email_sequences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

## 🎨 **Frontend Integration**

### **1. Install Supabase Client**
```bash
cd frontend
npm install @supabase/supabase-js
```

### **2. Create Supabase Configuration**
Create `frontend/src/lib/supabase.ts`:
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for your database
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          full_name: string | null
          subscription_plan: string
          is_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          subscription_plan?: string
          is_verified?: boolean
        }
        Update: {
          full_name?: string | null
          subscription_plan?: string
          is_verified?: boolean
        }
      }
      campaigns: {
        Row: {
          id: number
          user_id: string
          name: string
          description: string | null
          campaign_type: string
          status: string
          from_name: string | null
          from_email: string | null
          reply_to_email: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          name: string
          description?: string | null
          campaign_type?: string
          status?: string
          from_name?: string | null
          from_email?: string | null
          reply_to_email?: string | null
        }
        Update: {
          name?: string
          description?: string | null
          campaign_type?: string
          status?: string
          from_name?: string | null
          from_email?: string | null
          reply_to_email?: string | null
        }
      }
      // Add other table types...
    }
  }
}
```

### **3. Update Authentication Context**
Update `frontend/src/contexts/AuthContext.tsx`:
```typescript
import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, fullName: string) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        // Create user profile on sign up
        if (event === 'SIGNED_UP' && session?.user) {
          await createUserProfile(session.user)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const createUserProfile = async (user: User) => {
    const { error } = await supabase
      .from('user_profiles')
      .insert({
        id: user.id,
        full_name: user.user_metadata.full_name || '',
        subscription_plan: 'free',
        is_verified: false
      })

    if (error) {
      console.error('Error creating user profile:', error)
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName
        }
      }
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) {
      throw new Error(error.message)
    }
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email)
    if (error) {
      throw new Error(error.message)
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signUp,
      signOut,
      resetPassword
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

### **4. Update API Service**
Update `frontend/src/services/api.ts`:
```typescript
import { supabase } from '../lib/supabase'
import type { Database } from '../lib/supabase'

type Campaign = Database['public']['Tables']['campaigns']['Row']
type Contact = Database['public']['Tables']['contacts']['Row']

class SupabaseApiService {
  // Campaigns
  async getCampaigns() {
    const { data, error } = await supabase
      .from('campaigns')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw new Error(error.message)
    return { data, total: data?.length || 0 }
  }

  async createCampaign(campaignData: Database['public']['Tables']['campaigns']['Insert']) {
    const { data, error } = await supabase
      .from('campaigns')
      .insert(campaignData)
      .select()
      .single()

    if (error) throw new Error(error.message)
    return data
  }

  async updateCampaign(id: number, updates: Database['public']['Tables']['campaigns']['Update']) {
    const { data, error } = await supabase
      .from('campaigns')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw new Error(error.message)
    return data
  }

  async deleteCampaign(id: number) {
    const { error } = await supabase
      .from('campaigns')
      .delete()
      .eq('id', id)

    if (error) throw new Error(error.message)
  }

  // Contacts
  async getContacts() {
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw new Error(error.message)
    return { data, total: data?.length || 0 }
  }

  async createContact(contactData: Database['public']['Tables']['contacts']['Insert']) {
    const { data, error } = await supabase
      .from('contacts')
      .insert(contactData)
      .select()
      .single()

    if (error) throw new Error(error.message)
    return data
  }

  // Real-time subscriptions
  subscribeToChanges(table: string, callback: (payload: any) => void) {
    return supabase
      .channel(`public:${table}`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table }, 
        callback
      )
      .subscribe()
  }

  // Analytics
  async getDashboardStats() {
    const [campaignsResult, contactsResult] = await Promise.all([
      supabase.from('campaigns').select('id', { count: 'exact' }),
      supabase.from('contacts').select('id', { count: 'exact' })
    ])

    return {
      total_campaigns: campaignsResult.count || 0,
      total_contacts: contactsResult.count || 0,
      total_sequences: 0, // Add sequence count
      total_emails_sent: 0, // Add from email_sends table
      overall_open_rate: 0, // Calculate from email_sends
      overall_reply_rate: 0 // Calculate from email_sends
    }
  }
}

export const apiService = new SupabaseApiService()
```

---

## 🔄 **Real-time Features**

### **Add Real-time Updates**
```typescript
// In your React components
import { useEffect } from 'react'
import { apiService } from '../services/api'

const CampaignsPage = () => {
  const [campaigns, setCampaigns] = useState([])

  useEffect(() => {
    // Subscribe to real-time changes
    const subscription = apiService.subscribeToChanges('campaigns', (payload) => {
      console.log('Change received!', payload)
      
      switch (payload.eventType) {
        case 'INSERT':
          setCampaigns(prev => [payload.new, ...prev])
          break
        case 'UPDATE':
          setCampaigns(prev => prev.map(c => 
            c.id === payload.new.id ? payload.new : c
          ))
          break
        case 'DELETE':
          setCampaigns(prev => prev.filter(c => c.id !== payload.old.id))
          break
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Rest of component...
}
```

---

## 🌍 **Environment Variables**

### **Frontend (.env)**
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### **Backend (.env)**
```env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_KEY=your-service-role-key-here
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres
USE_SUPABASE_AUTH=true
```

---

## 🚀 **Benefits of Supabase Integration**

1. **Managed Database**: No need to manage PostgreSQL yourself
2. **Real-time Updates**: Live UI updates without polling
3. **Built-in Authentication**: Social logins, email verification
4. **Row Level Security**: Database-level security policies
5. **File Storage**: For email attachments and user uploads
6. **Edge Functions**: Serverless functions for complex logic
7. **Dashboard**: Visual database management
8. **Automatic Backups**: Built-in backup and recovery

---

## 🎯 **Next Steps**

1. **Create Supabase project** and get credentials
2. **Run the SQL schema** in Supabase SQL Editor
3. **Update environment variables** in both frontend and backend
4. **Install dependencies** and update code
5. **Test the integration** with your existing features
6. **Add real-time subscriptions** for live updates
7. **Configure Row Level Security** policies as needed

Your AI Email Outreach Tool will now be powered by Supabase! 🎉
