# Database Configuration (Supabase)
DATABASE_URL=postgresql://username:<EMAIL>:5432/postgres
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# JWT Authentication
SECRET_KEY=your-super-secret-jwt-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenRouter AI Configuration
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Email Configuration (Gmail SMTP)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-gmail-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your Name

# IMAP Configuration for tracking
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your-gmail-app-password

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Application Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
API_V1_STR=/api/v1

# Email Tracking
TRACKING_PIXEL_BASE_URL=http://localhost:8000
TRACKING_DOMAIN=localhost:8000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Warm-up Configuration
WARMUP_ENABLED=True
WARMUP_EMAILS_PER_DAY=10
WARMUP_DELAY_MINUTES=60
