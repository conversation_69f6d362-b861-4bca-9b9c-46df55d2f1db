"""
Main API router that includes all endpoint routers
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, campaigns, contacts, sequences, email_sending, ai_assistant, analytics

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["Campaigns"])
api_router.include_router(contacts.router, prefix="/contacts", tags=["Contacts"])
api_router.include_router(sequences.router, prefix="/sequences", tags=["Email Sequences"])
api_router.include_router(email_sending.router, prefix="/email", tags=["Email Sending"])
api_router.include_router(ai_assistant.router, prefix="/ai-assistant", tags=["AI Assistant"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["Analytics"])
