"""
AI Assistant endpoints for email content generation and optimization
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.services.ai_service import ai_service
from app.crud.campaign import campaign_crud
from app.crud.sequence import sequence_crud
from app.crud.contact import contact_crud

router = APIRouter()


class EmailGenerationRequest(BaseModel):
    """Request schema for AI email generation"""
    prompt: str = Field(..., description="Main prompt for email generation")
    context: Optional[str] = Field(None, description="Additional context")
    tone: str = Field("professional", description="Email tone")
    length: str = Field("medium", description="Email length")
    include_subject: bool = Field(True, description="Generate subject line")
    contact_data: Optional[Dict[str, Any]] = Field(None, description="Contact data for personalization")


class SubjectOptimizationRequest(BaseModel):
    """Request schema for subject line optimization"""
    subject: str = Field(..., description="Original subject line")
    context: Optional[str] = Field(None, description="Email context")
    target_audience: Optional[str] = Field(None, description="Target audience")


class EmailPersonalizationRequest(BaseModel):
    """Request schema for email personalization"""
    template: str = Field(..., description="Email template")
    contact_id: int = Field(..., description="Contact ID for personalization")
    personalization_level: str = Field("medium", description="Personalization level")


class PerformanceAnalysisRequest(BaseModel):
    """Request schema for performance analysis"""
    sequence_id: int = Field(..., description="Email sequence ID")
    performance_data: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")


class EmailGenerationResponse(BaseModel):
    """Response schema for email generation"""
    subject: str
    content: str
    ai_generated: bool
    suggestions: Optional[List[str]] = None


class SubjectOptimizationResponse(BaseModel):
    """Response schema for subject optimization"""
    original_subject: str
    optimized_subjects: List[str]
    ai_generated: bool


@router.post("/generate-email", response_model=EmailGenerationResponse)
async def generate_email_content(
    request: EmailGenerationRequest,
    current_user: User = Depends(get_current_user)
):
    """Generate email content using AI"""
    try:
        result = await ai_service.generate_email_content(
            prompt=request.prompt,
            context=request.context,
            tone=request.tone,
            length=request.length,
            include_subject=request.include_subject,
            contact_data=request.contact_data
        )
        
        return EmailGenerationResponse(
            subject=result["subject"],
            content=result["content"],
            ai_generated=ai_service.ai_enabled,
            suggestions=[
                "Consider A/B testing different subject lines",
                "Add personalization for better engagement",
                "Include a clear call-to-action"
            ]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate email content: {str(e)}"
        )


@router.post("/optimize-subject", response_model=SubjectOptimizationResponse)
async def optimize_subject_line(
    request: SubjectOptimizationRequest,
    current_user: User = Depends(get_current_user)
):
    """Optimize subject line using AI"""
    try:
        optimized_subjects = await ai_service.optimize_subject_line(
            subject=request.subject,
            context=request.context,
            target_audience=request.target_audience
        )
        
        return SubjectOptimizationResponse(
            original_subject=request.subject,
            optimized_subjects=optimized_subjects,
            ai_generated=ai_service.ai_enabled
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to optimize subject line: {str(e)}"
        )


@router.post("/personalize-email")
async def personalize_email_content(
    request: EmailPersonalizationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Personalize email content for a specific contact"""
    try:
        # Get contact data
        contact = await contact_crud.get_by_user_and_id(
            db, user_id=current_user.id, contact_id=request.contact_id
        )
        
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )
        
        # Prepare contact data for personalization
        contact_data = {
            "first_name": contact.first_name,
            "last_name": contact.last_name,
            "full_name": f"{contact.first_name} {contact.last_name}".strip(),
            "company": contact.company,
            "job_title": contact.job_title,
            "email": contact.email,
            "phone": contact.phone,
            "website": contact.website,
            "city": contact.city,
            "state": contact.state,
            "country": contact.country,
            "custom_fields": contact.custom_fields or {}
        }
        
        # Personalize email
        personalized_content = await ai_service.personalize_email(
            template=request.template,
            contact_data=contact_data,
            personalization_level=request.personalization_level
        )
        
        return {
            "original_template": request.template,
            "personalized_content": personalized_content,
            "contact_name": f"{contact.first_name} {contact.last_name}".strip(),
            "personalization_level": request.personalization_level,
            "ai_generated": ai_service.ai_enabled
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to personalize email: {str(e)}"
        )


@router.post("/analyze-performance")
async def analyze_email_performance(
    request: PerformanceAnalysisRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Analyze email sequence performance using AI"""
    try:
        # Get sequence data
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=request.sequence_id
        )
        
        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email sequence not found"
            )
        
        # Prepare email content
        email_content = f"Subject: {sequence.subject}\n\nContent:\n{sequence.content}"
        
        # Use provided performance data or get from sequence
        performance_data = request.performance_data or {
            "open_rate": (sequence.emails_opened / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0,
            "click_rate": (sequence.emails_clicked / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0,
            "reply_rate": (sequence.emails_replied / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0,
            "bounce_rate": (sequence.emails_bounced / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0,
            "emails_sent": sequence.emails_sent,
            "emails_delivered": sequence.emails_delivered,
            "emails_opened": sequence.emails_opened,
            "emails_clicked": sequence.emails_clicked,
            "emails_replied": sequence.emails_replied,
            "emails_bounced": sequence.emails_bounced
        }
        
        # Analyze performance
        analysis = await ai_service.analyze_email_performance(
            email_content=email_content,
            performance_data=performance_data
        )
        
        return {
            "sequence_id": request.sequence_id,
            "sequence_name": sequence.name,
            "performance_data": performance_data,
            "analysis": analysis,
            "ai_generated": ai_service.ai_enabled,
            "recommendations": analysis.get("recommendations", []),
            "next_steps": analysis.get("ab_tests", [])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze performance: {str(e)}"
        )


@router.get("/templates")
async def get_email_templates(
    campaign_type: Optional[str] = None,
    industry: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get AI-generated email templates"""
    try:
        templates = {
            "outreach": [
                {
                    "name": "Cold Outreach - Professional",
                    "subject": "Quick question about {{company}}'s growth",
                    "content": """Hi {{first_name}},

I hope this email finds you well. I noticed {{company}} has been making impressive strides in the {{industry}} space.

We've helped similar companies achieve [specific benefit] through our [solution/service]. 

Would you be open to a brief 15-minute call this week to discuss how we might help {{company}} achieve similar results?

Best regards,
{{from_name}}""",
                    "tone": "professional",
                    "use_case": "Initial outreach to prospects"
                },
                {
                    "name": "Follow-up - Value-focused",
                    "subject": "Re: {{company}} - Following up with value",
                    "content": """Hi {{first_name}},

I wanted to follow up on my previous email about helping {{company}} with [specific challenge].

I thought you might find this relevant: [case study/resource] showing how we helped [similar company] achieve [specific result].

Would next Tuesday or Wednesday work for a brief call?

Best,
{{from_name}}""",
                    "tone": "professional",
                    "use_case": "Follow-up with value proposition"
                }
            ],
            "nurture": [
                {
                    "name": "Educational Content",
                    "subject": "Thought you'd find this interesting, {{first_name}}",
                    "content": """Hi {{first_name}},

I came across this article about [relevant topic] and thought it might be valuable for {{company}}.

[Link to resource]

The key insight that stood out to me was [specific insight] - something that could be particularly relevant for companies in the {{industry}} space.

Hope you find it useful!

Best,
{{from_name}}""",
                    "tone": "helpful",
                    "use_case": "Educational content sharing"
                }
            ]
        }
        
        if campaign_type:
            return {"templates": templates.get(campaign_type, [])}
        
        return {"templates": templates}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get templates: {str(e)}"
        )


@router.get("/ai-status")
async def get_ai_status(current_user: User = Depends(get_current_user)):
    """Get AI service status and capabilities"""
    return {
        "ai_enabled": ai_service.ai_enabled,
        "model": ai_service.model,
        "max_tokens": ai_service.max_tokens,
        "capabilities": {
            "email_generation": True,
            "subject_optimization": True,
            "content_personalization": True,
            "performance_analysis": True,
            "template_suggestions": True
        },
        "fallback_mode": not ai_service.ai_enabled
    }
