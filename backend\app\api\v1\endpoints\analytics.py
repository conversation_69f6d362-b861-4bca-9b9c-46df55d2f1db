"""
Analytics and reporting endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
from pydantic import BaseModel
import logging

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.campaign import Campaign
from app.models.contact import Contact
from app.models.sequence import EmailSequence
from app.crud.campaign import campaign_crud
from app.crud.contact import contact_crud
from app.crud.sequence import sequence_crud

router = APIRouter()
logger = logging.getLogger(__name__)


class AnalyticsOverview(BaseModel):
    """Analytics overview response"""
    total_campaigns: int
    total_contacts: int
    total_sequences: int
    total_emails_sent: int
    total_emails_delivered: int
    total_emails_opened: int
    total_emails_clicked: int
    total_emails_replied: int
    overall_open_rate: float
    overall_click_rate: float
    overall_reply_rate: float
    active_campaigns: int
    recent_activity: List[Dict[str, Any]]


@router.get("/dashboard", response_model=AnalyticsOverview)
async def get_dashboard_stats(
    days: int = Query(30, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard analytics overview"""
    try:
        # Get user's campaigns with error handling
        try:
            campaigns = await campaign_crud.get_multi_by_user(
                db, user_id=current_user.id, skip=0, limit=1000
            )
        except Exception as e:
            logger.error(f"Error getting campaigns: {str(e)}")
            campaigns = []

        # Get user's contacts
        try:
            contacts = await contact_crud.get_multi_by_user(
                db, user_id=current_user.id, skip=0, limit=1000
            )
        except Exception as e:
            logger.error(f"Error getting contacts: {str(e)}")
            contacts = []

        # Get user's sequences
        try:
            sequences = await sequence_crud.get_multi_by_user(
                db, user_id=current_user.id, skip=0, limit=1000
            )
        except Exception as e:
            logger.error(f"Error getting sequences: {str(e)}")
            sequences = []

        # Calculate totals
        total_emails_sent = sum(seq.emails_sent for seq in sequences)
        total_emails_delivered = sum(seq.emails_delivered for seq in sequences)
        total_emails_opened = sum(seq.emails_opened for seq in sequences)
        total_emails_clicked = sum(seq.emails_clicked for seq in sequences)
        total_emails_replied = sum(seq.emails_replied for seq in sequences)

        # Calculate rates
        overall_open_rate = (total_emails_opened / total_emails_delivered * 100) if total_emails_delivered > 0 else 0
        overall_click_rate = (total_emails_clicked / total_emails_delivered * 100) if total_emails_delivered > 0 else 0
        overall_reply_rate = (total_emails_replied / total_emails_delivered * 100) if total_emails_delivered > 0 else 0

        # Count active campaigns
        active_campaigns = len([c for c in campaigns if c.status == "active"])

        # Get recent activity
        recent_activity = [
            {
                "type": "campaign_created",
                "description": f"Campaign '{campaign.name}' created",
                "timestamp": campaign.created_at,
                "campaign_id": campaign.id
            }
            for campaign in sorted(campaigns, key=lambda x: x.created_at, reverse=True)[:5]
        ]

        return AnalyticsOverview(
            total_campaigns=len(campaigns),
            total_contacts=len(contacts),
            total_sequences=len(sequences),
            total_emails_sent=total_emails_sent,
            total_emails_delivered=total_emails_delivered,
            total_emails_opened=total_emails_opened,
            total_emails_clicked=total_emails_clicked,
            total_emails_replied=total_emails_replied,
            overall_open_rate=round(overall_open_rate, 2),
            overall_click_rate=round(overall_click_rate, 2),
            overall_reply_rate=round(overall_reply_rate, 2),
            active_campaigns=active_campaigns,
            recent_activity=recent_activity
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard analytics: {str(e)}"
        )


@router.get("/campaigns/{campaign_id}")
async def get_campaign_analytics(
    campaign_id: int,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get campaign analytics"""
    # TODO: Implement campaign analytics
    return {
        "campaign_id": campaign_id,
        "emails_sent": 0,
        "emails_delivered": 0,
        "emails_opened": 0,
        "emails_clicked": 0,
        "emails_replied": 0,
        "emails_bounced": 0,
        "open_rate": 0.0,
        "click_rate": 0.0,
        "reply_rate": 0.0,
        "bounce_rate": 0.0
    }


@router.get("/performance")
async def get_performance_metrics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get performance metrics over time"""
    # TODO: Implement performance metrics
    return {
        "daily_stats": [],
        "summary": {
            "total_emails": 0,
            "average_open_rate": 0.0,
            "average_click_rate": 0.0,
            "average_reply_rate": 0.0
        }
    }


@router.get("/engagement")
async def get_engagement_analytics(
    campaign_id: Optional[int] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get engagement analytics"""
    # TODO: Implement engagement analytics
    return {
        "top_performing_sequences": [],
        "engagement_by_time": [],
        "device_breakdown": {},
        "location_breakdown": {}
    }


@router.get("/reports/export")
async def export_analytics_report(
    format: str = Query("csv", regex="^(csv|xlsx|pdf)$"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    campaign_id: Optional[int] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export analytics report"""
    # TODO: Implement report export
    return {"message": f"Export {format} report - to be implemented"}
