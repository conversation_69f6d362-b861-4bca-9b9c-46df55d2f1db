"""
Authentication endpoints
"""

from datetime import timed<PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>Re<PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    create_password_hash,
    verify_password,
    create_email_verification_token,
    verify_email_verification_token,
    create_password_reset_token,
    verify_password_reset_token,
    get_current_user
)
from app.core.exceptions import AuthenticationError, ValidationError
from app.crud.user import user_crud
from app.schemas.auth_simple import (
    Token,
    UserRegister,
    UserLogin,
    PasswordReset,
    PasswordResetConfirm,
    EmailVerification
)
from app.schemas.user import UserResponse
from app.models.user import User, UserStatus
from app.core.security import get_current_user

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    
    # Check if user already exists
    existing_user = await user_crud.get_by_email(db, email=user_data.email)
    if existing_user:
        raise ValidationError("Email already registered")
    
    # Create user
    user_dict = user_data.model_dump()
    user_dict["hashed_password"] = create_password_hash(user_data.password)
    del user_dict["password"]
    del user_dict["password_confirm"]

    user = await user_crud.create(db, obj_in=user_dict)
    
    # TODO: Send email verification email
    # verification_token = create_email_verification_token(user.email)
    # await send_verification_email(user.email, verification_token)
    
    return user


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """Login user and return access token"""
    
    # Get user by email
    user = await user_crud.get_by_email(db, email=form_data.username)
    if not user:
        raise AuthenticationError("Invalid email or password")
    
    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        raise AuthenticationError("Invalid email or password")
    
    # Check if user is active
    if not user.is_active:
        raise AuthenticationError("Account is disabled")
    
    # Create tokens
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)
    
    # Update last login
    await user_crud.update_last_login(db, user_id=user.id)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
            "subscription_plan": user.subscription_plan
        }
    }


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    
    # Verify refresh token
    subject = verify_token(refresh_token, "refresh")
    if subject is None:
        raise AuthenticationError("Invalid refresh token")
    
    # Get user
    user = await user_crud.get(db, id=int(subject))
    if not user or not user.is_active:
        raise AuthenticationError("User not found or inactive")
    
    # Create new tokens
    access_token = create_access_token(subject=user.id)
    new_refresh_token = create_refresh_token(subject=user.id)
    
    return {
        "access_token": access_token,
        "refresh_token": new_refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
            "subscription_plan": user.subscription_plan
        }
    }


@router.post("/verify-email")
async def verify_email(
    verification_data: EmailVerification,
    db: AsyncSession = Depends(get_db)
):
    """Verify user email address"""
    
    # Verify token
    email = verify_email_verification_token(verification_data.token)
    if email is None:
        raise AuthenticationError("Invalid or expired verification token")
    
    # Get user
    user = await user_crud.get_by_email(db, email=email)
    if not user:
        raise AuthenticationError("User not found")
    
    # Update user verification status
    await user_crud.verify_email(db, user_id=user.id)
    
    return {"message": "Email verified successfully"}


@router.post("/forgot-password")
async def forgot_password(
    email: str,
    db: AsyncSession = Depends(get_db)
):
    """Send password reset email"""
    
    # Get user
    user = await user_crud.get_by_email(db, email=email)
    if not user:
        # Don't reveal if email exists or not
        return {"message": "If the email exists, a password reset link has been sent"}
    
    # Create reset token
    reset_token = create_password_reset_token(email)
    
    # TODO: Send password reset email
    # await send_password_reset_email(email, reset_token)
    
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
):
    """Reset user password"""
    
    # Verify token
    email = verify_password_reset_token(reset_data.token)
    if email is None:
        raise AuthenticationError("Invalid or expired reset token")
    
    # Get user
    user = await user_crud.get_by_email(db, email=email)
    if not user:
        raise AuthenticationError("User not found")
    
    # Update password
    hashed_password = create_password_hash(reset_data.new_password)
    await user_crud.update_password(db, user_id=user.id, hashed_password=hashed_password)
    
    return {"message": "Password reset successfully"}


@router.post("/resend-verification")
async def resend_verification(
    email: str,
    db: AsyncSession = Depends(get_db)
):
    """Resend email verification"""
    
    # Get user
    user = await user_crud.get_by_email(db, email=email)
    if not user:
        return {"message": "If the email exists, a verification link has been sent"}
    
    if user.is_verified:
        raise ValidationError("Email is already verified")
    
    # Create verification token
    verification_token = create_email_verification_token(email)
    
    # TODO: Send verification email
    # await send_verification_email(email, verification_token)
    
    return {"message": "If the email exists, a verification link has been sent"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    return current_user
