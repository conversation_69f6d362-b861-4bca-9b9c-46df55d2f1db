"""
Campaign management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, <PERSON>
from datetime import datetime, timedelta, timezone
import uuid

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.campaign import Campaign, CampaignStatus, CampaignType, AIPersonalizationLevel
from app.crud.campaign import campaign_crud
from app.services.ai_service import ai_service
from app.services.email_service import email_service

router = APIRouter()


# Pydantic models for campaign endpoints
class CampaignCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    campaign_type: str = Field(default="outreach")
    from_name: Optional[str] = Field(None, max_length=255)
    from_email: Optional[str] = Field(None, max_length=255)
    reply_to_email: Optional[str] = Field(None, max_length=255)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    timezone: str = Field(default="UTC", max_length=50)
    daily_limit: int = Field(default=100, ge=1, le=1000)
    hourly_limit: int = Field(default=10, ge=1, le=100)
    send_weekdays_only: bool = Field(default=True)
    send_time_start: str = Field(default="09:00")
    send_time_end: str = Field(default="17:00")
    use_ai_optimization: bool = Field(default=True)
    ai_personalization_level: str = Field(default="medium")
    ai_subject_optimization: bool = Field(default=True)
    track_opens: bool = Field(default=True)
    track_clicks: bool = Field(default=True)
    track_replies: bool = Field(default=True)
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)


class CampaignUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    from_name: Optional[str] = Field(None, max_length=255)
    from_email: Optional[str] = Field(None, max_length=255)
    reply_to_email: Optional[str] = Field(None, max_length=255)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    daily_limit: Optional[int] = Field(None, ge=1, le=1000)
    hourly_limit: Optional[int] = Field(None, ge=1, le=100)
    send_weekdays_only: Optional[bool] = None
    send_time_start: Optional[str] = None
    send_time_end: Optional[str] = None
    use_ai_optimization: Optional[bool] = None
    ai_personalization_level: Optional[str] = None
    ai_subject_optimization: Optional[bool] = None
    track_opens: Optional[bool] = None
    track_clicks: Optional[bool] = None
    track_replies: Optional[bool] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class CampaignResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    status: str
    campaign_type: str
    from_name: Optional[str]
    from_email: Optional[str]
    reply_to_email: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    timezone: str
    daily_limit: int
    hourly_limit: int
    send_weekdays_only: bool
    send_time_start: str
    send_time_end: str
    use_ai_optimization: bool
    ai_personalization_level: str
    ai_subject_optimization: bool
    track_opens: bool
    track_clicks: bool
    track_replies: bool
    total_contacts: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    emails_unsubscribed: int
    tags: List[str]
    custom_fields: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    last_sent_at: Optional[datetime]

    model_config = {"from_attributes": True}


class CampaignStats(BaseModel):
    total_contacts: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    emails_unsubscribed: int
    delivery_rate: float
    open_rate: float
    click_rate: float
    reply_rate: float
    bounce_rate: float
    unsubscribe_rate: float


class CampaignList(BaseModel):
    campaigns: List[CampaignResponse]
    total: int
    page: int
    size: int
    pages: int


@router.get("/", response_model=CampaignList)
async def get_campaigns(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user campaigns"""
    try:
        # Build query
        query = select(Campaign).where(Campaign.user_id == current_user.id)

        # Filter by status if provided
        if status:
            query = query.where(Campaign.status == status)

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # Apply pagination
        query = query.offset(skip).limit(limit).order_by(Campaign.created_at.desc())

        # Execute query
        result = await db.execute(query)
        campaigns = result.scalars().all()

        # Calculate pagination info
        pages = (total + limit - 1) // limit if total > 0 else 0
        page = skip // limit + 1

        return CampaignList(
            campaigns=campaigns,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve campaigns: {str(e)}"
        )


@router.post("/", response_model=CampaignResponse, status_code=status.HTTP_201_CREATED)
async def create_campaign(
    campaign_data: CampaignCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new campaign"""
    try:
        # Create campaign with user ID
        campaign_dict = campaign_data.model_dump()
        campaign_dict["user_id"] = current_user.id
        campaign_dict["status"] = CampaignStatus.DRAFT

        # Convert enum values
        if campaign_dict["campaign_type"] == "outreach":
            campaign_dict["campaign_type"] = CampaignType.OUTREACH
        elif campaign_dict["campaign_type"] == "nurture":
            campaign_dict["campaign_type"] = CampaignType.NURTURE
        elif campaign_dict["campaign_type"] == "follow_up":
            campaign_dict["campaign_type"] = CampaignType.FOLLOW_UP

        if campaign_dict["ai_personalization_level"] == "low":
            campaign_dict["ai_personalization_level"] = AIPersonalizationLevel.LOW
        elif campaign_dict["ai_personalization_level"] == "medium":
            campaign_dict["ai_personalization_level"] = AIPersonalizationLevel.MEDIUM
        elif campaign_dict["ai_personalization_level"] == "high":
            campaign_dict["ai_personalization_level"] = AIPersonalizationLevel.HIGH

        # Create campaign in database
        db_campaign = Campaign(**campaign_dict)
        db.add(db_campaign)
        await db.commit()
        await db.refresh(db_campaign)

        return db_campaign

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create campaign: {str(e)}"
        )


@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get campaign by ID"""
    try:
        # Query campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        return campaign

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve campaign: {str(e)}"
        )


@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: int,
    campaign_data: CampaignUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update campaign"""
    try:
        # Get existing campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Check if campaign can be updated
        if campaign.status in [CampaignStatus.RUNNING, CampaignStatus.COMPLETED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot update running or completed campaigns"
            )

        # Update campaign fields
        update_data = campaign_data.model_dump(exclude_unset=True)

        # Convert enum values if provided
        if "campaign_type" in update_data:
            if update_data["campaign_type"] == "outreach":
                update_data["campaign_type"] = CampaignType.OUTREACH
            elif update_data["campaign_type"] == "nurture":
                update_data["campaign_type"] = CampaignType.NURTURE
            elif update_data["campaign_type"] == "follow_up":
                update_data["campaign_type"] = CampaignType.FOLLOW_UP

        if "ai_personalization_level" in update_data:
            if update_data["ai_personalization_level"] == "low":
                update_data["ai_personalization_level"] = AIPersonalizationLevel.LOW
            elif update_data["ai_personalization_level"] == "medium":
                update_data["ai_personalization_level"] = AIPersonalizationLevel.MEDIUM
            elif update_data["ai_personalization_level"] == "high":
                update_data["ai_personalization_level"] = AIPersonalizationLevel.HIGH

        # Apply updates
        for field, value in update_data.items():
            setattr(campaign, field, value)

        campaign.updated_at = datetime.now(timezone.utc)

        await db.commit()
        await db.refresh(campaign)

        return campaign

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update campaign: {str(e)}"
        )


@router.delete("/{campaign_id}")
async def delete_campaign(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete campaign"""
    try:
        # Get existing campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Check if campaign can be deleted
        if campaign.status == CampaignStatus.RUNNING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete running campaign. Pause it first."
            )

        # Delete campaign
        await db.delete(campaign)
        await db.commit()

        return {"message": "Campaign deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete campaign: {str(e)}"
        )


@router.post("/{campaign_id}/start")
async def start_campaign(
    campaign_id: int,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Start campaign"""
    try:
        # Get existing campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Check if campaign can be started
        if campaign.status not in [CampaignStatus.DRAFT, CampaignStatus.PAUSED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign can only be started from draft or paused status"
            )

        # Validate campaign has required data
        if campaign.total_contacts == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign must have contacts before starting"
            )

        # Update campaign status
        campaign.status = CampaignStatus.RUNNING
        campaign.updated_at = datetime.now(timezone.utc)

        await db.commit()

        # TODO: Add background task to process campaign emails
        # background_tasks.add_task(process_campaign_emails, campaign_id)

        return {
            "message": "Campaign started successfully",
            "campaign_id": campaign_id,
            "status": "running"
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start campaign: {str(e)}"
        )


@router.post("/{campaign_id}/pause")
async def pause_campaign(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Pause campaign"""
    try:
        # Get existing campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Check if campaign can be paused
        if campaign.status != CampaignStatus.RUNNING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only running campaigns can be paused"
            )

        # Update campaign status
        campaign.status = CampaignStatus.PAUSED
        campaign.updated_at = datetime.now(timezone.utc)

        await db.commit()

        return {
            "message": "Campaign paused successfully",
            "campaign_id": campaign_id,
            "status": "paused"
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to pause campaign: {str(e)}"
        )


@router.get("/{campaign_id}/stats", response_model=CampaignStats)
async def get_campaign_stats(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get campaign statistics"""
    try:
        # Get existing campaign
        query = select(Campaign).where(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        )
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Calculate rates
        delivery_rate = (campaign.emails_delivered / campaign.emails_sent * 100) if campaign.emails_sent > 0 else 0
        open_rate = (campaign.emails_opened / campaign.emails_delivered * 100) if campaign.emails_delivered > 0 else 0
        click_rate = (campaign.emails_clicked / campaign.emails_opened * 100) if campaign.emails_opened > 0 else 0
        reply_rate = (campaign.emails_replied / campaign.emails_delivered * 100) if campaign.emails_delivered > 0 else 0
        bounce_rate = (campaign.emails_bounced / campaign.emails_sent * 100) if campaign.emails_sent > 0 else 0
        unsubscribe_rate = (campaign.emails_unsubscribed / campaign.emails_delivered * 100) if campaign.emails_delivered > 0 else 0

        return CampaignStats(
            total_contacts=campaign.total_contacts,
            emails_sent=campaign.emails_sent,
            emails_delivered=campaign.emails_delivered,
            emails_opened=campaign.emails_opened,
            emails_clicked=campaign.emails_clicked,
            emails_replied=campaign.emails_replied,
            emails_bounced=campaign.emails_bounced,
            emails_unsubscribed=campaign.emails_unsubscribed,
            delivery_rate=round(delivery_rate, 2),
            open_rate=round(open_rate, 2),
            click_rate=round(click_rate, 2),
            reply_rate=round(reply_rate, 2),
            bounce_rate=round(bounce_rate, 2),
            unsubscribe_rate=round(unsubscribe_rate, 2)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve campaign stats: {str(e)}"
        )
