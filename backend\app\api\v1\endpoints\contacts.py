"""
Contact management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel
import csv
import io
import base64

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.contact import Contact, ContactStatus, ContactSource
from app.crud.contact import contact_crud
from app.schemas.contact import (
    ContactCreate, ContactUpdate, ContactBulkCreate, ContactBulkUpdate,
    ContactImport, ContactStats, ContactSearchResult, ContactExport,
    ContactEngagementUpdate
)

router = APIRouter()


# Pydantic models for responses
class ContactResponse(ContactCreate):
    id: int
    user_id: int
    status: str
    source: str

    # Engagement data
    last_opened_at: Optional[datetime]
    last_clicked_at: Optional[datetime]
    last_replied_at: Optional[datetime]
    total_opens: int
    total_clicks: int
    total_replies: int

    # Timestamps
    created_at: datetime
    updated_at: datetime
    unsubscribed_at: Optional[datetime]

    model_config = {"from_attributes": True}


class ContactList(BaseModel):
    contacts: List[ContactResponse]
    total: int
    page: int
    size: int
    pages: int


@router.get("/", response_model=ContactList)
async def get_contacts(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user contacts with filtering and pagination"""
    try:
        # Get contacts
        contacts = await contact_crud.get_by_user(
            db,
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            status=status,
            search=search
        )

        # Get total count
        total = await contact_crud.count_by_user(
            db,
            user_id=current_user.id,
            status=status,
            search=search
        )

        # Calculate pagination info
        pages = (total + limit - 1) // limit if total > 0 else 0
        page = skip // limit + 1

        return ContactList(
            contacts=contacts,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve contacts: {str(e)}"
        )


@router.post("/", response_model=ContactResponse, status_code=status.HTTP_201_CREATED)
async def create_contact(
    contact_data: ContactCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new contact"""
    try:
        # Check if contact already exists
        existing_contact = await contact_crud.get_by_email(
            db, user_id=current_user.id, email=contact_data.email
        )

        if existing_contact:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Contact with this email already exists"
            )

        # Create contact
        contact_dict = contact_data.model_dump()
        contact_dict["user_id"] = current_user.id
        contact_dict["status"] = ContactStatus.ACTIVE

        contact = Contact(**contact_dict)
        db.add(contact)
        await db.commit()
        await db.refresh(contact)

        return contact

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create contact: {str(e)}"
        )


@router.get("/stats")
async def get_contact_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get contact statistics"""
    try:
        stats = await contact_crud.get_contact_stats(db, user_id=current_user.id)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve contact stats: {str(e)}"
        )


@router.get("/search")
async def search_contacts(
    q: str = Query(..., min_length=1),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search contacts"""
    try:
        contacts = await contact_crud.search_contacts(
            db, user_id=current_user.id, query=q, skip=skip, limit=limit
        )

        total = await contact_crud.count_by_user(
            db, user_id=current_user.id, search=q
        )

        return {
            "contacts": contacts,
            "total": total,
            "query": q,
            "suggestions": []
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search contacts: {str(e)}"
        )


@router.post("/import")
async def import_contacts(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Import contacts from CSV file"""
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV files are supported"
            )

        # Read file content
        content = await file.read()
        csv_content = content.decode('utf-8')

        # Parse CSV
        import csv
        import io

        csv_reader = csv.DictReader(io.StringIO(csv_content))

        created_contacts = []
        skipped_contacts = []
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 for header row
            try:
                # Extract email (required field)
                email = row.get('email', '').strip().lower()
                if not email:
                    errors.append(f"Row {row_num}: Email is required")
                    continue

                # Check if contact already exists
                existing_contact = await contact_crud.get_by_email_and_user(
                    db, email=email, user_id=current_user.id
                )

                if existing_contact:
                    skipped_contacts.append(email)
                    continue

                # Create contact data
                contact_data = ContactCreate(
                    email=email,
                    first_name=row.get('first_name', '').strip(),
                    last_name=row.get('last_name', '').strip(),
                    company=row.get('company', '').strip(),
                    job_title=row.get('position', row.get('job_title', '')).strip(),
                    phone=row.get('phone', '').strip(),
                    source=ContactSource.CSV_IMPORT
                )

                # Create contact
                contact = await contact_crud.create_with_user(
                    db, obj_in=contact_data, user_id=current_user.id
                )
                created_contacts.append(contact)

            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")
                continue

        return {
            "imported": len(created_contacts),
            "skipped": len(skipped_contacts),
            "errors": errors,
            "total_processed": len(created_contacts) + len(skipped_contacts) + len(errors),
            "contacts": created_contacts
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import contacts: {str(e)}"
        )


@router.get("/{contact_id}", response_model=ContactResponse)
async def get_contact(
    contact_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get contact by ID"""
    try:
        contact = await contact_crud.get_by_user_and_id(
            db, user_id=current_user.id, contact_id=contact_id
        )

        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )

        return contact

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve contact: {str(e)}"
        )


@router.put("/{contact_id}", response_model=ContactResponse)
async def update_contact(
    contact_id: int,
    contact_data: ContactUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update contact"""
    try:
        # Get existing contact
        contact = await contact_crud.get_by_user_and_id(
            db, user_id=current_user.id, contact_id=contact_id
        )

        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )

        # Update contact fields
        update_data = contact_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(contact, field, value)

        contact.updated_at = datetime.now(timezone.utc)

        await db.commit()
        await db.refresh(contact)

        return contact

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update contact: {str(e)}"
        )


@router.delete("/{contact_id}")
async def delete_contact(
    contact_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete contact"""
    try:
        # Get existing contact
        contact = await contact_crud.get_by_user_and_id(
            db, user_id=current_user.id, contact_id=contact_id
        )

        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )

        # Delete contact
        await db.delete(contact)
        await db.commit()

        return {"message": "Contact deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete contact: {str(e)}"
        )


@router.post("/bulk")
async def bulk_create_contacts(
    contacts_data: ContactBulkCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Bulk create contacts"""
    try:
        created_contacts = []
        skipped_contacts = []

        for contact_data in contacts_data.contacts:
            # Check if contact already exists
            existing_contact = await contact_crud.get_by_email(
                db, user_id=current_user.id, email=contact_data.email
            )

            if existing_contact:
                if contacts_data.skip_duplicates:
                    skipped_contacts.append(contact_data.email)
                    continue
                elif contacts_data.update_existing:
                    # Update existing contact
                    update_data = contact_data.model_dump(exclude_unset=True)
                    for field, value in update_data.items():
                        if field != "email":  # Don't update email
                            setattr(existing_contact, field, value)
                    existing_contact.updated_at = datetime.now(timezone.utc)
                    created_contacts.append(existing_contact)
                    continue

            # Create new contact
            contact_dict = contact_data.model_dump()
            contact_dict["user_id"] = current_user.id
            contact_dict["status"] = ContactStatus.ACTIVE

            contact = Contact(**contact_dict)
            db.add(contact)
            created_contacts.append(contact)

        await db.commit()

        # Refresh all contacts
        for contact in created_contacts:
            await db.refresh(contact)

        return {
            "created": len(created_contacts),
            "skipped": len(skipped_contacts),
            "total_processed": len(contacts_data.contacts),
            "contacts": created_contacts,
            "skipped_emails": skipped_contacts
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk create contacts: {str(e)}"
        )
