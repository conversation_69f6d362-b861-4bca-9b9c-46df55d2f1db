"""
Email sending endpoints for campaigns and sequences
"""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.crud.campaign import campaign_crud
from app.crud.sequence import sequence_crud
from app.crud.contact import contact_crud
from app.services.email_service import email_service

router = APIRouter()


class TestEmailRequest(BaseModel):
    """Request schema for test emails"""
    to_email: EmailStr
    sequence_id: Optional[int] = None
    subject: Optional[str] = None
    content: Optional[str] = None
    personalization_data: Optional[Dict[str, Any]] = None


class CampaignSendRequest(BaseModel):
    """Request schema for sending campaign emails"""
    contact_ids: Optional[List[int]] = None  # If None, send to all active contacts
    sequence_id: Optional[int] = None  # If None, send first sequence
    batch_size: int = 10
    test_mode: bool = False


class BulkEmailRequest(BaseModel):
    """Request schema for bulk email sending"""
    emails: List[Dict[str, Any]]
    batch_size: int = 10


@router.post("/test")
async def send_test_email(
    request: TestEmailRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send a test email"""
    try:
        if request.sequence_id:
            # Send test email for a specific sequence
            sequence = await sequence_crud.get_by_user_and_id(
                db, user_id=current_user.id, sequence_id=request.sequence_id
            )
            
            if not sequence:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Sequence not found"
                )
            
            # Get campaign for from details
            campaign = await campaign_crud.get(db, id=sequence.campaign_id)
            
            if not campaign:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Campaign not found"
                )
            
            # Prepare personalization data
            personalization_data = request.personalization_data or {
                "first_name": "Test",
                "last_name": "User",
                "company": "Test Company",
                "from_name": campaign.from_name
            }
            
            # Apply personalization
            subject = email_service._apply_personalization(sequence.subject, personalization_data)
            content = email_service._apply_personalization(sequence.content, personalization_data)
            
            result = await email_service.send_test_email(
                to_email=request.to_email,
                subject=subject,
                content=content,
                from_email=campaign.from_email,
                from_name=campaign.from_name
            )
        else:
            # Send custom test email
            if not request.subject or not request.content:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Subject and content are required for custom test emails"
                )
            
            result = await email_service.send_test_email(
                to_email=request.to_email,
                subject=request.subject,
                content=request.content
            )
        
        return {
            "message": "Test email sent successfully",
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send test email: {str(e)}"
        )


@router.post("/campaigns/{campaign_id}/send")
async def send_campaign_emails(
    campaign_id: int,
    request: CampaignSendRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send emails for a campaign"""
    try:
        # Verify campaign ownership
        campaign = await campaign_crud.get_by_user_and_id(
            db, user_id=current_user.id, campaign_id=campaign_id
        )
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Get sequence to send
        if request.sequence_id:
            sequence = await sequence_crud.get_by_user_and_id(
                db, user_id=current_user.id, sequence_id=request.sequence_id
            )
            
            if not sequence or sequence.campaign_id != campaign_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Sequence not found or doesn't belong to campaign"
                )
        else:
            # Get first active sequence
            sequences = await sequence_crud.get_by_campaign(
                db, campaign_id=campaign_id, status="active", limit=1
            )
            
            if not sequences:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No active sequences found in campaign"
                )
            
            sequence = sequences[0]
        
        # Get contacts to send to
        if request.contact_ids:
            # Verify contact ownership and get contacts
            contacts = []
            for contact_id in request.contact_ids:
                contact = await contact_crud.get_by_user_and_id(
                    db, user_id=current_user.id, contact_id=contact_id
                )
                if contact:
                    contacts.append(contact)
            
            contact_ids = [c.id for c in contacts]
        else:
            # Get all active contacts for the user
            contacts = await contact_crud.get_contacts_for_campaign(
                db, user_id=current_user.id, campaign_id=campaign_id
            )
            contact_ids = [c.id for c in contacts]
        
        if not contact_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No contacts found to send emails to"
            )
        
        if request.test_mode:
            # Send to first 3 contacts only in test mode
            contact_ids = contact_ids[:3]
        
        # Send emails in background
        background_tasks.add_task(
            email_service.send_campaign_batch,
            db, campaign_id, sequence.id, contact_ids, request.batch_size
        )
        
        return {
            "message": f"Email sending started for {len(contact_ids)} contacts",
            "campaign_id": campaign_id,
            "sequence_id": sequence.id,
            "contact_count": len(contact_ids),
            "test_mode": request.test_mode
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start email sending: {str(e)}"
        )


@router.post("/sequences/{sequence_id}/send")
async def send_sequence_emails(
    sequence_id: int,
    request: CampaignSendRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send emails for a specific sequence"""
    try:
        # Verify sequence ownership
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )
        
        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )
        
        # Get contacts to send to
        if request.contact_ids:
            contact_ids = request.contact_ids
        else:
            # Get all active contacts for the campaign
            contacts = await contact_crud.get_contacts_for_campaign(
                db, user_id=current_user.id, campaign_id=sequence.campaign_id
            )
            contact_ids = [c.id for c in contacts]
        
        if not contact_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No contacts found to send emails to"
            )
        
        if request.test_mode:
            # Send to first 3 contacts only in test mode
            contact_ids = contact_ids[:3]
        
        # Send emails in background
        background_tasks.add_task(
            email_service.send_campaign_batch,
            db, sequence.campaign_id, sequence_id, contact_ids, request.batch_size
        )
        
        return {
            "message": f"Sequence email sending started for {len(contact_ids)} contacts",
            "sequence_id": sequence_id,
            "campaign_id": sequence.campaign_id,
            "contact_count": len(contact_ids),
            "test_mode": request.test_mode
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start sequence email sending: {str(e)}"
        )


@router.post("/bulk")
async def send_bulk_emails(
    request: BulkEmailRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send bulk emails"""
    try:
        # Validate email data
        for email_data in request.emails:
            if not email_data.get("to_email") or not email_data.get("subject") or not email_data.get("content"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Each email must have to_email, subject, and content"
                )
        
        # Send emails in background
        background_tasks.add_task(
            email_service.send_bulk_emails,
            request.emails, request.batch_size
        )
        
        return {
            "message": f"Bulk email sending started for {len(request.emails)} emails",
            "email_count": len(request.emails),
            "batch_size": request.batch_size
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start bulk email sending: {str(e)}"
        )


@router.get("/status/{campaign_id}")
async def get_sending_status(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email sending status for a campaign"""
    try:
        # Verify campaign ownership
        campaign = await campaign_crud.get_by_user_and_id(
            db, user_id=current_user.id, campaign_id=campaign_id
        )
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Get campaign statistics
        stats = await campaign_crud.get_campaign_stats(db, campaign_id=campaign_id)
        
        return {
            "campaign_id": campaign_id,
            "status": campaign.status,
            "statistics": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sending status: {str(e)}"
        )
