"""
Email sending and management endpoints
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr
import uuid
from datetime import datetime

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.services.email_service import email_service
from app.services.ai_service import ai_service

router = APIRouter()


# Pydantic models for email endpoints
class EmailSendRequest(BaseModel):
    to_email: EmailStr
    subject: str
    content: str
    content_html: Optional[str] = None
    from_email: Optional[str] = None
    from_name: Optional[str] = None
    reply_to: Optional[str] = None
    tracking_enabled: bool = True


class BulkEmailRequest(BaseModel):
    emails: List[EmailSendRequest]
    batch_size: int = 10


class TemplateEmailRequest(BaseModel):
    template_name: str
    to_email: EmailStr
    template_data: Dict[str, Any]
    from_email: Optional[str] = None
    from_name: Optional[str] = None


class EmailTestRequest(BaseModel):
    to_email: EmailStr
    subject: str
    content: str
    content_html: Optional[str] = None


@router.post("/send")
async def send_email(
    request: EmailSendRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send a single email"""
    try:
        # Generate unique message ID for tracking
        message_id = f"email_{uuid.uuid4().hex}_{int(datetime.now().timestamp())}"
        
        # Send email
        result = await email_service.send_email(
            to_email=request.to_email,
            subject=request.subject,
            content=request.content,
            content_html=request.content_html,
            from_email=request.from_email,
            from_name=request.from_name,
            reply_to=request.reply_to,
            tracking_pixel=request.tracking_enabled,
            message_id=message_id
        )
        
        # TODO: Log email to database
        # background_tasks.add_task(log_email_to_database, result, current_user.id)
        
        return {
            "status": "sent",
            "message_id": result["message_id"],
            "to_email": result["to_email"],
            "subject": result["subject"],
            "sent_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send email: {str(e)}"
        )


@router.post("/send-bulk")
async def send_bulk_emails(
    request: BulkEmailRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send multiple emails in batches"""
    try:
        # Convert to email service format
        email_data = []
        for email in request.emails:
            message_id = f"email_{uuid.uuid4().hex}_{int(datetime.now().timestamp())}"
            email_data.append({
                "to_email": email.to_email,
                "subject": email.subject,
                "content": email.content,
                "content_html": email.content_html,
                "from_email": email.from_email,
                "from_name": email.from_name,
                "reply_to": email.reply_to,
                "tracking_pixel": email.tracking_enabled,
                "message_id": message_id
            })
        
        # Send emails in batches
        results = await email_service.send_bulk_emails(
            emails=email_data,
            batch_size=request.batch_size
        )
        
        # Count successes and failures
        sent_count = sum(1 for r in results if r.get("status") == "sent")
        failed_count = len(results) - sent_count
        
        return {
            "total_emails": len(request.emails),
            "sent_count": sent_count,
            "failed_count": failed_count,
            "results": results,
            "batch_size": request.batch_size
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send bulk emails: {str(e)}"
        )


@router.post("/send-template")
async def send_template_email(
    request: TemplateEmailRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send email using a predefined template"""
    try:
        result = await email_service.send_template_email(
            template_name=request.template_name,
            to_email=request.to_email,
            template_data=request.template_data,
            from_email=request.from_email,
            from_name=request.from_name
        )
        
        return {
            "status": "sent",
            "template_name": request.template_name,
            "message_id": result["message_id"],
            "to_email": result["to_email"],
            "sent_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send template email: {str(e)}"
        )


@router.post("/test")
async def send_test_email(
    request: EmailTestRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send a test email (no tracking, no logging)"""
    try:
        result = await email_service.send_email(
            to_email=request.to_email,
            subject=f"[TEST] {request.subject}",
            content=request.content,
            content_html=request.content_html,
            tracking_pixel=False
        )
        
        return {
            "status": "test_sent",
            "to_email": result["to_email"],
            "subject": result["subject"],
            "message": "Test email sent successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send test email: {str(e)}"
        )


@router.get("/templates")
async def get_email_templates(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available email templates"""
    templates = [
        {
            "name": "welcome",
            "display_name": "Welcome Email",
            "description": "Welcome new users to the platform",
            "required_fields": ["name"],
            "optional_fields": ["company", "website"]
        },
        {
            "name": "password_reset",
            "display_name": "Password Reset",
            "description": "Password reset instructions",
            "required_fields": ["reset_link"],
            "optional_fields": []
        }
    ]
    
    return {
        "templates": templates,
        "total": len(templates)
    }


@router.get("/status/{message_id}")
async def get_email_status(
    message_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email delivery status"""
    # TODO: Implement email status tracking from database
    return {
        "message_id": message_id,
        "status": "sent",
        "sent_at": datetime.now().isoformat(),
        "delivered_at": None,
        "opened_at": None,
        "clicked_at": None,
        "bounced_at": None,
        "tracking_events": []
    }


@router.get("/stats")
async def get_email_stats(
    days: int = 30,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email sending statistics"""
    # TODO: Implement real statistics from database
    return {
        "period_days": days,
        "total_sent": 0,
        "total_delivered": 0,
        "total_opened": 0,
        "total_clicked": 0,
        "total_bounced": 0,
        "delivery_rate": 0.0,
        "open_rate": 0.0,
        "click_rate": 0.0,
        "bounce_rate": 0.0
    }
