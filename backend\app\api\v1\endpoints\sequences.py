"""
Email sequence management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.sequence import EmailSequence, SequenceStatus
from app.models.campaign import Campaign
from app.crud.sequence import sequence_crud
from app.crud.campaign import campaign_crud
from app.schemas.sequence import (
    EmailSequenceCreate, EmailSequenceUpdate, EmailSequenceReorder,
    EmailSequenceDuplicate, EmailSequenceList, EmailSequenceStats,
    EmailSequenceBulkAction
)

router = APIRouter()


# Pydantic models for responses
class EmailSequenceResponse(BaseModel):
    id: int
    campaign_id: int
    name: str
    subject_line: str
    email_content: str
    order: int
    delay_days: int
    delay_hours: int
    status: str

    # Statistics
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int

    # Timestamps
    created_at: datetime
    updated_at: datetime
    last_sent_at: Optional[datetime]

    @classmethod
    def from_model(cls, sequence: EmailSequence):
        """Create response from model with field mapping"""
        return cls(
            id=sequence.id,
            campaign_id=sequence.campaign_id,
            name=sequence.name,
            subject_line=sequence.subject,
            email_content=sequence.content,
            order=sequence.order,
            delay_days=sequence.delay_days,
            delay_hours=sequence.delay_hours,
            status=sequence.status.value if hasattr(sequence.status, 'value') else str(sequence.status),
            emails_sent=sequence.emails_sent,
            emails_delivered=sequence.emails_delivered,
            emails_opened=sequence.emails_opened,
            emails_clicked=sequence.emails_clicked,
            emails_replied=sequence.emails_replied,
            emails_bounced=sequence.emails_bounced,
            created_at=sequence.created_at,
            updated_at=sequence.updated_at,
            last_sent_at=sequence.last_sent_at
        )


class EmailSequenceList(BaseModel):
    sequences: List[EmailSequenceResponse]
    total: int
    page: int
    size: int
    pages: int


@router.get("/", response_model=EmailSequenceList)
async def get_sequences(
    campaign_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user sequences with filtering and pagination"""
    try:
        # Get sequences
        sequences = await sequence_crud.get_by_user(
            db,
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            campaign_id=campaign_id,
            status=status
        )

        # Get total count (simplified for now)
        total = len(sequences)  # TODO: Implement proper count

        # Calculate pagination info
        pages = (total + limit - 1) // limit if total > 0 else 0
        page = skip // limit + 1

        return EmailSequenceList(
            sequences=[EmailSequenceResponse.from_model(seq) for seq in sequences],
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sequences: {str(e)}"
        )


@router.post("/", response_model=EmailSequenceResponse, status_code=status.HTTP_201_CREATED)
async def create_sequence(
    sequence_data: EmailSequenceCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new email sequence"""
    try:
        # Verify campaign ownership
        campaign = await campaign_crud.get_by_user_and_id(
            db, user_id=current_user.id, campaign_id=sequence_data.campaign_id
        )

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )

        # Get next order number if not provided
        if hasattr(sequence_data, 'order') and sequence_data.order is not None:
            next_order = sequence_data.order
        else:
            next_order = await sequence_crud.get_next_order(
                db, campaign_id=sequence_data.campaign_id
            )

        # Create sequence with only valid model fields
        sequence_dict = {
            "campaign_id": sequence_data.campaign_id,
            "name": sequence_data.name,
            "subject": sequence_data.subject_line,
            "content": sequence_data.email_content,
            "order": next_order,
            "delay_days": sequence_data.delay_days,
            "delay_hours": sequence_data.delay_hours,
            "status": SequenceStatus.DRAFT,
            "use_ai_subject": sequence_data.ai_optimization_enabled,
            "use_ai_content": sequence_data.ai_optimization_enabled
        }

        sequence = EmailSequence(**sequence_dict)
        db.add(sequence)
        await db.commit()
        await db.refresh(sequence)

        return EmailSequenceResponse.from_model(sequence)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create sequence: {str(e)}"
        )


@router.get("/{sequence_id}", response_model=EmailSequenceResponse)
async def get_sequence(
    sequence_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get sequence by ID"""
    try:
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        return EmailSequenceResponse.from_model(sequence)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sequence: {str(e)}"
        )


@router.put("/{sequence_id}", response_model=EmailSequenceResponse)
async def update_sequence(
    sequence_id: int,
    sequence_data: EmailSequenceUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update sequence"""
    try:
        # Get existing sequence
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # Update sequence fields
        update_data = sequence_data.model_dump(exclude_unset=True)

        # Map schema fields to model fields
        if "subject_line" in update_data:
            update_data["subject"] = update_data.pop("subject_line")
        if "email_content" in update_data:
            update_data["content"] = update_data.pop("email_content")

        for field, value in update_data.items():
            setattr(sequence, field, value)

        sequence.updated_at = datetime.now(timezone.utc)

        await db.commit()
        await db.refresh(sequence)

        return EmailSequenceResponse.from_model(sequence)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update sequence: {str(e)}"
        )


@router.delete("/{sequence_id}")
async def delete_sequence(
    sequence_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete sequence"""
    try:
        # Get existing sequence
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # Delete sequence
        await db.delete(sequence)
        await db.commit()

        return {"message": "Sequence deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete sequence: {str(e)}"
        )


@router.get("/{sequence_id}/stats")
async def get_sequence_stats(
    sequence_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get sequence statistics"""
    try:
        # Verify sequence ownership
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # Get performance metrics
        stats = await sequence_crud.get_sequence_performance(db, sequence_id=sequence_id)

        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sequence stats: {str(e)}"
        )


@router.post("/{sequence_id}/activate")
async def activate_sequence(
    sequence_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Activate sequence"""
    try:
        # Get existing sequence
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # Update status to active
        sequence.status = SequenceStatus.ACTIVE
        sequence.updated_at = datetime.now(timezone.utc)

        await db.commit()
        await db.refresh(sequence)

        return {"message": "Sequence activated successfully", "sequence": sequence}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to activate sequence: {str(e)}"
        )


@router.post("/{sequence_id}/deactivate")
async def deactivate_sequence(
    sequence_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Deactivate sequence"""
    try:
        # Get existing sequence
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # Update status to inactive
        sequence.status = SequenceStatus.INACTIVE
        sequence.updated_at = datetime.now(timezone.utc)

        await db.commit()
        await db.refresh(sequence)

        return {"message": "Sequence deactivated successfully", "sequence": sequence}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to deactivate sequence: {str(e)}"
        )


@router.post("/{sequence_id}/duplicate")
async def duplicate_sequence(
    sequence_id: int,
    duplicate_data: EmailSequenceDuplicate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Duplicate sequence"""
    try:
        # Verify sequence ownership
        sequence = await sequence_crud.get_by_user_and_id(
            db, user_id=current_user.id, sequence_id=sequence_id
        )

        if not sequence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sequence not found"
            )

        # If new campaign specified, verify ownership
        if duplicate_data.new_campaign_id:
            campaign = await campaign_crud.get_by_user_and_id(
                db, user_id=current_user.id, campaign_id=duplicate_data.new_campaign_id
            )

            if not campaign:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Target campaign not found"
                )

        # Duplicate sequence
        duplicated = await sequence_crud.duplicate_sequence(
            db,
            sequence_id=sequence_id,
            new_campaign_id=duplicate_data.new_campaign_id
        )

        # Update name if provided
        if duplicate_data.new_name:
            duplicated.name = duplicate_data.new_name
            await db.commit()
            await db.refresh(duplicated)

        return {"message": "Sequence duplicated successfully", "sequence": duplicated}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to duplicate sequence: {str(e)}"
        )
