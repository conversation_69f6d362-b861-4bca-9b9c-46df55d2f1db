"""
Database utilities for Supabase PostgreSQL connections
"""

import logging
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from app.core.config import settings

logger = logging.getLogger(__name__)


async def test_supabase_connection(database_url: str = None) -> bool:
    """Test Supabase PostgreSQL connection"""
    url = database_url or settings.DATABASE_URL

    try:
        engine = create_async_engine(url, echo=False)

        async with engine.begin() as conn:
            # Test PostgreSQL connection
            result = await conn.execute(text("SELECT version()"))
            version = result.scalar()
            logger.info(f"Supabase connection successful. PostgreSQL version: {version}")

        await engine.dispose()
        return True

    except Exception as e:
        logger.error(f"Supabase connection failed: {e}")
        return False


async def ensure_database_schema():
    """Ensure database schema exists"""
    from app.core.database import engine, Base
    from app.models import user, campaign, contact, sequence, analytics  # noqa
    
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database schema created/verified successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create database schema: {e}")
        return False


class SupabaseDatabaseManager:
    """Manages Supabase PostgreSQL database connections and operations"""

    def __init__(self):
        self.database_url = settings.DATABASE_URL
        self.supabase_url = settings.SUPABASE_URL
        self.anon_key = settings.SUPABASE_ANON_KEY

    async def health_check(self) -> dict:
        """Perform Supabase database health check"""
        try:
            is_healthy = await test_supabase_connection(self.database_url)

            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "database_type": "postgresql",
                "provider": "supabase",
                "url": self.database_url.split("@")[0] + "@***" if "@" in self.database_url else self.database_url,
                "supabase_configured": bool(self.supabase_url and self.anon_key),
                "project_url": self.supabase_url
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "database_type": "postgresql",
                "provider": "supabase"
            }

    async def test_connection(self) -> bool:
        """Test Supabase connection"""
        return await test_supabase_connection(self.database_url)

    async def verify_schema(self) -> bool:
        """Verify database schema exists"""
        return await ensure_database_schema()


# Global Supabase database manager instance
db_manager = SupabaseDatabaseManager()
