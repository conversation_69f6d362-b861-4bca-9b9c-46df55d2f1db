"""
Base CRUD operations
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload

from app.core.database import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        
        **Parameters**
        
        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """Get a single record by ID"""
        result = await db.execute(select(self.model).where(self.model.id == id))
        return result.scalar_one_or_none()

    async def get_multi(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        order_by: str = "id",
        order_desc: bool = False
    ) -> List[ModelType]:
        """Get multiple records with pagination"""
        query = select(self.model)
        
        # Add ordering
        if hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(order_column.desc())
            else:
                query = query.order_by(order_column)
        
        # Add pagination
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """Create a new record"""
        obj_in_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """Update an existing record"""
        obj_data = db_obj.__dict__.copy()
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: int) -> ModelType:
        """Delete a record by ID"""
        obj = await self.get(db, id=id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj

    async def count(self, db: AsyncSession) -> int:
        """Count total records"""
        result = await db.execute(select(func.count(self.model.id)))
        return result.scalar()

    async def exists(self, db: AsyncSession, *, id: int) -> bool:
        """Check if record exists by ID"""
        result = await db.execute(
            select(self.model.id).where(self.model.id == id)
        )
        return result.scalar_one_or_none() is not None

    async def get_by_field(
        self, 
        db: AsyncSession, 
        *, 
        field: str, 
        value: Any
    ) -> Optional[ModelType]:
        """Get a record by a specific field"""
        if hasattr(self.model, field):
            column = getattr(self.model, field)
            result = await db.execute(select(self.model).where(column == value))
            return result.scalar_one_or_none()
        return None

    async def get_multi_by_field(
        self, 
        db: AsyncSession, 
        *, 
        field: str, 
        value: Any,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """Get multiple records by a specific field"""
        if hasattr(self.model, field):
            column = getattr(self.model, field)
            query = select(self.model).where(column == value)
            query = query.offset(skip).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        return []

    async def search(
        self,
        db: AsyncSession,
        *,
        query: str,
        fields: List[str],
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """Search records by text in specified fields"""
        search_query = select(self.model)
        
        # Build search conditions
        conditions = []
        for field in fields:
            if hasattr(self.model, field):
                column = getattr(self.model, field)
                if hasattr(column.type, 'python_type') and column.type.python_type == str:
                    conditions.append(column.ilike(f"%{query}%"))
        
        if conditions:
            from sqlalchemy import or_
            search_query = search_query.where(or_(*conditions))
        
        search_query = search_query.offset(skip).limit(limit)
        result = await db.execute(search_query)
        return result.scalars().all()

    async def bulk_create(
        self, 
        db: AsyncSession, 
        *, 
        objs_in: List[CreateSchemaType]
    ) -> List[ModelType]:
        """Create multiple records in bulk"""
        db_objs = []
        for obj_in in objs_in:
            obj_in_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in
            db_obj = self.model(**obj_in_data)
            db_objs.append(db_obj)
        
        db.add_all(db_objs)
        await db.commit()
        
        for db_obj in db_objs:
            await db.refresh(db_obj)
        
        return db_objs

    async def bulk_update(
        self,
        db: AsyncSession,
        *,
        updates: List[Dict[str, Any]]
    ) -> int:
        """Update multiple records in bulk"""
        if not updates:
            return 0
        
        stmt = update(self.model)
        result = await db.execute(stmt, updates)
        await db.commit()
        return result.rowcount

    async def bulk_delete(
        self,
        db: AsyncSession,
        *,
        ids: List[int]
    ) -> int:
        """Delete multiple records by IDs"""
        if not ids:
            return 0
        
        stmt = delete(self.model).where(self.model.id.in_(ids))
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount
