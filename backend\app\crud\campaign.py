"""
CRUD operations for campaigns
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.campaign import Campaign, CampaignStatus
from app.schemas.campaign import CampaignC<PERSON>, CampaignUpdate


class CRUDCampaign(CRUDBase[Campaign, CampaignCreate, CampaignUpdate]):
    """CRUD operations for campaigns"""
    
    async def get_by_user(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Campaign]:
        """Get campaigns by user ID with optional status filter"""
        query = select(self.model).where(self.model.user_id == user_id)
        
        if status:
            query = query.where(self.model.status == status)
        
        query = query.offset(skip).limit(limit).order_by(self.model.created_at.desc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_user_and_id(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        campaign_id: int
    ) -> Optional[Campaign]:
        """Get campaign by user ID and campaign ID"""
        query = select(self.model).where(
            self.model.user_id == user_id,
            self.model.id == campaign_id
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def count_by_user(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int,
        status: Optional[str] = None
    ) -> int:
        """Count campaigns by user ID with optional status filter"""
        query = select(func.count()).select_from(self.model).where(self.model.user_id == user_id)
        
        if status:
            query = query.where(self.model.status == status)
        
        result = await db.execute(query)
        return result.scalar()
    
    async def get_active_campaigns(
        self, 
        db: AsyncSession, 
        *, 
        user_id: Optional[int] = None
    ) -> List[Campaign]:
        """Get all active (running) campaigns"""
        query = select(self.model).where(self.model.status == CampaignStatus.RUNNING)
        
        if user_id:
            query = query.where(self.model.user_id == user_id)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def update_stats(
        self,
        db: AsyncSession,
        *,
        campaign_id: int,
        stats_update: Dict[str, Any]
    ) -> Optional[Campaign]:
        """Update campaign statistics"""
        query = (
            update(self.model)
            .where(self.model.id == campaign_id)
            .values(**stats_update)
            .returning(self.model)
        )
        
        result = await db.execute(query)
        await db.commit()
        
        return result.scalar_one_or_none()
    
    async def increment_stat(
        self,
        db: AsyncSession,
        *,
        campaign_id: int,
        stat_field: str,
        increment: int = 1
    ) -> bool:
        """Increment a specific statistic field"""
        try:
            # Get current campaign
            campaign = await self.get(db, id=campaign_id)
            if not campaign:
                return False
            
            # Increment the field
            current_value = getattr(campaign, stat_field, 0)
            setattr(campaign, stat_field, current_value + increment)
            
            await db.commit()
            return True
            
        except Exception:
            await db.rollback()
            return False
    
    async def get_campaigns_for_sending(
        self,
        db: AsyncSession,
        *,
        limit: int = 100
    ) -> List[Campaign]:
        """Get campaigns that are ready for sending emails"""
        query = select(self.model).where(
            self.model.status == CampaignStatus.RUNNING,
            self.model.total_contacts > 0,
            self.model.emails_sent < self.model.total_contacts
        ).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def mark_as_completed(
        self,
        db: AsyncSession,
        *,
        campaign_id: int
    ) -> Optional[Campaign]:
        """Mark campaign as completed"""
        query = (
            update(self.model)
            .where(self.model.id == campaign_id)
            .values(status=CampaignStatus.COMPLETED)
            .returning(self.model)
        )
        
        result = await db.execute(query)
        await db.commit()
        
        return result.scalar_one_or_none()
    
    async def get_campaign_summary(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> Dict[str, Any]:
        """Get campaign summary statistics for a user"""
        # Get total campaigns by status
        status_query = select(
            self.model.status,
            func.count().label('count')
        ).where(
            self.model.user_id == user_id
        ).group_by(self.model.status)
        
        status_result = await db.execute(status_query)
        status_counts = {row.status.value: row.count for row in status_result}
        
        # Get total statistics
        stats_query = select(
            func.sum(self.model.total_contacts).label('total_contacts'),
            func.sum(self.model.emails_sent).label('total_sent'),
            func.sum(self.model.emails_delivered).label('total_delivered'),
            func.sum(self.model.emails_opened).label('total_opened'),
            func.sum(self.model.emails_clicked).label('total_clicked'),
            func.sum(self.model.emails_replied).label('total_replied'),
            func.sum(self.model.emails_bounced).label('total_bounced'),
            func.sum(self.model.emails_unsubscribed).label('total_unsubscribed')
        ).where(self.model.user_id == user_id)
        
        stats_result = await db.execute(stats_query)
        stats = stats_result.first()
        
        return {
            'status_counts': status_counts,
            'total_campaigns': sum(status_counts.values()),
            'total_contacts': stats.total_contacts or 0,
            'total_sent': stats.total_sent or 0,
            'total_delivered': stats.total_delivered or 0,
            'total_opened': stats.total_opened or 0,
            'total_clicked': stats.total_clicked or 0,
            'total_replied': stats.total_replied or 0,
            'total_bounced': stats.total_bounced or 0,
            'total_unsubscribed': stats.total_unsubscribed or 0
        }

    async def get_campaign_stats(self, db: AsyncSession, *, campaign_id: int) -> Dict[str, Any]:
        """Get campaign statistics"""
        try:
            # Get campaign
            campaign = await self.get(db, id=campaign_id)
            if not campaign:
                return {}

            # Get sequences for this campaign
            from app.crud.sequence import sequence_crud
            sequences = await sequence_crud.get_by_campaign(db, campaign_id=campaign_id)

            # Calculate totals
            total_emails_sent = sum(seq.emails_sent for seq in sequences)
            total_emails_delivered = sum(seq.emails_delivered for seq in sequences)
            total_emails_opened = sum(seq.emails_opened for seq in sequences)
            total_emails_clicked = sum(seq.emails_clicked for seq in sequences)
            total_emails_replied = sum(seq.emails_replied for seq in sequences)
            total_emails_bounced = sum(seq.emails_bounced for seq in sequences)

            # Calculate rates
            delivery_rate = (total_emails_delivered / total_emails_sent * 100) if total_emails_sent > 0 else 0
            open_rate = (total_emails_opened / total_emails_delivered * 100) if total_emails_delivered > 0 else 0
            click_rate = (total_emails_clicked / total_emails_delivered * 100) if total_emails_delivered > 0 else 0
            reply_rate = (total_emails_replied / total_emails_delivered * 100) if total_emails_delivered > 0 else 0
            bounce_rate = (total_emails_bounced / total_emails_sent * 100) if total_emails_sent > 0 else 0

            # Get contact count
            from app.crud.contact import contact_crud
            contacts = await contact_crud.get_contacts_for_campaign(
                db, user_id=campaign.user_id, campaign_id=campaign_id
            )
            total_contacts = len(contacts)

            return {
                "campaign_id": campaign_id,
                "total_contacts": total_contacts,
                "total_sequences": len(sequences),
                "emails_sent": total_emails_sent,
                "emails_delivered": total_emails_delivered,
                "emails_opened": total_emails_opened,
                "emails_clicked": total_emails_clicked,
                "emails_replied": total_emails_replied,
                "emails_bounced": total_emails_bounced,
                "delivery_rate": round(delivery_rate, 2),
                "open_rate": round(open_rate, 2),
                "click_rate": round(click_rate, 2),
                "reply_rate": round(reply_rate, 2),
                "bounce_rate": round(bounce_rate, 2)
            }

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error getting campaign stats: {str(e)}")
            return {}

    async def get_multi_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Campaign]:
        """Get multiple campaigns for a user"""
        query = select(self.model).where(self.model.user_id == user_id)
        query = query.offset(skip).limit(limit).order_by(self.model.created_at.desc())

        result = await db.execute(query)
        return result.scalars().all()


# Create campaign CRUD instance
campaign_crud = CRUDCampaign(Campaign)
