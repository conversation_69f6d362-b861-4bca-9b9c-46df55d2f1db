"""
CRUD operations for contacts
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, or_, and_
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.contact import Contact, ContactStatus
from app.schemas.contact import Contact<PERSON>reate, ContactUpdate


class CRUDContact(CRUDBase[Contact, ContactCreate, ContactUpdate]):
    """CRUD operations for contacts"""
    
    async def get_by_user(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Contact]:
        """Get contacts by user ID with optional filters"""
        query = select(self.model).where(self.model.user_id == user_id)
        
        # Filter by status
        if status:
            query = query.where(self.model.status == status)
        
        # Search filter
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    self.model.email.ilike(search_term),
                    self.model.first_name.ilike(search_term),
                    self.model.last_name.ilike(search_term),
                    self.model.company.ilike(search_term),
                    self.model.job_title.ilike(search_term)
                )
            )
        
        query = query.offset(skip).limit(limit).order_by(self.model.created_at.desc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_user_and_id(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        contact_id: int
    ) -> Optional[Contact]:
        """Get contact by user ID and contact ID"""
        query = select(self.model).where(
            self.model.user_id == user_id,
            self.model.id == contact_id
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_email(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        email: str
    ) -> Optional[Contact]:
        """Get contact by email within user's contacts"""
        query = select(self.model).where(
            self.model.user_id == user_id,
            self.model.email == email
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def count_by_user(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> int:
        """Count contacts by user ID with optional filters"""
        query = select(func.count()).select_from(self.model).where(self.model.user_id == user_id)
        
        # Filter by status
        if status:
            query = query.where(self.model.status == status)
        
        # Search filter
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    self.model.email.ilike(search_term),
                    self.model.first_name.ilike(search_term),
                    self.model.last_name.ilike(search_term),
                    self.model.company.ilike(search_term),
                    self.model.job_title.ilike(search_term)
                )
            )
        
        result = await db.execute(query)
        return result.scalar()
    
    async def bulk_create(
        self,
        db: AsyncSession,
        *,
        contacts_data: List[Dict[str, Any]],
        user_id: int
    ) -> List[Contact]:
        """Bulk create contacts"""
        contacts = []
        
        for contact_data in contacts_data:
            contact_data["user_id"] = user_id
            contact = Contact(**contact_data)
            contacts.append(contact)
        
        db.add_all(contacts)
        await db.commit()
        
        # Refresh all contacts
        for contact in contacts:
            await db.refresh(contact)
        
        return contacts
    
    async def bulk_update_status(
        self,
        db: AsyncSession,
        *,
        contact_ids: List[int],
        user_id: int,
        status: ContactStatus
    ) -> int:
        """Bulk update contact status"""
        query = (
            update(self.model)
            .where(
                and_(
                    self.model.id.in_(contact_ids),
                    self.model.user_id == user_id
                )
            )
            .values(status=status)
        )
        
        result = await db.execute(query)
        await db.commit()
        
        return result.rowcount
    
    async def get_contacts_for_campaign(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        campaign_id: Optional[int] = None,
        status: Optional[ContactStatus] = None,
        limit: Optional[int] = None
    ) -> List[Contact]:
        """Get contacts available for campaigns"""
        query = select(self.model).where(self.model.user_id == user_id)
        
        if status:
            query = query.where(self.model.status == status)
        else:
            # Default to active contacts
            query = query.where(self.model.status == ContactStatus.ACTIVE)
        
        if campaign_id:
            # Exclude contacts already in this campaign
            from app.models.campaign import CampaignContact
            subquery = select(CampaignContact.contact_id).where(
                CampaignContact.campaign_id == campaign_id
            )
            query = query.where(~self.model.id.in_(subquery))
        
        if limit:
            query = query.limit(limit)
        
        query = query.order_by(self.model.created_at.desc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def update_engagement_stats(
        self,
        db: AsyncSession,
        *,
        contact_id: int,
        emails_sent: int = 0,
        emails_opened: int = 0,
        emails_clicked: int = 0,
        emails_replied: int = 0
    ) -> Optional[Contact]:
        """Update contact engagement statistics"""
        query = (
            update(self.model)
            .where(self.model.id == contact_id)
            .values(
                emails_sent=self.model.emails_sent + emails_sent,
                emails_opened=self.model.emails_opened + emails_opened,
                emails_clicked=self.model.emails_clicked + emails_clicked,
                emails_replied=self.model.emails_replied + emails_replied,
                last_contacted_at=func.now() if emails_sent > 0 else self.model.last_contacted_at
            )
            .returning(self.model)
        )
        
        result = await db.execute(query)
        await db.commit()
        
        return result.scalar_one_or_none()
    
    async def get_contact_stats(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> Dict[str, Any]:
        """Get contact statistics for a user"""
        # Get total contacts by status
        status_query = select(
            self.model.status,
            func.count().label('count')
        ).where(
            self.model.user_id == user_id
        ).group_by(self.model.status)
        
        status_result = await db.execute(status_query)
        status_counts = {row.status.value: row.count for row in status_result}
        
        # Get engagement statistics
        engagement_query = select(
            func.count().label('total_contacts'),
            func.sum(self.model.total_opens).label('total_opened'),
            func.sum(self.model.total_clicks).label('total_clicked'),
            func.sum(self.model.total_replies).label('total_replied'),
            func.avg(self.model.lead_score).label('avg_lead_score')
        ).where(self.model.user_id == user_id)
        
        engagement_result = await db.execute(engagement_query)
        engagement = engagement_result.first()
        
        return {
            'status_counts': status_counts,
            'total_contacts': sum(status_counts.values()),
            'total_sent': 0,  # This would come from email logs
            'total_opened': engagement.total_opened or 0,
            'total_clicked': engagement.total_clicked or 0,
            'total_replied': engagement.total_replied or 0,
            'avg_open_rate': 0.0,  # Calculate from email logs
            'avg_click_rate': 0.0,  # Calculate from email logs
            'avg_lead_score': round(engagement.avg_lead_score or 0, 2)
        }
    
    async def search_contacts(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Contact]:
        """Advanced contact search"""
        search_term = f"%{query}%"
        
        search_query = select(self.model).where(
            and_(
                self.model.user_id == user_id,
                or_(
                    self.model.email.ilike(search_term),
                    self.model.first_name.ilike(search_term),
                    self.model.last_name.ilike(search_term),
                    self.model.company.ilike(search_term),
                    self.model.job_title.ilike(search_term),
                    self.model.phone.ilike(search_term),
                    self.model.website.ilike(search_term),
                    self.model.notes.ilike(search_term)
                )
            )
        ).offset(skip).limit(limit).order_by(self.model.created_at.desc())
        
        result = await db.execute(search_query)
        return result.scalars().all()

    async def get_multi_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Contact]:
        """Get multiple contacts for a user"""
        query = select(self.model).where(self.model.user_id == user_id)
        query = query.offset(skip).limit(limit).order_by(self.model.created_at.desc())

        result = await db.execute(query)
        return result.scalars().all()


# Create contact CRUD instance
contact_crud = CRUDContact(Contact)
