"""
CRUD operations for email sequences
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, or_, and_
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.sequence import EmailSequence, SequenceStatus
from app.schemas.sequence import EmailSequenceCreate, EmailSequenceUpdate


class CRUDEmailSequence(CRUDBase[EmailSequence, EmailSequenceCreate, EmailSequenceUpdate]):
    """CRUD operations for email sequences"""
    
    async def get_by_campaign(
        self, 
        db: AsyncSession, 
        *, 
        campaign_id: int,
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[EmailSequence]:
        """Get sequences by campaign ID"""
        query = select(self.model).where(self.model.campaign_id == campaign_id)
        
        # Filter by status
        if status:
            query = query.where(self.model.status == status)
        
        query = query.offset(skip).limit(limit).order_by(self.model.order.asc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_user(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100,
        campaign_id: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[EmailSequence]:
        """Get sequences by user ID with optional filters"""
        # Join with campaigns to filter by user
        from app.models.campaign import Campaign
        
        query = (
            select(self.model)
            .join(Campaign, self.model.campaign_id == Campaign.id)
            .where(Campaign.user_id == user_id)
        )
        
        # Filter by campaign
        if campaign_id:
            query = query.where(self.model.campaign_id == campaign_id)
        
        # Filter by status
        if status:
            query = query.where(self.model.status == status)
        
        query = query.offset(skip).limit(limit).order_by(
            self.model.campaign_id.asc(),
            self.model.order.asc()
        )
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_user_and_id(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        sequence_id: int
    ) -> Optional[EmailSequence]:
        """Get sequence by user ID and sequence ID"""
        from app.models.campaign import Campaign
        
        query = (
            select(self.model)
            .join(Campaign, self.model.campaign_id == Campaign.id)
            .where(
                Campaign.user_id == user_id,
                self.model.id == sequence_id
            )
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def count_by_campaign(
        self, 
        db: AsyncSession, 
        *, 
        campaign_id: int,
        status: Optional[str] = None
    ) -> int:
        """Count sequences by campaign ID"""
        query = select(func.count()).select_from(self.model).where(
            self.model.campaign_id == campaign_id
        )
        
        if status:
            query = query.where(self.model.status == status)
        
        result = await db.execute(query)
        return result.scalar()
    
    async def get_next_order(
        self,
        db: AsyncSession,
        *,
        campaign_id: int
    ) -> int:
        """Get the next order number for a campaign"""
        query = select(func.max(self.model.order)).where(
            self.model.campaign_id == campaign_id
        )

        result = await db.execute(query)
        max_order = result.scalar()

        return (max_order or 0) + 1
    
    async def reorder_sequences(
        self,
        db: AsyncSession,
        *,
        campaign_id: int,
        sequence_orders: List[Dict[str, int]]  # [{"id": 1, "order": 1}, ...]
    ) -> List[EmailSequence]:
        """Reorder sequences in a campaign"""
        sequences = []
        
        for order_data in sequence_orders:
            sequence_id = order_data["id"]
            new_order = order_data["order"]

            # Update the sequence
            query = (
                update(self.model)
                .where(
                    and_(
                        self.model.id == sequence_id,
                        self.model.campaign_id == campaign_id
                    )
                )
                .values(order=new_order)
                .returning(self.model)
            )
            
            result = await db.execute(query)
            sequence = result.scalar_one_or_none()
            if sequence:
                sequences.append(sequence)
        
        await db.commit()
        return sequences
    
    async def duplicate_sequence(
        self,
        db: AsyncSession,
        *,
        sequence_id: int,
        new_campaign_id: Optional[int] = None
    ) -> EmailSequence:
        """Duplicate a sequence"""
        # Get original sequence
        original = await self.get(db, id=sequence_id)
        if not original:
            raise ValueError("Sequence not found")
        
        # Determine target campaign
        target_campaign_id = new_campaign_id or original.campaign_id
        
        # Get next order number
        next_order = await self.get_next_order(db, campaign_id=target_campaign_id)
        
        # Create duplicate
        duplicate_data = {
            "campaign_id": target_campaign_id,
            "order": next_order,
            "name": f"{original.name} (Copy)",
            "subject": original.subject,
            "content": original.content,
            "delay_days": original.delay_days,
            "delay_hours": original.delay_hours,
            "status": SequenceStatus.DRAFT,
            "use_ai_subject": original.use_ai_subject,
            "use_ai_content": original.use_ai_content
        }
        
        duplicate = EmailSequence(**duplicate_data)
        db.add(duplicate)
        await db.commit()
        await db.refresh(duplicate)
        
        return duplicate
    
    async def get_active_sequences_for_sending(
        self,
        db: AsyncSession,
        *,
        campaign_id: Optional[int] = None,
        limit: Optional[int] = None
    ) -> List[EmailSequence]:
        """Get active sequences ready for sending"""
        query = select(self.model).where(
            self.model.status == SequenceStatus.ACTIVE
        )
        
        if campaign_id:
            query = query.where(self.model.campaign_id == campaign_id)
        
        if limit:
            query = query.limit(limit)
        
        query = query.order_by(
            self.model.campaign_id.asc(),
            self.model.order.asc()
        )
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def update_sequence_stats(
        self,
        db: AsyncSession,
        *,
        sequence_id: int,
        emails_sent: int = 0,
        emails_delivered: int = 0,
        emails_opened: int = 0,
        emails_clicked: int = 0,
        emails_replied: int = 0,
        emails_bounced: int = 0
    ) -> Optional[EmailSequence]:
        """Update sequence statistics"""
        query = (
            update(self.model)
            .where(self.model.id == sequence_id)
            .values(
                emails_sent=self.model.emails_sent + emails_sent,
                emails_delivered=self.model.emails_delivered + emails_delivered,
                emails_opened=self.model.emails_opened + emails_opened,
                emails_clicked=self.model.emails_clicked + emails_clicked,
                emails_replied=self.model.emails_replied + emails_replied,
                emails_bounced=self.model.emails_bounced + emails_bounced,
                last_sent_at=func.now() if emails_sent > 0 else self.model.last_sent_at
            )
            .returning(self.model)
        )
        
        result = await db.execute(query)
        await db.commit()
        
        return result.scalar_one_or_none()
    
    async def get_sequence_performance(
        self,
        db: AsyncSession,
        *,
        sequence_id: int
    ) -> Dict[str, Any]:
        """Get detailed sequence performance metrics"""
        sequence = await self.get(db, id=sequence_id)
        if not sequence:
            return {}
        
        # Calculate rates
        delivery_rate = (sequence.emails_delivered / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0
        open_rate = (sequence.emails_opened / sequence.emails_delivered * 100) if sequence.emails_delivered > 0 else 0
        click_rate = (sequence.emails_clicked / sequence.emails_opened * 100) if sequence.emails_opened > 0 else 0
        reply_rate = (sequence.emails_replied / sequence.emails_delivered * 100) if sequence.emails_delivered > 0 else 0
        bounce_rate = (sequence.emails_bounced / sequence.emails_sent * 100) if sequence.emails_sent > 0 else 0
        
        return {
            "sequence_id": sequence_id,
            "emails_sent": sequence.emails_sent,
            "emails_delivered": sequence.emails_delivered,
            "emails_opened": sequence.emails_opened,
            "emails_clicked": sequence.emails_clicked,
            "emails_replied": sequence.emails_replied,
            "emails_bounced": sequence.emails_bounced,
            "delivery_rate": round(delivery_rate, 2),
            "open_rate": round(open_rate, 2),
            "click_rate": round(click_rate, 2),
            "reply_rate": round(reply_rate, 2),
            "bounce_rate": round(bounce_rate, 2),
            "last_sent_at": sequence.last_sent_at,
            "created_at": sequence.created_at
        }
    
    async def search_sequences(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[EmailSequence]:
        """Search sequences by name, subject, or content"""
        from app.models.campaign import Campaign
        
        search_term = f"%{query}%"
        
        search_query = (
            select(self.model)
            .join(Campaign, self.model.campaign_id == Campaign.id)
            .where(
                and_(
                    Campaign.user_id == user_id,
                    or_(
                        self.model.name.ilike(search_term),
                        self.model.subject_line.ilike(search_term),
                        self.model.email_content.ilike(search_term)
                    )
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        
        result = await db.execute(search_query)
        return result.scalars().all()

    async def get_multi_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[EmailSequence]:
        """Get multiple sequences for a user"""
        from app.models.campaign import Campaign

        query = (
            select(self.model)
            .join(Campaign, self.model.campaign_id == Campaign.id)
            .where(Campaign.user_id == user_id)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )

        result = await db.execute(query)
        return result.scalars().all()


# Create sequence CRUD instance
sequence_crud = CRUDEmailSequence(EmailSequence)
