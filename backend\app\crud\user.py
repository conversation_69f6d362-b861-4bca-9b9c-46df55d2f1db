"""
User CRUD operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.user import User, UserStatus
from app.schemas.user import UserCreate, UserUpdate
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """Get user by email"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()

    async def get_by_username(self, db: AsyncSession, *, username: str) -> Optional[User]:
        """Get user by username"""
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()

    async def create(self, db: AsyncSession, *, obj_in) -> User:
        """Create user with hashed password"""
        if hasattr(obj_in, 'model_dump'):
            create_data = obj_in.model_dump()
        elif isinstance(obj_in, dict):
            create_data = obj_in.copy()
        else:
            create_data = obj_in

        # Hash password if it exists
        if "password" in create_data:
            create_data["hashed_password"] = pwd_context.hash(create_data.pop("password"))

        db_obj = User(**create_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_password(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        hashed_password: str
    ) -> User:
        """Update user password"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(hashed_password=hashed_password, updated_at=func.now())
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def verify_email(self, db: AsyncSession, *, user_id: int) -> User:
        """Verify user email"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                is_verified=True,
                email_verified_at=func.now(),
                status=UserStatus.ACTIVE,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def update_last_login(self, db: AsyncSession, *, user_id: int) -> User:
        """Update user last login timestamp"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(last_login_at=func.now(), updated_at=func.now())
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def activate_user(self, db: AsyncSession, *, user_id: int) -> User:
        """Activate user account"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                is_active=True,
                status=UserStatus.ACTIVE,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def deactivate_user(self, db: AsyncSession, *, user_id: int) -> User:
        """Deactivate user account"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                is_active=False,
                status=UserStatus.INACTIVE,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def suspend_user(self, db: AsyncSession, *, user_id: int) -> User:
        """Suspend user account"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                is_active=False,
                status=UserStatus.SUSPENDED,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def increment_email_count(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        count: int = 1
    ) -> User:
        """Increment user's monthly email count"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                emails_sent_this_month=User.emails_sent_this_month + count,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def reset_monthly_email_count(self, db: AsyncSession, *, user_id: int) -> User:
        """Reset user's monthly email count"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(emails_sent_this_month=0, updated_at=func.now())
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def update_subscription(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        plan: str, 
        email_limit: int
    ) -> User:
        """Update user subscription plan"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                subscription_plan=plan,
                monthly_email_limit=email_limit,
                updated_at=func.now()
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return await self.get(db, id=user_id)

    async def get_active_users(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """Get all active users"""
        query = (
            select(User)
            .where(User.is_active == True, User.status == UserStatus.ACTIVE)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def search_users(
        self,
        db: AsyncSession,
        *,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """Search users by email, username, or full name"""
        search_query = select(User).where(
            (User.email.ilike(f"%{query}%")) |
            (User.username.ilike(f"%{query}%")) |
            (User.full_name.ilike(f"%{query}%")) |
            (User.company.ilike(f"%{query}%"))
        ).offset(skip).limit(limit)
        
        result = await db.execute(search_query)
        return result.scalars().all()

    async def get_user_stats(self, db: AsyncSession, *, user_id: int) -> Dict[str, Any]:
        """Get user statistics"""
        # This would typically involve joins with other tables
        # For now, return basic user info
        user = await self.get(db, id=user_id)
        if not user:
            return {}
        
        return {
            "emails_sent_this_month": user.emails_sent_this_month,
            "remaining_emails": user.remaining_emails,
            "subscription_plan": user.subscription_plan,
            "monthly_limit": user.monthly_email_limit,
            "account_created": user.created_at,
            "last_login": user.last_login_at,
            "is_verified": user.is_verified,
        }


# Create instance
user_crud = CRUDUser(User)
