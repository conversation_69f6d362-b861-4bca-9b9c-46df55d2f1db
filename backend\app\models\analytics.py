"""
Analytics and email log models
"""

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, Text, ForeignKey, Enum, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class EmailStatus(str, enum.Enum):
    """Email status"""
    QUEUED = "queued"
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    OPENED = "opened"
    CLICKED = "clicked"
    REPLIED = "replied"
    BOUNCED = "bounced"
    FAILED = "failed"
    UNSUBSCRIBED = "unsubscribed"


class EmailLog(Base):
    """Email log model for tracking individual email sends"""
    
    __tablename__ = "email_logs"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False, index=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False, index=True)
    sequence_id = Column(Integer, Foreign<PERSON>ey("email_sequences.id"), nullable=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Email details
    subject = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    content_html = Column(Text, nullable=True)
    from_email = Column(String(255), nullable=False)
    from_name = Column(String(255), nullable=True)
    to_email = Column(String(255), nullable=False, index=True)
    reply_to_email = Column(String(255), nullable=True)
    
    # Status and tracking
    status = Column(Enum(EmailStatus), default=EmailStatus.QUEUED, nullable=False, index=True)
    message_id = Column(String(255), nullable=True, unique=True, index=True)  # External message ID
    
    # Timestamps
    queued_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    sent_at = Column(DateTime(timezone=True), nullable=True, index=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    opened_at = Column(DateTime(timezone=True), nullable=True)
    clicked_at = Column(DateTime(timezone=True), nullable=True)
    replied_at = Column(DateTime(timezone=True), nullable=True)
    bounced_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Engagement tracking
    open_count = Column(Integer, default=0, nullable=False)
    click_count = Column(Integer, default=0, nullable=False)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    
    # Metadata
    user_agent = Column(String(500), nullable=True)  # For opens/clicks
    ip_address = Column(String(45), nullable=True)   # For opens/clicks
    location_data = Column(JSON, nullable=True)      # Geolocation data
    device_info = Column(JSON, nullable=True)        # Device information
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="email_logs")
    contact = relationship("Contact", back_populates="email_logs")
    sequence = relationship("EmailSequence", back_populates="email_logs")
    
    def __repr__(self):
        return f"<EmailLog(id={self.id}, to_email='{self.to_email}', status='{self.status}')>"
    
    @property
    def is_delivered(self) -> bool:
        """Check if email was delivered"""
        return self.status in [EmailStatus.DELIVERED, EmailStatus.OPENED, EmailStatus.CLICKED, EmailStatus.REPLIED]
    
    @property
    def is_engaged(self) -> bool:
        """Check if recipient engaged with email"""
        return self.status in [EmailStatus.OPENED, EmailStatus.CLICKED, EmailStatus.REPLIED]
    
    def mark_sent(self, message_id: str = None) -> None:
        """Mark email as sent"""
        self.status = EmailStatus.SENT
        self.sent_at = func.now()
        if message_id:
            self.message_id = message_id
    
    def mark_delivered(self) -> None:
        """Mark email as delivered"""
        self.status = EmailStatus.DELIVERED
        self.delivered_at = func.now()
    
    def mark_opened(self, user_agent: str = None, ip_address: str = None) -> None:
        """Mark email as opened"""
        if self.status not in [EmailStatus.OPENED, EmailStatus.CLICKED, EmailStatus.REPLIED]:
            self.status = EmailStatus.OPENED
            self.opened_at = func.now()
        
        self.open_count += 1
        if user_agent:
            self.user_agent = user_agent
        if ip_address:
            self.ip_address = ip_address
    
    def mark_clicked(self, user_agent: str = None, ip_address: str = None) -> None:
        """Mark email as clicked"""
        if self.status not in [EmailStatus.CLICKED, EmailStatus.REPLIED]:
            self.status = EmailStatus.CLICKED
            self.clicked_at = func.now()
        
        self.click_count += 1
        if user_agent:
            self.user_agent = user_agent
        if ip_address:
            self.ip_address = ip_address
    
    def mark_replied(self) -> None:
        """Mark email as replied"""
        self.status = EmailStatus.REPLIED
        self.replied_at = func.now()
    
    def mark_bounced(self, error_message: str = None) -> None:
        """Mark email as bounced"""
        self.status = EmailStatus.BOUNCED
        self.bounced_at = func.now()
        if error_message:
            self.error_message = error_message
    
    def mark_failed(self, error_message: str = None) -> None:
        """Mark email as failed"""
        self.status = EmailStatus.FAILED
        self.failed_at = func.now()
        if error_message:
            self.error_message = error_message
        self.retry_count += 1


class DailyStats(Base):
    """Daily statistics aggregation"""
    
    __tablename__ = "daily_stats"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=True, index=True)
    
    # Date
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Email statistics
    emails_sent = Column(Integer, default=0, nullable=False)
    emails_delivered = Column(Integer, default=0, nullable=False)
    emails_opened = Column(Integer, default=0, nullable=False)
    emails_clicked = Column(Integer, default=0, nullable=False)
    emails_replied = Column(Integer, default=0, nullable=False)
    emails_bounced = Column(Integer, default=0, nullable=False)
    emails_unsubscribed = Column(Integer, default=0, nullable=False)
    
    # Calculated rates
    delivery_rate = Column(Float, default=0.0, nullable=False)
    open_rate = Column(Float, default=0.0, nullable=False)
    click_rate = Column(Float, default=0.0, nullable=False)
    reply_rate = Column(Float, default=0.0, nullable=False)
    bounce_rate = Column(Float, default=0.0, nullable=False)
    unsubscribe_rate = Column(Float, default=0.0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<DailyStats(date={self.date}, emails_sent={self.emails_sent})>"
    
    def calculate_rates(self) -> None:
        """Calculate all rates based on current statistics"""
        if self.emails_sent > 0:
            self.delivery_rate = (self.emails_delivered / self.emails_sent) * 100
            self.bounce_rate = (self.emails_bounced / self.emails_sent) * 100
        
        if self.emails_delivered > 0:
            self.open_rate = (self.emails_opened / self.emails_delivered) * 100
            self.click_rate = (self.emails_clicked / self.emails_delivered) * 100
            self.reply_rate = (self.emails_replied / self.emails_delivered) * 100
            self.unsubscribe_rate = (self.emails_unsubscribed / self.emails_delivered) * 100
