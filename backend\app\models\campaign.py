"""
Campaign model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class CampaignStatus(str, enum.Enum):
    """Campaign status"""
    DRAFT = "draft"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class CampaignType(str, enum.Enum):
    """Campaign type"""
    OUTREACH = "outreach"
    FOLLOW_UP = "follow_up"
    NURTURE = "nurture"
    PROMOTIONAL = "promotional"
    TRANSACTIONAL = "transactional"


class AIPersonalizationLevel(str, enum.Enum):
    """AI personalization level"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class Campaign(Base):
    """Campaign model"""
    
    __tablename__ = "campaigns"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Campaign settings
    status = Column(Enum(CampaignStatus), default=CampaignStatus.DRAFT, nullable=False, index=True)
    campaign_type = Column(Enum(CampaignType), default=CampaignType.OUTREACH, nullable=False)
    
    # Email settings
    from_name = Column(String(255), nullable=True)
    from_email = Column(String(255), nullable=True)
    reply_to_email = Column(String(255), nullable=True)
    
    # Scheduling
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    timezone = Column(String(50), default="UTC", nullable=False)
    
    # Sending settings
    daily_limit = Column(Integer, default=50, nullable=False)
    hourly_limit = Column(Integer, default=10, nullable=False)
    send_weekdays_only = Column(Boolean, default=True, nullable=False)
    send_time_start = Column(String(5), default="09:00", nullable=False)  # HH:MM format
    send_time_end = Column(String(5), default="17:00", nullable=False)    # HH:MM format
    
    # AI settings
    use_ai_optimization = Column(Boolean, default=True, nullable=False)
    ai_personalization_level = Column(Enum(AIPersonalizationLevel), default=AIPersonalizationLevel.MEDIUM, nullable=False)
    ai_subject_optimization = Column(Boolean, default=True, nullable=False)
    
    # Tracking settings
    track_opens = Column(Boolean, default=True, nullable=False)
    track_clicks = Column(Boolean, default=True, nullable=False)
    track_replies = Column(Boolean, default=True, nullable=False)
    
    # Statistics
    total_contacts = Column(Integer, default=0, nullable=False)
    emails_sent = Column(Integer, default=0, nullable=False)
    emails_delivered = Column(Integer, default=0, nullable=False)
    emails_opened = Column(Integer, default=0, nullable=False)
    emails_clicked = Column(Integer, default=0, nullable=False)
    emails_replied = Column(Integer, default=0, nullable=False)
    emails_bounced = Column(Integer, default=0, nullable=False)
    emails_unsubscribed = Column(Integer, default=0, nullable=False)
    
    # Metadata
    tags = Column(JSON, default=list, nullable=False)  # List of tags
    custom_fields = Column(JSON, default=dict, nullable=False)  # Custom metadata
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_sent_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="campaigns")
    sequences = relationship("EmailSequence", back_populates="campaign", cascade="all, delete-orphan")
    campaign_contacts = relationship("CampaignContact", back_populates="campaign", cascade="all, delete-orphan")
    email_logs = relationship("EmailLog", back_populates="campaign", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def open_rate(self) -> float:
        """Calculate open rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_opened / self.emails_delivered) * 100
    
    @property
    def click_rate(self) -> float:
        """Calculate click rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_clicked / self.emails_delivered) * 100
    
    @property
    def reply_rate(self) -> float:
        """Calculate reply rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_replied / self.emails_delivered) * 100
    
    @property
    def bounce_rate(self) -> float:
        """Calculate bounce rate percentage"""
        if self.emails_sent == 0:
            return 0.0
        return (self.emails_bounced / self.emails_sent) * 100
    
    @property
    def unsubscribe_rate(self) -> float:
        """Calculate unsubscribe rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_unsubscribed / self.emails_delivered) * 100
    
    @property
    def is_active(self) -> bool:
        """Check if campaign is active"""
        return self.status == CampaignStatus.RUNNING
    
    @property
    def can_send_emails(self) -> bool:
        """Check if campaign can send emails"""
        return (
            self.status == CampaignStatus.RUNNING and
            (self.end_date is None or self.end_date > func.now()) and
            len(self.sequences) > 0
        )
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the campaign"""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the campaign"""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def update_stats(self, 
                    sent: int = 0, 
                    delivered: int = 0, 
                    opened: int = 0, 
                    clicked: int = 0, 
                    replied: int = 0, 
                    bounced: int = 0, 
                    unsubscribed: int = 0) -> None:
        """Update campaign statistics"""
        self.emails_sent += sent
        self.emails_delivered += delivered
        self.emails_opened += opened
        self.emails_clicked += clicked
        self.emails_replied += replied
        self.emails_bounced += bounced
        self.emails_unsubscribed += unsubscribed
        
        if sent > 0:
            self.last_sent_at = func.now()
