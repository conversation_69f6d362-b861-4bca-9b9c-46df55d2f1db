"""
Contact model
"""

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, Text, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class ContactStatus(str, enum.Enum):
    """Contact status"""
    ACTIVE = "active"
    UNSUBSCRIBED = "unsubscribed"
    BOUNCED = "bounced"
    COMPLAINED = "complained"
    SUPPRESSED = "suppressed"


class ContactSource(str, enum.Enum):
    """Contact source"""
    MANUAL = "manual"
    CSV_IMPORT = "csv_import"
    API = "api"
    WEBHOOK = "webhook"
    INTEGRATION = "integration"


class Contact(Base):
    """Contact model"""
    
    __tablename__ = "contacts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Basic information
    email = Column(String(255), nullable=False, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    full_name = Column(String(255), nullable=True)
    
    # Contact details
    company = Column(String(255), nullable=True, index=True)
    job_title = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    website = Column(String(255), nullable=True)
    linkedin_url = Column(String(255), nullable=True)
    twitter_handle = Column(String(100), nullable=True)
    
    # Address information
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Status and preferences
    status = Column(Enum(ContactStatus), default=ContactStatus.ACTIVE, nullable=False, index=True)
    source = Column(Enum(ContactSource), default=ContactSource.MANUAL, nullable=False)
    
    # Email preferences
    email_verified = Column(Boolean, default=False, nullable=False)
    accepts_marketing = Column(Boolean, default=True, nullable=False)
    preferred_language = Column(String(10), default="en", nullable=False)
    timezone = Column(String(50), nullable=True)
    
    # Engagement data
    last_opened_at = Column(DateTime(timezone=True), nullable=True)
    last_clicked_at = Column(DateTime(timezone=True), nullable=True)
    last_replied_at = Column(DateTime(timezone=True), nullable=True)
    total_opens = Column(Integer, default=0, nullable=False)
    total_clicks = Column(Integer, default=0, nullable=False)
    total_replies = Column(Integer, default=0, nullable=False)
    
    # Segmentation
    tags = Column(JSON, default=list, nullable=False)  # List of tags
    custom_fields = Column(JSON, default=dict, nullable=False)  # Custom contact data
    lead_score = Column(Integer, default=0, nullable=False)
    
    # Notes and history
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    unsubscribed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="contacts")
    campaign_contacts = relationship("CampaignContact", back_populates="contact", cascade="all, delete-orphan")
    email_logs = relationship("EmailLog", back_populates="contact", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Contact(id={self.id}, email='{self.email}', status='{self.status}')>"
    
    @property
    def display_name(self) -> str:
        """Get display name for contact"""
        if self.full_name:
            return self.full_name
        elif self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email
    
    @property
    def is_active(self) -> bool:
        """Check if contact is active"""
        return self.status == ContactStatus.ACTIVE
    
    @property
    def can_receive_emails(self) -> bool:
        """Check if contact can receive emails"""
        return (
            self.status == ContactStatus.ACTIVE and
            self.accepts_marketing and
            self.email_verified
        )
    
    @property
    def engagement_score(self) -> float:
        """Calculate engagement score based on opens, clicks, and replies"""
        # Simple scoring: replies worth 10, clicks worth 3, opens worth 1
        score = (self.total_replies * 10) + (self.total_clicks * 3) + self.total_opens
        return min(score, 100)  # Cap at 100
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the contact"""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the contact"""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def set_custom_field(self, key: str, value: any) -> None:
        """Set a custom field value"""
        self.custom_fields[key] = value
    
    def get_custom_field(self, key: str, default=None) -> any:
        """Get a custom field value"""
        return self.custom_fields.get(key, default)
    
    def unsubscribe(self) -> None:
        """Unsubscribe the contact"""
        self.status = ContactStatus.UNSUBSCRIBED
        self.accepts_marketing = False
        self.unsubscribed_at = func.now()
    
    def update_engagement(self, opened: bool = False, clicked: bool = False, replied: bool = False) -> None:
        """Update engagement statistics"""
        now = func.now()
        
        if opened:
            self.total_opens += 1
            self.last_opened_at = now
            
        if clicked:
            self.total_clicks += 1
            self.last_clicked_at = now
            
        if replied:
            self.total_replies += 1
            self.last_replied_at = now
            
        # Update lead score based on engagement
        if replied:
            self.lead_score += 10
        elif clicked:
            self.lead_score += 3
        elif opened:
            self.lead_score += 1
