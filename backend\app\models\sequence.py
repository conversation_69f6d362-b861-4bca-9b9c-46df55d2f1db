"""
Email sequence models
"""

from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Boolean, DateTime, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class SequenceStatus(str, enum.Enum):
    """Sequence status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class EmailSequence(Base):
    """Email sequence model"""
    
    __tablename__ = "email_sequences"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False, index=True)
    
    # Sequence information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    order = Column(Integer, nullable=False, index=True)  # Order in the sequence
    
    # Email content
    subject = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    content_html = Column(Text, nullable=True)  # HTML version
    content_text = Column(Text, nullable=True)  # Plain text version
    
    # Timing
    delay_days = Column(Integer, default=0, nullable=False)  # Days after previous email
    delay_hours = Column(Integer, default=0, nullable=False)  # Additional hours
    delay_minutes = Column(Integer, default=0, nullable=False)  # Additional minutes
    
    # Status and settings
    status = Column(Enum(SequenceStatus), default=SequenceStatus.ACTIVE, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # AI settings
    use_ai_subject = Column(Boolean, default=True, nullable=False)
    use_ai_content = Column(Boolean, default=True, nullable=False)
    ai_personalization_prompt = Column(Text, nullable=True)
    
    # A/B testing
    is_ab_test = Column(Boolean, default=False, nullable=False)
    ab_test_percentage = Column(Integer, default=50, nullable=False)  # Percentage for variant A
    ab_variant = Column(String(1), nullable=True)  # 'A' or 'B'
    
    # Statistics
    emails_sent = Column(Integer, default=0, nullable=False)
    emails_delivered = Column(Integer, default=0, nullable=False)
    emails_opened = Column(Integer, default=0, nullable=False)
    emails_clicked = Column(Integer, default=0, nullable=False)
    emails_replied = Column(Integer, default=0, nullable=False)
    emails_bounced = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_sent_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="sequences")
    email_logs = relationship("EmailLog", back_populates="sequence", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<EmailSequence(id={self.id}, name='{self.name}', order={self.order})>"
    
    @property
    def total_delay_minutes(self) -> int:
        """Get total delay in minutes"""
        return (self.delay_days * 24 * 60) + (self.delay_hours * 60) + self.delay_minutes
    
    @property
    def open_rate(self) -> float:
        """Calculate open rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_opened / self.emails_delivered) * 100
    
    @property
    def click_rate(self) -> float:
        """Calculate click rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_clicked / self.emails_delivered) * 100
    
    @property
    def reply_rate(self) -> float:
        """Calculate reply rate percentage"""
        if self.emails_delivered == 0:
            return 0.0
        return (self.emails_replied / self.emails_delivered) * 100
    
    @property
    def bounce_rate(self) -> float:
        """Calculate bounce rate percentage"""
        if self.emails_sent == 0:
            return 0.0
        return (self.emails_bounced / self.emails_sent) * 100
    
    def update_stats(self, 
                    sent: int = 0, 
                    delivered: int = 0, 
                    opened: int = 0, 
                    clicked: int = 0, 
                    replied: int = 0, 
                    bounced: int = 0) -> None:
        """Update sequence statistics"""
        self.emails_sent += sent
        self.emails_delivered += delivered
        self.emails_opened += opened
        self.emails_clicked += clicked
        self.emails_replied += replied
        self.emails_bounced += bounced
        
        if sent > 0:
            self.last_sent_at = func.now()


class CampaignContact(Base):
    """Association table for campaigns and contacts with status tracking"""
    
    __tablename__ = "campaign_contacts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False, index=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False, index=True)
    
    # Status tracking
    status = Column(String(50), default="active", nullable=False, index=True)
    current_sequence_step = Column(Integer, default=0, nullable=False)  # Current step in sequence
    completed_sequences = Column(Integer, default=0, nullable=False)  # Number of completed sequences
    
    # Scheduling
    next_send_at = Column(DateTime(timezone=True), nullable=True, index=True)
    last_sent_at = Column(DateTime(timezone=True), nullable=True)
    
    # Engagement
    total_opens = Column(Integer, default=0, nullable=False)
    total_clicks = Column(Integer, default=0, nullable=False)
    total_replies = Column(Integer, default=0, nullable=False)
    last_opened_at = Column(DateTime(timezone=True), nullable=True)
    last_clicked_at = Column(DateTime(timezone=True), nullable=True)
    last_replied_at = Column(DateTime(timezone=True), nullable=True)
    
    # Flags
    is_completed = Column(Boolean, default=False, nullable=False)
    is_unsubscribed = Column(Boolean, default=False, nullable=False)
    is_bounced = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="campaign_contacts")
    contact = relationship("Contact", back_populates="campaign_contacts")
    
    def __repr__(self):
        return f"<CampaignContact(campaign_id={self.campaign_id}, contact_id={self.contact_id}, status='{self.status}')>"
    
    @property
    def can_receive_next_email(self) -> bool:
        """Check if contact can receive the next email in sequence"""
        return (
            self.status == "active" and
            not self.is_completed and
            not self.is_unsubscribed and
            not self.is_bounced and
            self.next_send_at is not None
        )
    
    def advance_sequence(self, delay_minutes: int = 0) -> None:
        """Advance to next sequence step"""
        self.current_sequence_step += 1
        if delay_minutes > 0:
            from datetime import datetime, timedelta
            self.next_send_at = datetime.utcnow() + timedelta(minutes=delay_minutes)
    
    def complete_sequence(self) -> None:
        """Mark sequence as completed"""
        self.is_completed = True
        self.completed_sequences += 1
        self.status = "completed"
        self.next_send_at = None
    
    def unsubscribe(self) -> None:
        """Unsubscribe contact from campaign"""
        self.is_unsubscribed = True
        self.status = "unsubscribed"
        self.next_send_at = None
    
    def mark_bounced(self) -> None:
        """Mark contact as bounced"""
        self.is_bounced = True
        self.status = "bounced"
        self.next_send_at = None
