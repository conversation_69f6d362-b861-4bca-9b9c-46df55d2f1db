"""
User model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class UserRole(str, enum.Enum):
    """User roles"""
    USER = "user"
    ADMIN = "admin"
    SUPERUSER = "superuser"


class UserStatus(str, enum.Enum):
    """User status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(Base):
    """User model"""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=True)
    full_name = Column(String(255), nullable=True)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Role and status
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.PENDING_VERIFICATION, nullable=False)
    
    # Profile information
    company = Column(String(255), nullable=True)
    job_title = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    website = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)
    
    # Email settings
    default_from_name = Column(String(255), nullable=True)
    default_from_email = Column(String(255), nullable=True)
    default_reply_to = Column(String(255), nullable=True)
    
    # Subscription and limits
    subscription_plan = Column(String(50), default="free", nullable=False)
    monthly_email_limit = Column(Integer, default=1000, nullable=False)
    emails_sent_this_month = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    campaigns = relationship("Campaign", back_populates="user", cascade="all, delete-orphan")
    contacts = relationship("Contact", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role}')>"
    
    @property
    def is_email_verified(self) -> bool:
        """Check if email is verified"""
        return self.is_verified and self.email_verified_at is not None
    
    @property
    def can_send_emails(self) -> bool:
        """Check if user can send emails"""
        return (
            self.is_active and 
            self.is_email_verified and 
            self.status == UserStatus.ACTIVE and
            self.emails_sent_this_month < self.monthly_email_limit
        )
    
    @property
    def remaining_emails(self) -> int:
        """Get remaining emails for this month"""
        return max(0, self.monthly_email_limit - self.emails_sent_this_month)
    
    def increment_email_count(self, count: int = 1) -> None:
        """Increment email count for this month"""
        self.emails_sent_this_month += count
