"""
Campaign schemas for API requests and responses
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class CampaignBase(BaseModel):
    """Base campaign schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    campaign_type: str = Field(default="outreach")
    from_name: Optional[str] = Field(None, max_length=255)
    from_email: Optional[str] = Field(None, max_length=255)
    reply_to_email: Optional[str] = Field(None, max_length=255)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    timezone: str = Field(default="UTC", max_length=50)
    daily_limit: int = Field(default=100, ge=1, le=1000)
    hourly_limit: int = Field(default=10, ge=1, le=100)
    send_weekdays_only: bool = Field(default=True)
    send_time_start: str = Field(default="09:00")
    send_time_end: str = Field(default="17:00")
    use_ai_optimization: bool = Field(default=True)
    ai_personalization_level: str = Field(default="medium")
    ai_subject_optimization: bool = Field(default=True)
    track_opens: bool = Field(default=True)
    track_clicks: bool = Field(default=True)
    track_replies: bool = Field(default=True)
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)


class CampaignCreate(CampaignBase):
    """Schema for creating campaigns"""
    pass


class CampaignUpdate(BaseModel):
    """Schema for updating campaigns"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    from_name: Optional[str] = Field(None, max_length=255)
    from_email: Optional[str] = Field(None, max_length=255)
    reply_to_email: Optional[str] = Field(None, max_length=255)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    daily_limit: Optional[int] = Field(None, ge=1, le=1000)
    hourly_limit: Optional[int] = Field(None, ge=1, le=100)
    send_weekdays_only: Optional[bool] = None
    send_time_start: Optional[str] = None
    send_time_end: Optional[str] = None
    use_ai_optimization: Optional[bool] = None
    ai_personalization_level: Optional[str] = None
    ai_subject_optimization: Optional[bool] = None
    track_opens: Optional[bool] = None
    track_clicks: Optional[bool] = None
    track_replies: Optional[bool] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class CampaignInDB(CampaignBase):
    """Schema for campaigns in database"""
    id: int
    user_id: int
    status: str
    total_contacts: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    emails_unsubscribed: int
    created_at: datetime
    updated_at: datetime
    last_sent_at: Optional[datetime]

    model_config = {"from_attributes": True}


class Campaign(CampaignInDB):
    """Public campaign schema"""
    pass


class CampaignStats(BaseModel):
    """Campaign statistics schema"""
    total_contacts: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    emails_unsubscribed: int
    delivery_rate: float
    open_rate: float
    click_rate: float
    reply_rate: float
    bounce_rate: float
    unsubscribe_rate: float


class CampaignSummary(BaseModel):
    """Campaign summary statistics"""
    status_counts: Dict[str, int]
    total_campaigns: int
    total_contacts: int
    total_sent: int
    total_delivered: int
    total_opened: int
    total_clicked: int
    total_replied: int
    total_bounced: int
    total_unsubscribed: int


class CampaignList(BaseModel):
    """Paginated campaign list"""
    campaigns: List[Campaign]
    total: int
    page: int
    size: int
    pages: int
