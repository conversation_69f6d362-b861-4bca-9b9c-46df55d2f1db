"""
Contact schemas for API requests and responses
"""

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime


class ContactBase(BaseModel):
    """Base contact schema"""
    email: EmailStr
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    full_name: Optional[str] = Field(None, max_length=255)
    company: Optional[str] = Field(None, max_length=255)
    job_title: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    website: Optional[str] = Field(None, max_length=255)
    linkedin_url: Optional[str] = Field(None, max_length=255)
    twitter_handle: Optional[str] = Field(None, max_length=100)

    # Address fields
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)

    # Email preferences
    email_verified: Optional[bool] = Field(default=False)
    accepts_marketing: Optional[bool] = Field(default=True)
    preferred_language: Optional[str] = Field(default="en", max_length=10)
    timezone: Optional[str] = Field(None, max_length=50)

    # Segmentation
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)
    lead_score: Optional[int] = Field(default=0)
    notes: Optional[str] = Field(None, max_length=1000)


class ContactCreate(ContactBase):
    """Schema for creating contacts"""
    pass


class ContactUpdate(BaseModel):
    """Schema for updating contacts"""
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    full_name: Optional[str] = Field(None, max_length=255)
    company: Optional[str] = Field(None, max_length=255)
    job_title: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    website: Optional[str] = Field(None, max_length=255)
    linkedin_url: Optional[str] = Field(None, max_length=255)
    twitter_handle: Optional[str] = Field(None, max_length=100)

    # Address fields
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)

    # Email preferences
    email_verified: Optional[bool] = None
    accepts_marketing: Optional[bool] = None
    preferred_language: Optional[str] = Field(None, max_length=10)
    timezone: Optional[str] = Field(None, max_length=50)

    # Segmentation
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    lead_score: Optional[int] = None
    notes: Optional[str] = Field(None, max_length=1000)


class ContactBulkCreate(BaseModel):
    """Schema for bulk contact creation"""
    contacts: List[ContactCreate]
    skip_duplicates: bool = Field(default=True)
    update_existing: bool = Field(default=False)


class ContactBulkUpdate(BaseModel):
    """Schema for bulk contact updates"""
    contact_ids: List[int]
    status: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class ContactImport(BaseModel):
    """Schema for contact import"""
    file_data: str  # Base64 encoded CSV data
    mapping: Dict[str, str]  # Field mapping
    skip_duplicates: bool = Field(default=True)
    update_existing: bool = Field(default=False)


class ContactInDB(ContactBase):
    """Schema for contacts in database"""
    id: int
    user_id: int
    status: str
    source: str

    # Engagement data
    last_opened_at: Optional[datetime]
    last_clicked_at: Optional[datetime]
    last_replied_at: Optional[datetime]
    total_opens: int
    total_clicks: int
    total_replies: int

    # Timestamps
    created_at: datetime
    updated_at: datetime
    unsubscribed_at: Optional[datetime]

    model_config = {"from_attributes": True}


class Contact(ContactInDB):
    """Public contact schema"""
    pass


class ContactWithEngagement(Contact):
    """Contact with engagement metrics"""
    open_rate: float
    click_rate: float
    reply_rate: float
    engagement_score: float


class ContactStats(BaseModel):
    """Contact statistics schema"""
    status_counts: Dict[str, int]
    total_contacts: int
    total_sent: int
    total_opened: int
    total_clicked: int
    total_replied: int
    avg_open_rate: float
    avg_click_rate: float


class ContactList(BaseModel):
    """Paginated contact list"""
    contacts: List[Contact]
    total: int
    page: int
    size: int
    pages: int


class ContactSearchResult(BaseModel):
    """Contact search result"""
    contacts: List[Contact]
    total: int
    query: str
    suggestions: List[str] = Field(default_factory=list)


class ContactExport(BaseModel):
    """Contact export configuration"""
    contact_ids: Optional[List[int]] = None
    status: Optional[str] = None
    format: str = Field(default="csv")  # csv, xlsx, json
    fields: List[str] = Field(default_factory=list)
    include_engagement: bool = Field(default=True)


class ContactEngagementUpdate(BaseModel):
    """Schema for updating contact engagement"""
    emails_sent: int = 0
    emails_opened: int = 0
    emails_clicked: int = 0
    emails_replied: int = 0
    emails_bounced: int = 0


class ContactListAssignment(BaseModel):
    """Schema for assigning contacts to lists/campaigns"""
    contact_ids: List[int]
    list_id: Optional[int] = None
    campaign_id: Optional[int] = None
    action: str = Field(default="add")  # add, remove


class ContactDuplicate(BaseModel):
    """Contact duplicate information"""
    contact: Contact
    duplicates: List[Contact]
    merge_suggestions: Dict[str, Any]


class ContactMerge(BaseModel):
    """Contact merge configuration"""
    primary_contact_id: int
    duplicate_contact_ids: List[int]
    field_preferences: Dict[str, str]  # field -> contact_id to use
    merge_engagement: bool = Field(default=True)


class ContactValidation(BaseModel):
    """Contact validation result"""
    contact_id: int
    email_valid: bool
    email_deliverable: bool
    phone_valid: bool
    social_profiles_found: List[str]
    company_verified: bool
    validation_score: float
    issues: List[str] = Field(default_factory=list)


class ContactEnrichment(BaseModel):
    """Contact enrichment data"""
    contact_id: int
    enriched_fields: Dict[str, Any]
    confidence_scores: Dict[str, float]
    data_sources: List[str]
    enrichment_date: datetime
