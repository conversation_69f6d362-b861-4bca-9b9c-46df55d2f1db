"""
Email sequence schemas for API requests and responses
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class EmailSequenceBase(BaseModel):
    """Base email sequence schema"""
    name: str = Field(..., max_length=255)
    subject_line: str = Field(..., max_length=500)
    email_content: str = Field(..., min_length=1)
    delay_days: int = Field(default=0, ge=0, le=365)
    delay_hours: int = Field(default=0, ge=0, le=23)
    sequence_type: str = Field(default="follow_up")
    send_conditions: Dict[str, Any] = Field(default_factory=dict)
    personalization_fields: List[str] = Field(default_factory=list)
    ai_optimization_enabled: bool = Field(default=True)
    track_opens: bool = Field(default=True)
    track_clicks: bool = Field(default=True)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)

    model_config = {"from_attributes": True}


class EmailSequenceCreate(EmailSequenceBase):
    """Schema for creating email sequences"""
    campaign_id: int
    order: Optional[int] = None  # Auto-assigned if not provided


class EmailSequenceUpdate(BaseModel):
    """Schema for updating email sequences"""
    name: Optional[str] = Field(None, max_length=255)
    subject_line: Optional[str] = Field(None, max_length=500)
    email_content: Optional[str] = None
    delay_days: Optional[int] = Field(None, ge=0, le=365)
    delay_hours: Optional[int] = Field(None, ge=0, le=23)
    sequence_type: Optional[str] = None
    status: Optional[str] = None
    send_conditions: Optional[Dict[str, Any]] = None
    personalization_fields: Optional[List[str]] = None
    ai_optimization_enabled: Optional[bool] = None
    track_opens: Optional[bool] = None
    track_clicks: Optional[bool] = None
    custom_fields: Optional[Dict[str, Any]] = None


class EmailSequenceReorder(BaseModel):
    """Schema for reordering sequences"""
    sequence_orders: List[Dict[str, int]] = Field(
        ...,
        description="List of {id: sequence_id, order: new_order}"
    )


class EmailSequenceDuplicate(BaseModel):
    """Schema for duplicating sequences"""
    new_campaign_id: Optional[int] = None
    new_name: Optional[str] = None


class EmailSequenceInDB(EmailSequenceBase):
    """Schema for sequences in database"""
    id: int
    campaign_id: int
    order: int
    status: str
    
    # Statistics
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    last_sent_at: Optional[datetime]

    model_config = {"from_attributes": True}


class EmailSequence(EmailSequenceInDB):
    """Public email sequence schema"""
    pass


class EmailSequenceWithCampaign(EmailSequence):
    """Email sequence with campaign information"""
    campaign_name: str
    campaign_status: str


class EmailSequenceStats(BaseModel):
    """Email sequence statistics schema"""
    sequence_id: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    emails_bounced: int
    delivery_rate: float
    open_rate: float
    click_rate: float
    reply_rate: float
    bounce_rate: float
    last_sent_at: Optional[datetime]
    created_at: datetime


class EmailSequenceList(BaseModel):
    """Paginated email sequence list"""
    sequences: List[EmailSequence]
    total: int
    page: int
    size: int
    pages: int


class EmailSequenceSearchResult(BaseModel):
    """Email sequence search result"""
    sequences: List[EmailSequence]
    total: int
    query: str


class EmailSequenceTemplate(BaseModel):
    """Email sequence template"""
    name: str
    description: str
    category: str
    sequences: List[Dict[str, Any]]
    tags: List[str] = Field(default_factory=list)


class EmailSequencePreview(BaseModel):
    """Email sequence preview with personalization"""
    subject_line: str
    email_content: str
    personalized_subject: str
    personalized_content: str
    personalization_data: Dict[str, str]


class EmailSequenceValidation(BaseModel):
    """Email sequence validation result"""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)


class EmailSequenceAIOptimization(BaseModel):
    """AI optimization suggestions for sequences"""
    sequence_id: int
    optimized_subject_lines: List[str]
    optimized_content: str
    personalization_suggestions: List[str]
    send_time_recommendations: Dict[str, Any]
    confidence_score: float


class EmailSequenceBulkAction(BaseModel):
    """Bulk action on sequences"""
    sequence_ids: List[int]
    action: str = Field(..., description="activate, deactivate, delete, duplicate")
    target_campaign_id: Optional[int] = None  # For duplicate action


class EmailSequenceImport(BaseModel):
    """Import sequences from template or file"""
    campaign_id: int
    template_id: Optional[str] = None
    sequences_data: Optional[List[Dict[str, Any]]] = None
    replace_existing: bool = Field(default=False)


class EmailSequenceExport(BaseModel):
    """Export sequences configuration"""
    sequence_ids: Optional[List[int]] = None
    campaign_id: Optional[int] = None
    format: str = Field(default="json")  # json, csv, template
    include_stats: bool = Field(default=True)


class EmailSequenceAnalytics(BaseModel):
    """Comprehensive sequence analytics"""
    sequence_id: int
    performance_metrics: EmailSequenceStats
    engagement_timeline: List[Dict[str, Any]]
    best_performing_elements: Dict[str, Any]
    optimization_opportunities: List[str]
    comparison_data: Optional[Dict[str, Any]] = None


class EmailSequenceA_BTest(BaseModel):
    """A/B test configuration for sequences"""
    sequence_id: int
    test_type: str = Field(..., description="subject_line, content, send_time")
    variant_a: Dict[str, Any]
    variant_b: Dict[str, Any]
    test_percentage: int = Field(default=50, ge=10, le=90)
    duration_days: int = Field(default=7, ge=1, le=30)


class EmailSequencePersonalization(BaseModel):
    """Personalization configuration"""
    sequence_id: int
    personalization_rules: Dict[str, Any]
    fallback_values: Dict[str, str]
    dynamic_content_blocks: List[Dict[str, Any]]


class EmailSequenceSchedule(BaseModel):
    """Sequence scheduling configuration"""
    sequence_id: int
    send_immediately: bool = Field(default=False)
    scheduled_start: Optional[datetime] = None
    time_zone: str = Field(default="UTC")
    send_days: List[str] = Field(default_factory=lambda: ["monday", "tuesday", "wednesday", "thursday", "friday"])
    send_time_start: str = Field(default="09:00")
    send_time_end: str = Field(default="17:00")
    respect_recipient_timezone: bool = Field(default=True)
