"""
User schemas
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from app.models.user import User<PERSON><PERSON>, UserStatus


class UserBase(BaseModel):
    """Base user schema"""
    email: EmailStr
    username: Optional[str] = None
    full_name: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    bio: Optional[str] = None


class UserCreate(UserBase):
    """User creation schema"""
    password: str = Field(..., min_length=8)


class UserUpdate(BaseModel):
    """User update schema"""
    username: Optional[str] = None
    full_name: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    bio: Optional[str] = None
    default_from_name: Optional[str] = None
    default_from_email: Optional[EmailStr] = None
    default_reply_to: Optional[EmailStr] = None


class UserResponse(UserBase):
    """User response schema"""
    id: int
    is_active: bool
    is_verified: bool
    is_superuser: bool
    role: UserRole
    status: UserStatus
    subscription_plan: str
    monthly_email_limit: int
    emails_sent_this_month: int
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None
    email_verified_at: Optional[datetime] = None
    
    model_config = {"from_attributes": True}


class UserProfile(BaseModel):
    """User profile schema"""
    id: int
    email: EmailStr
    username: Optional[str] = None
    full_name: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    bio: Optional[str] = None
    is_verified: bool
    subscription_plan: str
    monthly_email_limit: int
    emails_sent_this_month: int
    remaining_emails: int
    created_at: datetime
    
    model_config = {"from_attributes": True}


class UserList(BaseModel):
    """User list response schema"""
    users: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int


class UserStats(BaseModel):
    """User statistics schema"""
    total_campaigns: int
    active_campaigns: int
    total_contacts: int
    emails_sent_today: int
    emails_sent_this_week: int
    emails_sent_this_month: int
    average_open_rate: float
    average_click_rate: float
    average_reply_rate: float
