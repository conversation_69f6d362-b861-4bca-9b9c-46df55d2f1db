"""
AI service for email generation and optimization using OpenAI
"""

import openai
from typing import Dict, List, Optional, Any
import logging
import json
import re

from app.core.config import settings
from app.core.exceptions import AIServiceError

logger = logging.getLogger(__name__)


class AIService:
    """AI service for email generation and optimization"""
    
    def __init__(self):
        # Initialize OpenAI client if API key is available
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-3.5-turbo')
            self.max_tokens = getattr(settings, 'OPENAI_MAX_TOKENS', 1000)
            self.ai_enabled = True
            logger.info("OpenAI client initialized successfully")
        else:
            self.client = None
            self.model = 'gpt-3.5-turbo'
            self.max_tokens = 1000
            self.ai_enabled = False
            logger.warning("OpenAI API key not found. AI features will use fallback responses.")
    
    async def generate_email_content(
        self,
        prompt: str,
        context: Optional[str] = None,
        tone: str = "professional",
        length: str = "medium",
        include_subject: bool = True,
        contact_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """
        Generate email content using AI
        
        Args:
            prompt: The main prompt for email generation
            context: Additional context about the recipient or campaign
            tone: Tone of the email (professional, casual, friendly, etc.)
            length: Length of the email (short, medium, long)
            include_subject: Whether to generate a subject line
            contact_data: Contact information for personalization
            
        Returns:
            Dict with generated subject and content
        """
        try:
            # Check if AI is enabled
            if not self.ai_enabled or not self.client:
                return self._get_fallback_email_content(prompt, tone)

            # Build the system prompt
            system_prompt = self._build_system_prompt(tone, length, include_subject)

            # Build the user prompt
            user_prompt = self._build_user_prompt(prompt, context, contact_data)

            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.7
            )

            # Parse response
            content = response.choices[0].message.content
            return self._parse_email_response(content, include_subject)

        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error: {str(e)}")
            return self._get_fallback_email_content(prompt, tone)

        except Exception as e:
            logger.error(f"Unexpected error in AI service: {str(e)}")
            return self._get_fallback_email_content(prompt, tone)
    
    async def optimize_subject_line(
        self,
        subject: str,
        context: Optional[str] = None,
        target_audience: Optional[str] = None
    ) -> List[str]:
        """
        Generate optimized subject line variations
        
        Args:
            subject: Original subject line
            context: Context about the email content
            target_audience: Information about the target audience
            
        Returns:
            List of optimized subject lines
        """
        try:
            # Check if AI is enabled
            if not self.ai_enabled or not self.client:
                return self._get_fallback_subject_lines(subject)

            system_prompt = """
You are an expert email marketer specializing in subject line optimization.
Generate 5 improved subject line variations that:
- Increase open rates
- Create curiosity or urgency
- Are personalized and relevant
- Follow email marketing best practices
- Are under 50 characters when possible

Return only the subject lines, one per line, without numbering or bullets.
            """

            user_prompt = f"""
Original subject: {subject}
Context: {context or 'General email outreach'}
Target audience: {target_audience or 'Business professionals'}

Generate 5 optimized subject line variations:
            """

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=200,
                temperature=0.8
            )

            content = response.choices[0].message.content
            subjects = [line.strip() for line in content.split('\n') if line.strip()]

            return subjects[:5]  # Return max 5 subjects

        except Exception as e:
            logger.error(f"Error optimizing subject line: {str(e)}")
            return self._get_fallback_subject_lines(subject)
    
    async def personalize_email(
        self,
        template: str,
        contact_data: Dict[str, Any],
        personalization_level: str = "medium"
    ) -> str:
        """
        Personalize email content using contact data
        
        Args:
            template: Email template with placeholders
            contact_data: Contact information for personalization
            personalization_level: Level of personalization (low, medium, high)
            
        Returns:
            Personalized email content
        """
        try:
            # Basic placeholder replacement
            personalized = template
            
            # Replace common placeholders
            replacements = {
                "{{first_name}}": contact_data.get("first_name", ""),
                "{{last_name}}": contact_data.get("last_name", ""),
                "{{full_name}}": contact_data.get("full_name", ""),
                "{{company}}": contact_data.get("company", ""),
                "{{job_title}}": contact_data.get("job_title", ""),
                "{{website}}": contact_data.get("website", ""),
            }
            
            for placeholder, value in replacements.items():
                personalized = personalized.replace(placeholder, value or "")
            
            # AI-powered personalization for medium/high levels
            if personalization_level in ["medium", "high"] and any(contact_data.values()):
                system_prompt = f"""
You are an expert at personalizing email content. 
Personalization level: {personalization_level}
- Low: Basic name/company insertion
- Medium: Add relevant context and industry insights
- High: Deep personalization with specific references

Enhance the email with natural, relevant personalization based on the contact data.
Keep the core message intact but make it more personal and engaging.
                """
                
                user_prompt = f"""
Email content to personalize:
{personalized}

Contact data:
{json.dumps(contact_data, indent=2)}

Return the personalized email content:
                """
                
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=self.max_tokens,
                    temperature=0.6
                )
                
                personalized = response.choices[0].message.content
            
            return personalized
            
        except Exception as e:
            logger.error(f"Error personalizing email: {str(e)}")
            raise AIServiceError(f"Personalization error: {str(e)}")
    
    async def analyze_email_performance(
        self,
        email_content: str,
        performance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze email performance and provide insights
        
        Args:
            email_content: The email content to analyze
            performance_data: Performance metrics (open rate, click rate, etc.)
            
        Returns:
            Analysis insights and recommendations
        """
        try:
            system_prompt = """
You are an email marketing analyst. Analyze the email content and performance data to provide:
1. Performance assessment (good/average/poor)
2. Key insights about what worked or didn't work
3. Specific recommendations for improvement
4. A/B testing suggestions

Return your analysis in JSON format with keys: assessment, insights, recommendations, ab_tests
            """
            
            user_prompt = f"""
Email content:
{email_content}

Performance data:
{json.dumps(performance_data, indent=2)}

Provide analysis and recommendations:
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.5
            )
            
            content = response.choices[0].message.content
            
            # Try to parse as JSON, fallback to structured text
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                return {
                    "assessment": "Analysis completed",
                    "insights": content,
                    "recommendations": [],
                    "ab_tests": []
                }
            
        except Exception as e:
            logger.error(f"Error analyzing email performance: {str(e)}")
            raise AIServiceError(f"Performance analysis error: {str(e)}")
    
    def _build_system_prompt(self, tone: str, length: str, include_subject: bool) -> str:
        """Build system prompt for email generation"""
        prompt = f"""
You are an expert email copywriter specializing in business outreach.
Generate high-converting email content with the following specifications:

Tone: {tone}
Length: {length} (short: 50-100 words, medium: 100-200 words, long: 200-300 words)
        """
        
        if include_subject:
            prompt += """
Format your response as:
SUBJECT: [subject line]
CONTENT: [email content]
            """
        else:
            prompt += """
Generate only the email content, no subject line.
            """
        
        prompt += """
Best practices:
- Clear and compelling value proposition
- Personalized when possible
- Strong call-to-action
- Professional but engaging tone
- Mobile-friendly formatting
        """
        
        return prompt
    
    def _build_user_prompt(
        self, 
        prompt: str, 
        context: Optional[str], 
        contact_data: Optional[Dict[str, Any]]
    ) -> str:
        """Build user prompt with context and contact data"""
        user_prompt = f"Email request: {prompt}"
        
        if context:
            user_prompt += f"\n\nAdditional context: {context}"
        
        if contact_data:
            user_prompt += f"\n\nContact information for personalization:\n{json.dumps(contact_data, indent=2)}"
        
        return user_prompt
    
    def _parse_email_response(self, content: str, include_subject: bool) -> Dict[str, str]:
        """Parse AI response into subject and content"""
        if include_subject:
            # Look for SUBJECT: and CONTENT: markers
            subject_match = re.search(r'SUBJECT:\s*(.+)', content, re.IGNORECASE)
            content_match = re.search(r'CONTENT:\s*(.+)', content, re.IGNORECASE | re.DOTALL)
            
            subject = subject_match.group(1).strip() if subject_match else "Generated Email"
            email_content = content_match.group(1).strip() if content_match else content
            
            return {
                "subject": subject,
                "content": email_content
            }
        else:
            return {
                "subject": "",
                "content": content.strip()
            }

    def _get_fallback_email_content(self, prompt: str, tone: str) -> Dict[str, str]:
        """Provide fallback email content when AI is unavailable"""
        templates = {
            "professional": {
                "subject": "Following up on our conversation",
                "content": """Hi {{first_name}},

I hope this email finds you well. I wanted to follow up on our previous conversation about {{company}}.

We specialize in helping companies like yours achieve their goals through innovative solutions.

Would you be available for a brief call this week to discuss how we might be able to help?

Best regards,
{{from_name}}"""
            },
            "casual": {
                "subject": "Quick question about {{company}}",
                "content": """Hey {{first_name}},

Hope you're doing well! I came across {{company}} and was impressed by what you're doing.

I think there might be some interesting opportunities for us to work together.

Would you be up for a quick chat sometime this week?

Cheers,
{{from_name}}"""
            },
            "friendly": {
                "subject": "Great to connect, {{first_name}}!",
                "content": """Hi {{first_name}},

It's great to connect with you! I've been following {{company}}'s progress and I'm really impressed.

I'd love to learn more about your current challenges and see if there's a way we can help.

Looking forward to hearing from you!

Best,
{{from_name}}"""
            }
        }

        template = templates.get(tone, templates["professional"])
        return template

    def _get_fallback_subject_lines(self, original_subject: str) -> List[str]:
        """Provide fallback subject line variations"""
        variations = [
            f"Re: {original_subject}",
            f"Quick question: {original_subject}",
            f"Following up: {original_subject}",
            f"{{first_name}}, {original_subject.lower()}",
            f"Important: {original_subject}"
        ]
        return variations


# Create global AI service instance
ai_service = AIService()
