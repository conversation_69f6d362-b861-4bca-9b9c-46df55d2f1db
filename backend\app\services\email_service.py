"""
Email service for sending emails via SMTP with AI integration and campaign management
"""

import asyncio
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timezone
import re
import logging
from pathlib import Path
from jinja2 import Template

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.config import settings
from app.core.exceptions import EmailDeliveryError

logger = logging.getLogger(__name__)


class EmailService:
    """Email service for sending emails via SMTP"""
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_use_tls = settings.SMTP_USE_TLS
        self.default_from_email = settings.DEFAULT_FROM_EMAIL
        self.default_from_name = settings.DEFAULT_FROM_NAME
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        content: str,
        content_html: Optional[str] = None,
        from_email: Optional[str] = None,
        from_name: Optional[str] = None,
        reply_to: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None,
        tracking_pixel: bool = True,
        message_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send an email via SMTP
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            content: Plain text content
            content_html: HTML content (optional)
            from_email: Sender email (optional, uses default)
            from_name: Sender name (optional, uses default)
            reply_to: Reply-to email (optional)
            attachments: List of attachments (optional)
            tracking_pixel: Whether to include tracking pixel
            message_id: Custom message ID for tracking
            
        Returns:
            Dict with send status and message ID
        """
        try:
            # Use defaults if not provided
            sender_email = from_email or self.default_from_email
            sender_name = from_name or self.default_from_name
            
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{sender_name} <{sender_email}>"
            message["To"] = to_email
            
            if reply_to:
                message["Reply-To"] = reply_to
            
            if message_id:
                message["Message-ID"] = message_id
            
            # Add plain text content
            text_part = MIMEText(content, "plain")
            message.attach(text_part)
            
            # Add HTML content if provided
            if content_html:
                # Add tracking pixel if enabled
                if tracking_pixel and message_id:
                    tracking_url = f"{settings.SERVER_HOST}:{settings.SERVER_PORT}/api/v1/tracking/open/{message_id}"
                    tracking_img = f'<img src="{tracking_url}" width="1" height="1" style="display:none;" />'
                    content_html += tracking_img
                
                html_part = MIMEText(content_html, "html")
                message.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    self._add_attachment(message, attachment)
            
            # Send email
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls(context=context)
                
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(message)
            
            logger.info(f"Email sent successfully to {to_email}")
            
            return {
                "status": "sent",
                "message_id": message_id or message["Message-ID"],
                "to_email": to_email,
                "subject": subject
            }
            
        except smtplib.SMTPException as e:
            logger.error(f"SMTP error sending email to {to_email}: {str(e)}")
            raise EmailDeliveryError(f"Failed to send email: {str(e)}")
        
        except Exception as e:
            logger.error(f"Unexpected error sending email to {to_email}: {str(e)}")
            raise EmailDeliveryError(f"Unexpected error: {str(e)}")
    
    def _add_attachment(self, message: MIMEMultipart, attachment: Dict[str, Any]) -> None:
        """Add attachment to email message"""
        try:
            filename = attachment.get("filename")
            content = attachment.get("content")
            content_type = attachment.get("content_type", "application/octet-stream")
            
            if not filename or not content:
                logger.warning("Invalid attachment data, skipping")
                return
            
            part = MIMEBase(*content_type.split("/"))
            part.set_payload(content)
            encoders.encode_base64(part)
            
            part.add_header(
                "Content-Disposition",
                f"attachment; filename= {filename}",
            )
            
            message.attach(part)
            
        except Exception as e:
            logger.error(f"Error adding attachment {filename}: {str(e)}")
    
    async def send_bulk_emails(
        self,
        emails: List[Dict[str, Any]],
        batch_size: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Send multiple emails in batches
        
        Args:
            emails: List of email data dictionaries
            batch_size: Number of emails to send per batch
            
        Returns:
            List of send results
        """
        results = []
        
        for i in range(0, len(emails), batch_size):
            batch = emails[i:i + batch_size]
            
            for email_data in batch:
                try:
                    result = await self.send_email(**email_data)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "status": "failed",
                        "error": str(e),
                        "to_email": email_data.get("to_email"),
                        "subject": email_data.get("subject")
                    })
        
        return results
    
    async def send_template_email(
        self,
        template_name: str,
        to_email: str,
        template_data: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send email using a template
        
        Args:
            template_name: Name of the email template
            to_email: Recipient email
            template_data: Data to populate template
            **kwargs: Additional email parameters
            
        Returns:
            Send result
        """
        # TODO: Implement template rendering
        # For now, use basic string formatting
        
        templates = {
            "welcome": {
                "subject": "Welcome to AI Email Outreach Tool!",
                "content": """
Hello {name},

Welcome to AI Email Outreach Tool! We're excited to have you on board.

Your account has been successfully created and you can now start creating powerful email campaigns.

Best regards,
The AI Email Outreach Team
                """,
                "content_html": """
<html>
<body>
    <h2>Welcome to AI Email Outreach Tool!</h2>
    <p>Hello {name},</p>
    <p>Welcome to AI Email Outreach Tool! We're excited to have you on board.</p>
    <p>Your account has been successfully created and you can now start creating powerful email campaigns.</p>
    <p>Best regards,<br>The AI Email Outreach Team</p>
</body>
</html>
                """
            },
            "password_reset": {
                "subject": "Password Reset Request",
                "content": """
Hello,

You requested a password reset for your AI Email Outreach Tool account.

Click the link below to reset your password:
{reset_link}

If you didn't request this, please ignore this email.

Best regards,
The AI Email Outreach Team
                """,
                "content_html": """
<html>
<body>
    <h2>Password Reset Request</h2>
    <p>Hello,</p>
    <p>You requested a password reset for your AI Email Outreach Tool account.</p>
    <p><a href="{reset_link}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
    <p>If you didn't request this, please ignore this email.</p>
    <p>Best regards,<br>The AI Email Outreach Team</p>
</body>
</html>
                """
            }
        }
        
        template = templates.get(template_name)
        if not template:
            raise EmailDeliveryError(f"Template '{template_name}' not found")
        
        # Format template with data
        subject = template["subject"].format(**template_data)
        content = template["content"].format(**template_data)
        content_html = template["content_html"].format(**template_data)
        
        return await self.send_email(
            to_email=to_email,
            subject=subject,
            content=content,
            content_html=content_html,
            **kwargs
        )

    async def send_sequence_email(
        self,
        db: AsyncSession,
        sequence_id: int,
        contact_id: int,
        campaign_id: int,
        personalization_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Send a sequence email to a contact with AI optimization"""
        try:
            # Import here to avoid circular imports
            from app.crud.sequence import sequence_crud
            from app.crud.contact import contact_crud
            from app.crud.campaign import campaign_crud

            # Get sequence, contact, and campaign
            sequence = await sequence_crud.get(db, id=sequence_id)
            contact = await contact_crud.get(db, id=contact_id)
            campaign = await campaign_crud.get(db, id=campaign_id)

            if not sequence or not contact or not campaign:
                raise EmailDeliveryError("Sequence, contact, or campaign not found")

            # Prepare personalization data
            if personalization_data is None:
                personalization_data = self._prepare_personalization_data(contact, campaign)

            # Apply personalization to subject and content
            subject = self._apply_personalization(sequence.subject, personalization_data)
            content = self._apply_personalization(sequence.content, personalization_data)

            # Convert content to HTML
            content_html = self._convert_to_html(content)

            # Send email
            result = await self.send_email(
                to_email=contact.email,
                subject=subject,
                content=content,
                content_html=content_html,
                from_email=campaign.from_email,
                from_name=campaign.from_name,
                reply_to=campaign.reply_to_email,
                message_id=f"seq-{sequence_id}-contact-{contact_id}-{int(datetime.now().timestamp())}"
            )

            # Update statistics if successful
            if result["status"] == "sent":
                await sequence_crud.update_sequence_stats(
                    db, sequence_id=sequence_id, emails_sent=1
                )
                await contact_crud.update_engagement_stats(
                    db, contact_id=contact_id, emails_sent=1
                )

            return result

        except Exception as e:
            logger.error(f"Failed to send sequence email: {str(e)}")
            raise EmailDeliveryError(f"Failed to send sequence email: {str(e)}")

    async def send_campaign_batch(
        self,
        db: AsyncSession,
        campaign_id: int,
        sequence_id: int,
        contact_ids: List[int],
        batch_size: int = 10
    ) -> Dict[str, Any]:
        """Send emails to multiple contacts in a campaign sequence"""
        results = {
            "total_contacts": len(contact_ids),
            "sent": 0,
            "failed": 0,
            "errors": []
        }

        # Process contacts in batches
        for i in range(0, len(contact_ids), batch_size):
            batch = contact_ids[i:i + batch_size]

            for contact_id in batch:
                try:
                    result = await self.send_sequence_email(
                        db, sequence_id, contact_id, campaign_id
                    )

                    if result["status"] == "sent":
                        results["sent"] += 1
                    else:
                        results["failed"] += 1
                        results["errors"].append(f"Contact {contact_id}: {result.get('error', 'Unknown error')}")

                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"Contact {contact_id}: {str(e)}")

            # Rate limiting - wait between batches
            if i + batch_size < len(contact_ids):
                await asyncio.sleep(1)  # 1 second between batches

        return results

    def _prepare_personalization_data(self, contact, campaign) -> Dict[str, Any]:
        """Prepare personalization data for email templates"""
        return {
            "first_name": contact.first_name or "there",
            "last_name": contact.last_name or "",
            "full_name": f"{contact.first_name or ''} {contact.last_name or ''}".strip() or "there",
            "email": contact.email,
            "company": contact.company or "",
            "job_title": contact.job_title or "",
            "phone": contact.phone or "",
            "website": contact.website or "",
            "city": contact.city or "",
            "state": contact.state or "",
            "country": contact.country or "",
            "campaign_name": campaign.name,
            "from_name": campaign.from_name,
            "from_email": campaign.from_email,
            "sender_name": campaign.from_name,
            "unsubscribe_url": f"{settings.FRONTEND_URL}/unsubscribe?contact={contact.id}&campaign={campaign.id}",
            "custom_fields": contact.custom_fields or {}
        }

    def _apply_personalization(self, template_text: str, data: Dict[str, Any]) -> str:
        """Apply personalization to email template using Jinja2"""
        try:
            template = Template(template_text)
            return template.render(**data)
        except Exception as e:
            logger.warning(f"Jinja2 personalization failed: {str(e)}")
            # Fallback to simple string replacement
            result = template_text
            for key, value in data.items():
                if isinstance(value, str):
                    result = result.replace(f"{{{{{key}}}}}", value)
            return result

    def _convert_to_html(self, content: str) -> str:
        """Convert plain text content to HTML"""
        # Simple conversion - replace newlines with <br> tags
        html_content = content.replace('\n', '<br>\n')

        # Wrap in basic HTML structure
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            {html_content}
        </body>
        </html>
        """

    async def send_test_email(
        self,
        to_email: str,
        subject: str,
        content: str,
        from_email: Optional[str] = None,
        from_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send a test email"""
        # Add test prefix
        test_subject = f"[TEST] {subject}"

        # Convert to HTML
        content_html = self._convert_to_html(content)

        return await self.send_email(
            to_email=to_email,
            subject=test_subject,
            content=content,
            content_html=content_html,
            from_email=from_email,
            from_name=from_name
        )


# Create global email service instance
email_service = EmailService()
