"""
Supabase service for additional Supabase-specific functionality
"""

from typing import Optional, Dict, Any, List
import logging
from supabase import create_client, Client
from app.core.config import settings

logger = logging.getLogger(__name__)


class SupabaseService:
    """Service for Supabase-specific operations"""
    
    def __init__(self):
        self.url = settings.SUPABASE_URL
        self.anon_key = settings.SUPABASE_ANON_KEY
        self.service_role_key = settings.SUPABASE_SERVICE_ROLE_KEY
        
        self.client: Optional[Client] = None
        self.admin_client: Optional[Client] = None
        
        if self.url and self.anon_key:
            try:
                # Client for public operations (with RLS)
                self.client = create_client(self.url, self.anon_key)
                
                # Admin client for service operations (bypasses RLS)
                if self.service_role_key:
                    self.admin_client = create_client(self.url, self.service_role_key)
                
                logger.info("Supabase clients initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase clients: {e}")
        else:
            logger.warning("Supabase credentials not configured")
    
    @property
    def is_configured(self) -> bool:
        """Check if Supabase is properly configured"""
        return self.client is not None
    
    async def authenticate_user(self, email: str, password: str) -> Dict[str, Any]:
        """Authenticate user with Supabase Auth"""
        if not self.client:
            raise ValueError("Supabase client not configured")
        
        try:
            response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            return {
                "user": response.user,
                "session": response.session,
                "access_token": response.session.access_token if response.session else None
            }
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise
    
    async def register_user(self, email: str, password: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Register new user with Supabase Auth"""
        if not self.client:
            raise ValueError("Supabase client not configured")
        
        try:
            response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": metadata or {}
                }
            })
            return {
                "user": response.user,
                "session": response.session
            }
        except Exception as e:
            logger.error(f"Registration failed: {e}")
            raise
    
    async def get_user_by_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Get user information by JWT token"""
        if not self.client:
            return None
        
        try:
            response = self.client.auth.get_user(token)
            return response.user
        except Exception as e:
            logger.error(f"Failed to get user by token: {e}")
            return None
    
    async def create_record(self, table: str, data: Dict[str, Any], use_admin: bool = False) -> Dict[str, Any]:
        """Create a new record in Supabase table"""
        client = self.admin_client if use_admin and self.admin_client else self.client
        if not client:
            raise ValueError("Supabase client not configured")
        
        try:
            response = client.table(table).insert(data).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"Failed to create record in {table}: {e}")
            raise
    
    async def get_records(self, table: str, filters: Dict[str, Any] = None, 
                         limit: int = None, offset: int = None, 
                         use_admin: bool = False) -> List[Dict[str, Any]]:
        """Get records from Supabase table"""
        client = self.admin_client if use_admin and self.admin_client else self.client
        if not client:
            raise ValueError("Supabase client not configured")
        
        try:
            query = client.table(table).select("*")
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            if limit:
                query = query.limit(limit)
            
            if offset:
                query = query.offset(offset)
            
            response = query.execute()
            return response.data
        except Exception as e:
            logger.error(f"Failed to get records from {table}: {e}")
            raise
    
    async def update_record(self, table: str, record_id: Any, data: Dict[str, Any],
                           use_admin: bool = False) -> Dict[str, Any]:
        """Update a record in Supabase table"""
        client = self.admin_client if use_admin and self.admin_client else self.client
        if not client:
            raise ValueError("Supabase client not configured")

        try:
            logger.info(f"🔄 Supabase update_record: table={table}, id={record_id}, data={data}")
            response = client.table(table).update(data).eq("id", record_id).execute()
            logger.info(f"✅ Supabase update response: {response.data}")
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"❌ Failed to update record in {table}: {e}")
            raise
    
    async def delete_record(self, table: str, record_id: Any, use_admin: bool = False) -> bool:
        """Delete a record from Supabase table"""
        client = self.admin_client if use_admin and self.admin_client else self.client
        if not client:
            raise ValueError("Supabase client not configured")
        
        try:
            response = client.table(table).delete().eq("id", record_id).execute()
            return len(response.data) > 0
        except Exception as e:
            logger.error(f"Failed to delete record from {table}: {e}")
            raise
    
    async def upload_file(self, bucket: str, file_path: str, file_data: bytes, 
                         content_type: str = None) -> str:
        """Upload file to Supabase Storage"""
        if not self.client:
            raise ValueError("Supabase client not configured")
        
        try:
            response = self.client.storage.from_(bucket).upload(
                file_path, file_data, {"content-type": content_type}
            )
            return f"{self.url}/storage/v1/object/public/{bucket}/{file_path}"
        except Exception as e:
            logger.error(f"Failed to upload file: {e}")
            raise
    
    async def subscribe_to_changes(self, table: str, callback, event: str = "*"):
        """Subscribe to real-time changes"""
        if not self.client:
            raise ValueError("Supabase client not configured")
        
        try:
            self.client.table(table).on(event, callback).subscribe()
        except Exception as e:
            logger.error(f"Failed to subscribe to {table}: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """Test Supabase connection"""
        if not self.client:
            return False
        
        try:
            # Try a simple query to test connection
            response = self.client.table('users').select('count', count='exact').execute()
            logger.info("Supabase connection test successful")
            return True
        except Exception as e:
            logger.error(f"Supabase connection test failed: {e}")
            return False


# Global instance
supabase_service = SupabaseService()
