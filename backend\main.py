"""
Main FastAPI application entry point for AI Email Outreach Tool
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router
from app.core.exceptions import setup_exception_handlers


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting AI Email Outreach Tool API...")

    # Import all models to ensure they are registered with SQLAlchemy
    from app.models import user, campaign, contact, sequence, analytics  # noqa
    from app.core.database_utils import db_manager

    try:
        # Test database connection first
        from app.core.database_utils import test_supabase_connection

        print("🔍 Testing Supabase connection...")
        connection_ok = await test_supabase_connection()

        if connection_ok:
            # Create database tables
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            print("✅ Database tables created")
            print("🗄️  Database: SUPABASE POSTGRESQL (healthy)")
        else:
            print("❌ Supabase connection failed")
            print("🔄 Application will start but database operations will fail")

        # Check database health
        db_health = await db_manager.health_check()
        if settings.SUPABASE_URL:
            print(f"🔗 Supabase: {'Configured' if db_health.get('supabase_configured') else 'Not configured'}")

    except Exception as e:
        print(f"⚠️  Database initialization warning: {e}")
        print("🔄 Application will continue with limited functionality")

    print(f"🌐 API running on: {settings.SERVER_HOST}:{settings.SERVER_PORT}")
    print(f"📚 API docs available at: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}/docs")

    yield

    # Shutdown
    print("🛑 Shutting down AI Email Outreach Tool API...")
    try:
        await engine.dispose()
        print("✅ Database connections closed")
    except Exception as e:
        print(f"⚠️  Database cleanup warning: {e}")


# Create FastAPI application
app = FastAPI(
    title="AI Email Outreach Tool API",
    description="""
    A comprehensive API for AI-powered email outreach campaigns.
    
    ## Features
    
    * **User Management**: Registration, authentication, and profile management
    * **Campaign Management**: Create, manage, and track email campaigns
    * **Contact Management**: Import, organize, and segment contacts
    * **AI Integration**: Generate and optimize email content with AI
    * **Email Infrastructure**: Send, track, and analyze email performance
    * **Analytics**: Comprehensive reporting and insights
    
    ## Authentication
    
    This API uses JWT tokens for authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# Setup exception handlers
setup_exception_handlers(app)

# Include API router
app.include_router(api_router, prefix="/api/v1")


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Email Outreach Tool API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc",
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    from app.core.database_utils import db_manager

    # Get database health
    db_health = await db_manager.health_check()

    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "version": "1.0.0",
        "database": db_health,
        "supabase_configured": bool(settings.SUPABASE_URL and settings.SUPABASE_ANON_KEY),
        "environment": settings.ENVIRONMENT
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.SERVER_HOST,
        port=settings.SERVER_PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
