#!/usr/bin/env python3
"""
AI Email Outreach Tool API - Using Supabase REST API
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import uvicorn

from app.core.config import settings
from app.services.supabase_service import supabase_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting AI Email Outreach Tool API (Supabase REST API Mode)...")
    
    # Test Supabase connection
    try:
        if supabase_service.is_configured:
            connection_ok = await supabase_service.test_connection()
            if connection_ok:
                print("✅ Supabase REST API connection successful!")
            else:
                print("⚠️  Supabase REST API connection failed, but continuing...")
        else:
            print("⚠️  Supabase not configured, using limited functionality")
    except Exception as e:
        print(f"⚠️  Supabase test warning: {e}")
    
    print(f"🌐 API running on: {settings.SERVER_HOST}:{settings.SERVER_PORT}")
    print(f"📚 API docs available at: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}/docs")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down AI Email Outreach Tool API...")


# Create FastAPI application
app = FastAPI(
    title="AI Email Outreach Tool API",
    description="A comprehensive API for AI-powered email outreach campaigns using Supabase.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Email Outreach Tool API",
        "version": "1.0.0",
        "status": "running",
        "mode": "supabase_rest_api",
        "docs": "/docs",
        "redoc": "/redoc",
        "supabase_configured": supabase_service.is_configured
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    
    # Test Supabase connection
    supabase_status = "not_configured"
    if supabase_service.is_configured:
        try:
            connection_ok = await supabase_service.test_connection()
            supabase_status = "healthy" if connection_ok else "unhealthy"
        except Exception:
            supabase_status = "error"
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "version": "1.0.0",
        "mode": "supabase_rest_api",
        "database": {
            "provider": "supabase",
            "type": "rest_api",
            "status": supabase_status
        },
        "supabase_configured": supabase_service.is_configured,
        "environment": settings.ENVIRONMENT
    }


@app.get("/test-supabase", tags=["Test"])
async def test_supabase():
    """Test Supabase functionality"""
    if not supabase_service.is_configured:
        return {
            "configured": False,
            "error": "Supabase not configured"
        }
    
    try:
        # Test connection
        connection_ok = await supabase_service.test_connection()
        
        # Test table access
        tables_tested = {}
        for table in ['users', 'campaigns', 'contacts']:
            try:
                records = await supabase_service.get_records(table, limit=1)
                tables_tested[table] = "accessible"
            except Exception as e:
                tables_tested[table] = f"error: {str(e)[:50]}"
        
        return {
            "configured": True,
            "connection": "success" if connection_ok else "failed",
            "tables": tables_tested,
            "supabase_url": settings.SUPABASE_URL
        }
    except Exception as e:
        return {
            "configured": True,
            "connection": "error",
            "error": str(e),
            "supabase_url": settings.SUPABASE_URL
        }


# Basic API endpoints using Supabase REST API
@app.get("/api/users", tags=["Users"])
async def get_users(limit: int = 10, offset: int = 0):
    """Get users from Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        users = await supabase_service.get_records(
            "users", 
            limit=limit, 
            use_admin=True
        )
        return {"users": users, "count": len(users)}
    except Exception as e:
        return {"error": str(e)}


@app.post("/api/users", tags=["Users"])
async def create_user(user_data: dict):
    """Create a new user in Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        user = await supabase_service.create_record(
            "users", 
            user_data, 
            use_admin=True
        )
        return {"user": user, "message": "User created successfully"}
    except Exception as e:
        return {"error": str(e)}


@app.get("/api/campaigns", tags=["Campaigns"])
async def get_campaigns(limit: int = 10, offset: int = 0):
    """Get campaigns from Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        campaigns = await supabase_service.get_records(
            "campaigns", 
            limit=limit,
            use_admin=True
        )
        return {"campaigns": campaigns, "count": len(campaigns)}
    except Exception as e:
        return {"error": str(e)}


@app.post("/api/campaigns", tags=["Campaigns"])
async def create_campaign(campaign_data: dict):
    """Create a new campaign in Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        campaign = await supabase_service.create_record(
            "campaigns", 
            campaign_data,
            use_admin=True
        )
        return {"campaign": campaign, "message": "Campaign created successfully"}
    except Exception as e:
        return {"error": str(e)}


@app.get("/api/contacts", tags=["Contacts"])
async def get_contacts(limit: int = 10, offset: int = 0):
    """Get contacts from Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        contacts = await supabase_service.get_records(
            "contacts", 
            limit=limit,
            use_admin=True
        )
        return {"contacts": contacts, "count": len(contacts)}
    except Exception as e:
        return {"error": str(e)}


@app.post("/api/contacts", tags=["Contacts"])
async def create_contact(contact_data: dict):
    """Create a new contact in Supabase"""
    if not supabase_service.is_configured:
        return {"error": "Supabase not configured"}
    
    try:
        contact = await supabase_service.create_record(
            "contacts", 
            contact_data,
            use_admin=True
        )
        return {"contact": contact, "message": "Contact created successfully"}
    except Exception as e:
        return {"error": str(e)}


if __name__ == "__main__":
    print("🚀 Starting AI Email Outreach Tool API (Supabase REST API Mode)...")
    print(f"🌐 API will run on: {settings.SERVER_HOST}:{settings.SERVER_PORT}")
    print(f"📚 API docs available at: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}/docs")
    print("🔗 Using Supabase REST API for database operations")
    
    uvicorn.run(
        "main_supabase_api:app",
        host=settings.SERVER_HOST,
        port=settings.SERVER_PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
