# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM - Supabase PostgreSQL
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0  # Required for Supabase PostgreSQL async connections

# Supabase integration
supabase==2.0.2
postgrest-py==0.13.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Task queue and background jobs
celery==5.3.4
redis==5.0.1

# Email handling
email-validator==2.1.0
aiosmtplib==3.0.1
imapclient==2.3.1

# HTTP client for API calls
httpx>=0.28.0
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI and OpenRouter integration
openai>=1.50.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
