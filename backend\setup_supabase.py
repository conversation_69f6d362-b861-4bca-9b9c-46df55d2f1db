#!/usr/bin/env python3
"""
Supabase setup and configuration script
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database_utils import test_supabase_connection, db_manager
from app.services.supabase_service import supabase_service


async def test_supabase_connection():
    """Test Supabase database connection"""
    print("🔍 Testing Supabase Database Connection...")
    print("=" * 50)
    
    # Check if credentials are configured
    if not settings.SUPABASE_URL:
        print("❌ SUPABASE_URL not configured")
        return False
    
    if not settings.SUPABASE_ANON_KEY:
        print("❌ SUPABASE_ANON_KEY not configured")
        return False
    
    print(f"📡 Supabase URL: {settings.SUPABASE_URL}")
    print(f"🔑 Anon Key: {settings.SUPABASE_ANON_KEY[:20]}...")
    
    # Test database connection
    print("\n🗄️  Testing database connection...")
    db_success = await test_supabase_connection()
    
    if db_success:
        print("✅ Database connection successful!")
    else:
        print("❌ Database connection failed!")
        return False
    
    # Test Supabase client
    print("\n🔌 Testing Supabase client...")
    client_success = await supabase_service.test_connection()
    
    if client_success:
        print("✅ Supabase client connection successful!")
    else:
        print("❌ Supabase client connection failed!")
    
    return db_success and client_success


async def setup_supabase_schema():
    """Set up Supabase database schema"""
    print("\n🏗️  Setting up Supabase schema...")
    
    try:
        from app.core.database_utils import ensure_database_schema
        success = await ensure_database_schema()
        
        if success:
            print("✅ Schema setup successful!")
        else:
            print("❌ Schema setup failed!")
        
        return success
    except Exception as e:
        print(f"❌ Schema setup error: {e}")
        return False


async def verify_supabase_tables():
    """Verify that all required tables exist"""
    print("\n📋 Verifying Supabase tables...")
    
    required_tables = [
        'users', 'campaigns', 'contacts', 'email_sequences',
        'campaign_contacts', 'email_logs', 'daily_stats'
    ]
    
    try:
        if supabase_service.is_configured:
            for table in required_tables:
                try:
                    # Try to query each table
                    response = await supabase_service.get_records(table, limit=1, use_admin=True)
                    print(f"  ✅ {table} - OK")
                except Exception as e:
                    print(f"  ❌ {table} - Error: {e}")
                    return False
            
            print("✅ All tables verified!")
            return True
        else:
            print("❌ Supabase client not configured")
            return False
    except Exception as e:
        print(f"❌ Table verification error: {e}")
        return False


async def switch_to_supabase():
    """Switch the application to use Supabase"""
    print("\n🔄 Switching to Supabase...")
    
    # Update .env file
    env_file = backend_dir / ".env"
    
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
        
        # Comment out SQLite and uncomment Supabase
        content = content.replace(
            "DATABASE_URL=sqlite+aiosqlite:///./email_outreach.db",
            "# DATABASE_URL=sqlite+aiosqlite:///./email_outreach.db"
        )
        content = content.replace(
            "# DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres",
            "DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
        )
        content = content.replace(
            "USE_SUPABASE_DATABASE=false",
            "USE_SUPABASE_DATABASE=true"
        )
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ Environment configuration updated!")
        print("🔄 Please restart your backend server to apply changes.")
        return True
    else:
        print("❌ .env file not found!")
        return False


async def main():
    """Main setup function"""
    print("🚀 Supabase Setup and Configuration")
    print("=" * 50)
    
    # Test connection
    connection_success = await test_supabase_connection()
    
    if not connection_success:
        print("\n❌ Supabase connection failed!")
        print("\n🔧 Troubleshooting steps:")
        print("1. Check your Supabase project is active")
        print("2. Verify credentials in .env file")
        print("3. Ensure your IP is whitelisted (if applicable)")
        print("4. Check network connectivity")
        return False
    
    # Set up schema
    schema_success = await setup_supabase_schema()
    
    if not schema_success:
        print("\n❌ Schema setup failed!")
        return False
    
    # Verify tables
    tables_success = await verify_supabase_tables()
    
    if not tables_success:
        print("\n❌ Table verification failed!")
        return False
    
    # Ask user if they want to switch
    print("\n" + "=" * 50)
    print("🎉 Supabase is ready!")
    
    switch = input("\nDo you want to switch your application to use Supabase now? (y/N): ").lower().strip()
    
    if switch in ['y', 'yes']:
        switch_success = await switch_to_supabase()
        if switch_success:
            print("\n🎉 Successfully configured to use Supabase!")
            print("Your application is now ready to use Supabase as the database.")
        else:
            print("\n❌ Failed to switch to Supabase")
    else:
        print("\n📝 Supabase is configured but not active.")
        print("You can switch later by running this script again or manually updating your .env file.")
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
