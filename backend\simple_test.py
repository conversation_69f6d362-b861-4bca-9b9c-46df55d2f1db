"""
Simple API test
"""

import requests
import json

def test_health():
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
    except Exception as e:
        print(f"Health check failed: {e}")
    return False

def test_root():
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        print(f"Root endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
    except Exception as e:
        print(f"Root endpoint failed: {e}")
    return False

if __name__ == "__main__":
    print("🧪 Testing API endpoints...")
    
    if test_health():
        print("✅ Health check passed")
    else:
        print("❌ Health check failed")
    
    if test_root():
        print("✅ Root endpoint passed")
    else:
        print("❌ Root endpoint failed")
    
    print("🎉 Basic tests completed!")
