"""
Development server startup script
"""

import uvicorn
import os
from pathlib import Path

if __name__ == "__main__":
    # Set environment variables for development
    os.environ.setdefault("DEBUG", "true")
    os.environ.setdefault("SECRET_KEY", "development-secret-key-change-in-production-at-least-32-chars")
    os.environ.setdefault("DATABASE_URL", "sqlite+aiosqlite:///./email_outreach.db")
    os.environ.setdefault("REDIS_URL", "redis://localhost:6379")
    os.environ.setdefault("SMTP_HOST", "smtp.gmail.com")
    os.environ.setdefault("SMTP_PORT", "587")
    os.environ.setdefault("SMTP_USERNAME", "<EMAIL>")
    os.environ.setdefault("SMTP_PASSWORD", "your-app-password")
    os.environ.setdefault("DEFAULT_FROM_EMAIL", "<EMAIL>")
    os.environ.setdefault("OPENAI_API_KEY", "sk-your-openai-key-here")
    
    # Create uploads directory
    uploads_dir = Path("uploads")
    uploads_dir.mkdir(exist_ok=True)
    
    print("🚀 Starting AI Email Outreach Tool API in development mode...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Auto-reload enabled")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
