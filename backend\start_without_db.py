#!/usr/bin/env python3
"""
Start the backend without database initialization for testing
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import uvicorn

from app.core.config import settings

# Create FastAPI application without lifespan
app = FastAPI(
    title="AI Email Outreach Tool API",
    description="A comprehensive API for AI-powered email outreach campaigns.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Email Outreach Tool API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc",
        "note": "Started without database initialization"
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "version": "1.0.0",
        "database": {"status": "not_initialized", "note": "Started without DB"},
        "supabase_configured": bool(settings.SUPABASE_URL and settings.SUPABASE_ANON_KEY),
        "environment": settings.ENVIRONMENT
    }

@app.get("/test-supabase", tags=["Test"])
async def test_supabase():
    """Test Supabase configuration"""
    try:
        from app.core.database_utils import test_supabase_connection
        
        connection_ok = await test_supabase_connection()
        
        return {
            "supabase_configured": bool(settings.SUPABASE_URL and settings.SUPABASE_ANON_KEY),
            "connection_test": "passed" if connection_ok else "failed",
            "database_url": settings.DATABASE_URL.split("@")[0] + "@***" if "@" in settings.DATABASE_URL else settings.DATABASE_URL,
            "supabase_url": settings.SUPABASE_URL
        }
    except Exception as e:
        return {
            "supabase_configured": bool(settings.SUPABASE_URL and settings.SUPABASE_ANON_KEY),
            "connection_test": "error",
            "error": str(e),
            "database_url": settings.DATABASE_URL.split("@")[0] + "@***" if "@" in settings.DATABASE_URL else settings.DATABASE_URL,
            "supabase_url": settings.SUPABASE_URL
        }

if __name__ == "__main__":
    print("🚀 Starting AI Email Outreach Tool API (No DB Mode)...")
    print(f"🌐 API will run on: {settings.SERVER_HOST}:{settings.SERVER_PORT}")
    print(f"📚 API docs available at: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}/docs")
    print("⚠️  Database initialization skipped")
    
    uvicorn.run(
        "start_without_db:app",
        host=settings.SERVER_HOST,
        port=settings.SERVER_PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
