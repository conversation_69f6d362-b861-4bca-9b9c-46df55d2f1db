"""
Simple API test script
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_health():
    """Test health endpoint"""
    response = requests.get(f"{BASE_URL}/health")
    print(f"Health check: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def test_register():
    """Test user registration"""
    user_data = {
        "email": "<EMAIL>",
        "password": "TestPass123",
        "password_confirm": "TestPass123",
        "full_name": "Test User",
        "company": "Test Company"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=user_data)
    print(f"Registration: {response.status_code}")
    
    if response.status_code == 201:
        print(f"User created: {response.json()}")
        return True
    else:
        print(f"Error: {response.text}")
        return False

def test_login():
    """Test user login"""
    login_data = {
        "username": "<EMAIL>",  # OAuth2PasswordRequestForm uses 'username' field
        "password": "TestPass123"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login", 
        data=login_data,  # OAuth2PasswordRequestForm expects form data
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    print(f"Login: {response.status_code}")
    
    if response.status_code == 200:
        token_data = response.json()
        print(f"Login successful: {token_data['access_token'][:20]}...")
        return token_data["access_token"]
    else:
        print(f"Login error: {response.text}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
    
    print(f"Protected endpoint: {response.status_code}")
    
    if response.status_code == 200:
        print(f"User profile: {response.json()}")
        return True
    else:
        print(f"Error: {response.text}")
        return False

if __name__ == "__main__":
    print("🧪 Testing AI Email Outreach Tool API...")
    print("=" * 50)
    
    # Test health
    if test_health():
        print("✅ Health check passed")
    else:
        print("❌ Health check failed")
        exit(1)
    
    print()
    
    # Test registration
    if test_register():
        print("✅ Registration passed")
    else:
        print("❌ Registration failed")
        # Continue anyway, user might already exist
    
    print()
    
    # Test login
    token = test_login()
    if token:
        print("✅ Login passed")
    else:
        print("❌ Login failed")
        exit(1)
    
    print()
    
    # Test protected endpoint
    if test_protected_endpoint(token):
        print("✅ Protected endpoint passed")
    else:
        print("❌ Protected endpoint failed")
    
    print()
    print("🎉 All tests completed!")
