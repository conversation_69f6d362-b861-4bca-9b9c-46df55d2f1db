"""
Test authentication endpoints
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_register():
    """Test user registration"""
    user_data = {
        "email": "<EMAIL>",
        "password": "TestPass123",
        "password_confirm": "TestPass123",
        "full_name": "Test User",
        "company": "Test Company"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=user_data, timeout=10)
        print(f"Registration: {response.status_code}")
        
        if response.status_code == 201:
            user = response.json()
            print(f"✅ User created: {user['email']} (ID: {user['id']})")
            return True
        elif response.status_code == 422:
            print(f"❌ Validation error: {response.json()}")
            return False
        else:
            print(f"❌ Registration failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_login():
    """Test user login"""
    login_data = {
        "username": "<EMAIL>",  # OAuth2PasswordRequestForm uses 'username'
        "password": "TestPass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login", 
            data=login_data,  # Form data for OAuth2
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"Login: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            print(f"✅ Login successful! Token: {token_data['access_token'][:20]}...")
            return token_data["access_token"]
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers, timeout=10)
        
        print(f"Protected endpoint: {response.status_code}")
        
        if response.status_code == 200:
            user_profile = response.json()
            print(f"✅ User profile: {user_profile['email']} - {user_profile['full_name']}")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False

if __name__ == "__main__":
    print("🔐 Testing Authentication Flow...")
    print("=" * 50)
    
    # Test registration
    print("1. Testing Registration...")
    if test_register():
        print("✅ Registration successful")
    else:
        print("⚠️ Registration failed (user might already exist)")
    
    print()
    
    # Test login
    print("2. Testing Login...")
    token = test_login()
    if token:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        exit(1)
    
    print()
    
    # Test protected endpoint
    print("3. Testing Protected Endpoint...")
    if test_protected_endpoint(token):
        print("✅ Protected endpoint successful")
    else:
        print("❌ Protected endpoint failed")
    
    print()
    print("🎉 Authentication flow test completed!")
