"""
Test contact management functionality
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "TestPass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_create_contact(token):
    """Test contact creation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    contact_data = {
        "email": "<EMAIL>",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "company": "TechCorp Inc",
        "job_title": "Marketing Director",
        "phone": "******-123-4567",
        "website": "https://techcorp.com",
        "linkedin_url": "https://linkedin.com/in/johndoe",
        "city": "San Francisco",
        "state": "CA",
        "country": "USA",
        "notes": "Interested in AI marketing solutions",
        "tags": ["prospect", "marketing", "tech"],
        "custom_fields": {
            "industry": "Technology",
            "company_size": "100-500",
            "source": "website",
            "budget": "50k-100k"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/contacts/",
            json=contact_data,
            headers=headers,
            timeout=15
        )
        
        print(f"Create Contact: {response.status_code}")
        if response.status_code == 201:
            contact = response.json()
            print(f"✅ Contact created: {contact['first_name']} {contact['last_name']} (ID: {contact['id']})")
            return contact["id"]
        else:
            print(f"❌ Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Contact creation error: {e}")
        return None

def test_bulk_create_contacts(token):
    """Test bulk contact creation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    bulk_data = {
        "contacts": [
            {
                "email": "<EMAIL>",
                "first_name": "Jane",
                "last_name": "Smith",
                "company": "Innovate Solutions",
                "job_title": "CEO",
                "tags": ["decision-maker", "ceo"],
                "custom_fields": {"industry": "Software"}
            },
            {
                "email": "<EMAIL>",
                "first_name": "Mike",
                "last_name": "Johnson",
                "company": "Startup.io",
                "job_title": "CTO",
                "tags": ["technical", "startup"],
                "custom_fields": {"industry": "Technology"}
            },
            {
                "email": "<EMAIL>",
                "first_name": "Sarah",
                "last_name": "Wilson",
                "company": "Enterprise Corp",
                "job_title": "VP Marketing",
                "tags": ["marketing", "enterprise"],
                "custom_fields": {"industry": "Enterprise"}
            }
        ],
        "skip_duplicates": True,
        "update_existing": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/contacts/bulk",
            json=bulk_data,
            headers=headers,
            timeout=20
        )
        
        print(f"Bulk Create Contacts: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Bulk creation: {result['created']} created, {result['skipped']} skipped")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Bulk creation error: {e}")
        return False

def test_get_contacts(token):
    """Test getting contacts list"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/contacts/",
            headers=headers,
            timeout=10
        )
        
        print(f"Get Contacts: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Found {result['total']} contacts")
            for contact in result['contacts'][:3]:  # Show first 3
                print(f"   - {contact['first_name']} {contact['last_name']} ({contact['email']})")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Get contacts error: {e}")
        return False

def test_get_contact(token, contact_id):
    """Test getting a specific contact"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/contacts/{contact_id}",
            headers=headers,
            timeout=10
        )
        
        print(f"Get Contact {contact_id}: {response.status_code}")
        if response.status_code == 200:
            contact = response.json()
            print(f"✅ Contact: {contact['first_name']} {contact['last_name']}")
            print(f"   Company: {contact['company']}")
            print(f"   Job Title: {contact['job_title']}")
            print(f"   Status: {contact['status']}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Get contact error: {e}")
        return False

def test_update_contact(token, contact_id):
    """Test updating a contact"""
    headers = {"Authorization": f"Bearer {token}"}
    
    update_data = {
        "job_title": "Senior Marketing Director",
        "phone": "******-123-9999",
        "notes": "Updated: Very interested in AI marketing solutions. Follow up next week.",
        "tags": ["prospect", "marketing", "tech", "hot-lead"],
        "lead_score": 95,
        "custom_fields": {
            "source": "website",
            "budget": "100k+",
            "last_interaction": "2024-01-15"
        }
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/api/v1/contacts/{contact_id}",
            json=update_data,
            headers=headers,
            timeout=15
        )
        
        print(f"Update Contact {contact_id}: {response.status_code}")
        if response.status_code == 200:
            contact = response.json()
            print(f"✅ Contact updated: {contact['job_title']}")
            print(f"   Lead Score: {contact['lead_score']}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Update contact error: {e}")
        return False

def test_search_contacts(token):
    """Test contact search"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/contacts/search?q=marketing",
            headers=headers,
            timeout=10
        )
        
        print(f"Search Contacts: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Search results: {result['total']} contacts found for 'marketing'")
            for contact in result['contacts']:
                print(f"   - {contact['first_name']} {contact['last_name']} ({contact['job_title']})")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

def test_contact_stats(token):
    """Test contact statistics"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/contacts/stats",
            headers=headers,
            timeout=10
        )
        
        print(f"Contact Stats: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Contact Statistics:")
            print(f"   Total Contacts: {stats['total_contacts']}")
            print(f"   Status Counts: {stats['status_counts']}")
            print(f"   Emails Sent: {stats['total_sent']}")
            print(f"   Avg Open Rate: {stats['avg_open_rate']}%")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Stats error: {e}")
        return False

def test_delete_contact(token, contact_id):
    """Test deleting a contact"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.delete(
            f"{BASE_URL}/api/v1/contacts/{contact_id}",
            headers=headers,
            timeout=10
        )
        
        print(f"Delete Contact {contact_id}: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result['message']}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Delete contact error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Contact Management System...")
    print("=" * 60)
    
    # Get authentication token
    print("1. Getting authentication token...")
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        exit(1)
    print("✅ Authentication successful")
    
    print()
    
    # Test contact creation
    print("2. Testing Contact Creation...")
    contact_id = test_create_contact(token)
    if not contact_id:
        print("❌ Contact creation failed")
        exit(1)
    
    print()
    
    # Test bulk contact creation
    print("3. Testing Bulk Contact Creation...")
    test_bulk_create_contacts(token)
    
    print()
    
    # Test getting contacts
    print("4. Testing Get Contacts...")
    test_get_contacts(token)
    
    print()
    
    # Test getting specific contact
    print("5. Testing Get Specific Contact...")
    test_get_contact(token, contact_id)
    
    print()
    
    # Test updating contact
    print("6. Testing Contact Update...")
    test_update_contact(token, contact_id)
    
    print()
    
    # Test contact search
    print("7. Testing Contact Search...")
    test_search_contacts(token)
    
    print()
    
    # Test contact statistics
    print("8. Testing Contact Statistics...")
    test_contact_stats(token)
    
    print()
    
    # Test deleting contact
    print("9. Testing Delete Contact...")
    test_delete_contact(token, contact_id)
    
    print()
    print("🎉 Contact management testing completed!")
    print()
    print("📝 Notes:")
    print("- All CRUD operations are working")
    print("- Bulk operations are functional")
    print("- Search and filtering capabilities implemented")
    print("- Statistics and analytics ready")
    print("- Ready for campaign integration")
