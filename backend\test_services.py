"""
Test email and AI services
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "TestPass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_ai_email_generation(token):
    """Test AI email generation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    request_data = {
        "prompt": "Write a professional outreach email to a potential client about our AI email marketing services",
        "context": "The recipient is a marketing director at a tech company",
        "tone": "professional",
        "length": "medium",
        "include_subject": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/ai/generate-email",
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        print(f"AI Email Generation: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Generated Subject: {result.get('subject', 'N/A')}")
            print(f"✅ Generated Content: {result.get('content', 'N/A')[:100]}...")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ AI Generation error: {e}")
        return False

def test_subject_optimization(token):
    """Test subject line optimization"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/ai/optimize-subject?subject=Hello&context=Business outreach",
            headers=headers,
            timeout=20
        )
        
        print(f"Subject Optimization: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Original: {result.get('original_subject')}")
            print(f"✅ Optimized: {result.get('optimized_subjects', [])}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Subject optimization error: {e}")
        return False

def test_email_personalization(token):
    """Test email personalization"""
    headers = {"Authorization": f"Bearer {token}"}
    
    request_data = {
        "template": "Hello {{first_name}}, I hope this email finds you well at {{company}}. We have an exciting opportunity for {{job_title}} professionals.",
        "contact_data": {
            "first_name": "John",
            "last_name": "Doe",
            "company": "Tech Corp",
            "job_title": "Marketing Director"
        },
        "personalization_level": "medium"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/ai/personalize",
            json=request_data,
            headers=headers,
            timeout=20
        )
        
        print(f"Email Personalization: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Personalized: {result.get('personalized_content', 'N/A')[:100]}...")
            print(f"✅ Elements: {result.get('personalization_elements', [])}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Personalization error: {e}")
        return False

def test_email_templates(token):
    """Test email templates"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/emails/templates",
            headers=headers,
            timeout=10
        )
        
        print(f"Email Templates: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Available templates: {len(result.get('templates', []))}")
            for template in result.get('templates', []):
                print(f"   - {template.get('display_name')}: {template.get('description')}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Templates error: {e}")
        return False

def test_email_sending(token):
    """Test email sending (without actually sending)"""
    headers = {"Authorization": f"Bearer {token}"}
    
    request_data = {
        "to_email": "<EMAIL>",
        "subject": "Test Email from AI Outreach Tool",
        "content": "This is a test email to verify the email sending functionality.",
        "content_html": "<p>This is a <strong>test email</strong> to verify the email sending functionality.</p>",
        "tracking_enabled": True
    }
    
    try:
        # Note: This will fail without proper SMTP configuration, but we can test the endpoint
        response = requests.post(
            f"{BASE_URL}/api/v1/emails/test",
            json=request_data,
            headers=headers,
            timeout=10
        )
        
        print(f"Email Sending Test: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test email status: {result.get('status')}")
            return True
        else:
            print(f"⚠️ Expected failure (no SMTP config): {response.status_code}")
            # This is expected to fail without SMTP configuration
            return True
    except Exception as e:
        print(f"⚠️ Email sending error (expected): {e}")
        return True

def test_email_stats(token):
    """Test email statistics"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/emails/stats",
            headers=headers,
            timeout=10
        )
        
        print(f"Email Stats: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stats retrieved: {result}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Stats error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Email and AI Services...")
    print("=" * 60)
    
    # Get authentication token
    print("1. Getting authentication token...")
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        exit(1)
    print("✅ Authentication successful")
    
    print()
    
    # Test AI services
    print("2. Testing AI Email Generation...")
    test_ai_email_generation(token)
    
    print()
    
    print("3. Testing Subject Optimization...")
    test_subject_optimization(token)
    
    print()
    
    print("4. Testing Email Personalization...")
    test_email_personalization(token)
    
    print()
    
    # Test email services
    print("5. Testing Email Templates...")
    test_email_templates(token)
    
    print()
    
    print("6. Testing Email Sending...")
    test_email_sending(token)
    
    print()
    
    print("7. Testing Email Stats...")
    test_email_stats(token)
    
    print()
    print("🎉 Service testing completed!")
    print()
    print("📝 Notes:")
    print("- AI services require OpenAI API key in .env file")
    print("- Email sending requires SMTP configuration in .env file")
    print("- Some tests may show expected failures without proper configuration")
