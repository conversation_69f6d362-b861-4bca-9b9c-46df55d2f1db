#!/usr/bin/env python3
"""
Comprehensive Supabase integration test
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database_utils import test_database_connection, db_manager


async def test_configuration():
    """Test Supabase configuration"""
    print("🔧 Testing Configuration...")
    print("=" * 40)
    
    config_issues = []
    
    # Check required environment variables
    if not settings.SUPABASE_URL:
        config_issues.append("SUPABASE_URL not set")
    else:
        print(f"✅ SUPABASE_URL: {settings.SUPABASE_URL}")
    
    if not settings.SUPABASE_ANON_KEY:
        config_issues.append("SUPABASE_ANON_KEY not set")
    else:
        print(f"✅ SUPABASE_ANON_KEY: {settings.SUPABASE_ANON_KEY[:20]}...")
    
    if not settings.SUPABASE_SERVICE_ROLE_KEY:
        config_issues.append("SUPABASE_SERVICE_ROLE_KEY not set")
    else:
        print(f"✅ SUPABASE_SERVICE_ROLE_KEY: {settings.SUPABASE_SERVICE_ROLE_KEY[:20]}...")
    
    # Check database URL
    print(f"🗄️  DATABASE_URL: {settings.DATABASE_URL}")
    print(f"🔗 USE_SUPABASE_DATABASE: {settings.USE_SUPABASE_DATABASE}")
    
    if config_issues:
        print("\n❌ Configuration Issues:")
        for issue in config_issues:
            print(f"  - {issue}")
        return False
    
    print("\n✅ Configuration looks good!")
    return True


async def test_database_connectivity():
    """Test database connectivity"""
    print("\n🗄️  Testing Database Connectivity...")
    print("=" * 40)
    
    # Test current database URL
    print(f"Testing current database: {settings.DATABASE_URL}")
    current_success = await test_database_connection(settings.DATABASE_URL)
    
    if current_success:
        print("✅ Current database connection successful!")
    else:
        print("❌ Current database connection failed!")
    
    # Test Supabase specifically if configured
    if settings.SUPABASE_URL:
        supabase_url = f"postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
        print(f"\nTesting Supabase database: {supabase_url}")
        supabase_success = await test_database_connection(supabase_url)
        
        if supabase_success:
            print("✅ Supabase database connection successful!")
        else:
            print("❌ Supabase database connection failed!")
        
        return current_success, supabase_success
    
    return current_success, False


async def test_supabase_client():
    """Test Supabase client functionality"""
    print("\n🔌 Testing Supabase Client...")
    print("=" * 40)
    
    try:
        from app.services.supabase_service import supabase_service
        
        if not supabase_service.is_configured:
            print("❌ Supabase service not configured")
            return False
        
        # Test connection
        connection_success = await supabase_service.test_connection()
        
        if connection_success:
            print("✅ Supabase client connection successful!")
            return True
        else:
            print("❌ Supabase client connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Supabase client test error: {e}")
        return False


async def test_database_manager():
    """Test database manager functionality"""
    print("\n🔧 Testing Database Manager...")
    print("=" * 40)
    
    try:
        # Test health check
        health = await db_manager.health_check()
        print(f"Database Status: {health['status']}")
        print(f"Database Type: {health.get('database_type', 'unknown')}")
        print(f"Supabase Configured: {health.get('supabase_configured', False)}")
        print(f"Supabase Enabled: {health.get('supabase_enabled', False)}")
        
        if health['status'] == 'healthy':
            print("✅ Database manager working correctly!")
            return True
        else:
            print("❌ Database manager reports unhealthy status!")
            return False
            
    except Exception as e:
        print(f"❌ Database manager test error: {e}")
        return False


async def test_models_import():
    """Test that all models can be imported"""
    print("\n📋 Testing Models Import...")
    print("=" * 40)
    
    try:
        from app.models import user, campaign, contact, sequence, analytics
        print("✅ All models imported successfully!")
        
        # Test model classes
        models = [
            ("User", user.User),
            ("Campaign", campaign.Campaign),
            ("Contact", contact.Contact),
            ("EmailSequence", sequence.EmailSequence),
            ("EmailLog", analytics.EmailLog),
            ("DailyStats", analytics.DailyStats)
        ]
        
        for name, model_class in models:
            if hasattr(model_class, '__tablename__'):
                print(f"  ✅ {name} -> {model_class.__tablename__}")
            else:
                print(f"  ❌ {name} -> No tablename")
        
        return True
        
    except Exception as e:
        print(f"❌ Models import error: {e}")
        return False


async def main():
    """Main test function"""
    print("🧪 Supabase Integration Test Suite")
    print("=" * 50)
    
    # Test configuration
    config_ok = await test_configuration()
    
    # Test database connectivity
    current_db_ok, supabase_db_ok = await test_database_connectivity()
    
    # Test Supabase client
    client_ok = await test_supabase_client()
    
    # Test database manager
    manager_ok = await test_database_manager()
    
    # Test models
    models_ok = await test_models_import()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    results = [
        ("Configuration", config_ok),
        ("Current Database", current_db_ok),
        ("Supabase Database", supabase_db_ok),
        ("Supabase Client", client_ok),
        ("Database Manager", manager_ok),
        ("Models Import", models_ok)
    ]
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    # Overall status
    all_critical_pass = config_ok and current_db_ok and manager_ok and models_ok
    supabase_ready = all_critical_pass and supabase_db_ok and client_ok
    
    print("\n" + "=" * 50)
    if supabase_ready:
        print("🎉 ALL TESTS PASSED!")
        print("Your backend is fully configured for Supabase!")
        print("\nTo switch to Supabase:")
        print("1. Run: python setup_supabase.py")
        print("2. Or manually update your .env file")
    elif all_critical_pass:
        print("✅ CORE FUNCTIONALITY WORKING!")
        print("Your backend is working with the current database.")
        print("Supabase configuration needs attention.")
    else:
        print("❌ CRITICAL ISSUES FOUND!")
        print("Please fix the failing tests before proceeding.")
    
    return supabase_ready


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
