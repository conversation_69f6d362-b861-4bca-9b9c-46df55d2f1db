#!/usr/bin/env python3
"""
Supabase-only backend test
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database_utils import test_supabase_connection, db_manager


async def test_supabase_configuration():
    """Test Supabase configuration"""
    print("🔧 Testing Supabase Configuration...")
    print("=" * 50)
    
    # Check required environment variables
    config_ok = True
    
    if not settings.SUPABASE_URL:
        print("❌ SUPABASE_URL not configured")
        config_ok = False
    else:
        print(f"✅ SUPABASE_URL: {settings.SUPABASE_URL}")
    
    if not settings.SUPABASE_ANON_KEY:
        print("❌ SUPABASE_ANON_KEY not configured")
        config_ok = False
    else:
        print(f"✅ SUPABASE_ANON_KEY: {settings.SUPABASE_ANON_KEY[:20]}...")
    
    if not settings.SUPABASE_SERVICE_ROLE_KEY:
        print("❌ SUPABASE_SERVICE_ROLE_KEY not configured")
        config_ok = False
    else:
        print(f"✅ SUPABASE_SERVICE_ROLE_KEY: {settings.SUPABASE_SERVICE_ROLE_KEY[:20]}...")
    
    # Check database URL
    if "postgresql" not in settings.DATABASE_URL:
        print("❌ DATABASE_URL is not PostgreSQL")
        config_ok = False
    else:
        print(f"✅ DATABASE_URL: PostgreSQL configured")
    
    return config_ok


async def test_database_connection():
    """Test database connection"""
    print("\n🗄️  Testing Supabase Database Connection...")
    print("=" * 50)
    
    try:
        success = await test_supabase_connection()
        
        if success:
            print("✅ Supabase database connection successful!")
            return True
        else:
            print("❌ Supabase database connection failed!")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


async def test_database_manager():
    """Test database manager"""
    print("\n🔧 Testing Database Manager...")
    print("=" * 50)
    
    try:
        health = await db_manager.health_check()
        
        print(f"Status: {health['status']}")
        print(f"Database Type: {health.get('database_type', 'unknown')}")
        print(f"Provider: {health.get('provider', 'unknown')}")
        print(f"Supabase Configured: {health.get('supabase_configured', False)}")
        
        if health['status'] == 'healthy':
            print("✅ Database manager working correctly!")
            return True
        else:
            print("❌ Database manager reports issues!")
            return False
    except Exception as e:
        print(f"❌ Database manager error: {e}")
        return False


async def test_supabase_service():
    """Test Supabase service"""
    print("\n🔌 Testing Supabase Service...")
    print("=" * 50)
    
    try:
        from app.services.supabase_service import supabase_service
        
        if not supabase_service.is_configured:
            print("❌ Supabase service not configured")
            return False
        
        success = await supabase_service.test_connection()
        
        if success:
            print("✅ Supabase service working correctly!")
            return True
        else:
            print("❌ Supabase service connection failed!")
            return False
    except Exception as e:
        print(f"❌ Supabase service error: {e}")
        return False


async def test_models():
    """Test model imports"""
    print("\n📋 Testing Models...")
    print("=" * 50)
    
    try:
        from app.models import user, campaign, contact, sequence, analytics
        
        models = [
            ("User", user.User),
            ("Campaign", campaign.Campaign),
            ("Contact", contact.Contact),
            ("EmailSequence", sequence.EmailSequence),
            ("EmailLog", analytics.EmailLog),
            ("DailyStats", analytics.DailyStats)
        ]
        
        for name, model_class in models:
            if hasattr(model_class, '__tablename__'):
                print(f"✅ {name} -> {model_class.__tablename__}")
            else:
                print(f"❌ {name} -> No tablename")
        
        print("✅ All models imported successfully!")
        return True
    except Exception as e:
        print(f"❌ Models import error: {e}")
        return False


async def main():
    """Main test function"""
    print("🧪 Supabase-Only Backend Test")
    print("=" * 60)
    
    # Run all tests
    config_ok = await test_supabase_configuration()
    db_connection_ok = await test_database_connection()
    db_manager_ok = await test_database_manager()
    service_ok = await test_supabase_service()
    models_ok = await test_models()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    results = [
        ("Supabase Configuration", config_ok),
        ("Database Connection", db_connection_ok),
        ("Database Manager", db_manager_ok),
        ("Supabase Service", service_ok),
        ("Models Import", models_ok)
    ]
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
    
    # Overall status
    all_pass = all(result for _, result in results)
    
    print("\n" + "=" * 60)
    if all_pass:
        print("🎉 ALL TESTS PASSED!")
        print("Your Supabase-only backend is ready!")
        print("\nYou can now start your backend:")
        print("python main.py")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please fix the issues before starting your backend.")
        
        if not config_ok:
            print("\n🔧 Configuration Issues:")
            print("- Check your .env file")
            print("- Verify Supabase credentials")
        
        if not db_connection_ok:
            print("\n🗄️  Database Issues:")
            print("- Check Supabase project status")
            print("- Verify network connectivity")
            print("- Check database URL format")
    
    return all_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
