IMAPClient-2.3.1.dist-info/AUTHORS.rst,sha256=_rxgM888TH8IWFTSvJClxnAxGSXnJHfndoKriDlpw6U,782
IMAPClient-2.3.1.dist-info/COPYING,sha256=NHLfcEugl9DSTplK0wgKxQS2ZAK6uJf6N06zG-mk7Js,1484
IMAPClient-2.3.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
IMAPClient-2.3.1.dist-info/METADATA,sha256=PHDLKJsz23xPZqZcddhTlUtQ2Wt9HVpQGXH88oUJoUk,1902
IMAPClient-2.3.1.dist-info/RECORD,,
IMAPClient-2.3.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IMAPClient-2.3.1.dist-info/WHEEL,sha256=z9j0xAa_JmUKMpmz72K0ZGALSM_n-wQVmGbleXx2VHg,110
IMAPClient-2.3.1.dist-info/top_level.txt,sha256=pf_BiuslDCnFXY3K-jy6peSBOfJWXGH17SDmDGcBqMI,11
imapclient/__init__.py,sha256=FmqZJCrce0SdehPgF0q3aPISISZZa8sDGBilGXFOir8,453
imapclient/__pycache__/__init__.cpython-311.pyc,,
imapclient/__pycache__/__init__.cpython-37.pyc,sha256=AsG-9HTZmrIo4f9QSKXSFcAYy9GChaQe3Ty9dbXVQqQ,341
imapclient/__pycache__/config.cpython-311.pyc,,
imapclient/__pycache__/config.cpython-37.pyc,sha256=eMjFVgndjWZEQIVeoiezzfS-q0y1qhvOGJNgz3F4sGU,5350
imapclient/__pycache__/datetime_util.cpython-311.pyc,,
imapclient/__pycache__/datetime_util.cpython-37.pyc,sha256=R10Dew6Yjpf286wh9aXICPMSqAyZC9NUHpM3kkLcBXA,2199
imapclient/__pycache__/exceptions.cpython-311.pyc,,
imapclient/__pycache__/exceptions.cpython-37.pyc,sha256=3aMof50hoH9lmQBp5jQ6j8tbTZA11fCsaZTQ7CYqXSc,1589
imapclient/__pycache__/fixed_offset.cpython-311.pyc,,
imapclient/__pycache__/fixed_offset.cpython-37.pyc,sha256=zGSj27u1gSWNNSNYOA2-HfN_AVR-24cKNaeYn_csd9U,1568
imapclient/__pycache__/imap4.cpython-311.pyc,,
imapclient/__pycache__/imap4.cpython-37.pyc,sha256=6Ov9nwrO2FmzxGtdb7dG8BG5JZ6hdrQHxG-0E8cY2gc,718
imapclient/__pycache__/imap_utf7.cpython-311.pyc,,
imapclient/__pycache__/imap_utf7.cpython-37.pyc,sha256=VJbpkVGX3LitYzpq-CA50tMCEPG9BxGFtq9TDhhTBsI,2320
imapclient/__pycache__/imapclient.cpython-311.pyc,,
imapclient/__pycache__/imapclient.cpython-37.pyc,sha256=izYJjET8fe7CsjhwaWXeEV-B6PTuCgPZDeWe6EFUdS4,61238
imapclient/__pycache__/imaplib_ssl_fix.cpython-311.pyc,,
imapclient/__pycache__/imaplib_ssl_fix.cpython-37.pyc,sha256=knxjLRJH0ncBcYNltVdaYWYR0eEeXclhDu2OKFLvVUk,1422
imapclient/__pycache__/interact.cpython-311.pyc,,
imapclient/__pycache__/interact.cpython-37.pyc,sha256=3PUkpxdjcLmHJETuXTpzhbpIaiA6fm5Y-ZxNXNLwZKQ,3038
imapclient/__pycache__/livetest.cpython-311.pyc,,
imapclient/__pycache__/livetest.cpython-37.pyc,sha256=L2oGyoMolnDACaqrIJD7bUGBMwIckNeYqJ6BqVWUXVQ,28652
imapclient/__pycache__/response_lexer.cpython-311.pyc,,
imapclient/__pycache__/response_lexer.cpython-37.pyc,sha256=3ZF3J0esKxOEwFiOC6KD8o5c7HYnoEpcpl8E7C66VcM,4893
imapclient/__pycache__/response_parser.cpython-311.pyc,,
imapclient/__pycache__/response_parser.cpython-37.pyc,sha256=CbmR7P1IDfqoz4LQoG46buR48oM9Kkyyhxm7MhpJilY,5652
imapclient/__pycache__/response_types.cpython-311.pyc,,
imapclient/__pycache__/response_types.cpython-37.pyc,sha256=prrxOqIhwhg49-C4buCxruR_1786JK8xIgNZB7CYVlo,4831
imapclient/__pycache__/testable_imapclient.cpython-311.pyc,,
imapclient/__pycache__/testable_imapclient.cpython-37.pyc,sha256=dz4ATCk_wWQp5y6YBBYrv_ZGbCz_AucCRTuRcKxVJzw,1913
imapclient/__pycache__/tls.cpython-311.pyc,,
imapclient/__pycache__/tls.cpython-37.pyc,sha256=ryELup6_SbJu6Ch6CAirtBGreBvP6YSRfkxbJ9Lyp0s,1936
imapclient/__pycache__/util.cpython-311.pyc,,
imapclient/__pycache__/util.cpython-37.pyc,sha256=g4JH9X2NzDilKa3IPmH8GgaDvfPAjhqm7T3SuFpd2mo,1161
imapclient/__pycache__/version.cpython-311.pyc,,
imapclient/__pycache__/version.cpython-37.pyc,sha256=OFVZh5bX4NKyfr_WZBonjcEsG3-KolhPEsFNSFJW-gc,623
imapclient/config.py,sha256=xAjBy3WZzrah4M_LTxmt_R110QI80_K8JZlgufxjaHw,6154
imapclient/datetime_util.py,sha256=21IBIOjDvR5ZQhx_d9iZV2g5AzhYgTOrJqjteXY6hJ4,2210
imapclient/examples/__pycache__/example.cpython-311.pyc,,
imapclient/examples/__pycache__/example.cpython-37.pyc,sha256=MO-vVcorr59ASTHavnvjLDw-ILeKZeGIccnMZGkeVGI,801
imapclient/examples/__pycache__/idle_example.cpython-311.pyc,,
imapclient/examples/__pycache__/idle_example.cpython-37.pyc,sha256=spnwh-U2q4Pc2ZgFBEfiDAn_s3_7XbJBvOGMV_qRzcI,627
imapclient/examples/__pycache__/oauth2_example.cpython-311.pyc,,
imapclient/examples/__pycache__/oauth2_example.cpython-37.pyc,sha256=fTi1lilSezFiSuNh_fin2yhLZovPoqd_SmgdZB4B_Us,540
imapclient/examples/__pycache__/tls_cacert.cpython-311.pyc,,
imapclient/examples/__pycache__/tls_cacert.cpython-37.pyc,sha256=AkDck33yyzl_DvuFSYklhy9ZqNlfSR9BJ0DTboDxnvs,451
imapclient/examples/__pycache__/tls_no_checks.cpython-311.pyc,,
imapclient/examples/__pycache__/tls_no_checks.cpython-37.pyc,sha256=UeYChcvHbSm2mFaRhj8LEuyWfNmzJ1oLfeFXJ7sYJqs,515
imapclient/examples/example.py,sha256=mBiWYdnDCG0pfXHuUCIeGGMh1rRQiT9mTHuHbrNlLxE,874
imapclient/examples/idle_example.py,sha256=NKPOqMTEUD0k0MKooxRKwnttGLSGEd3JOtg4bhmAzHw,713
imapclient/examples/oauth2_example.py,sha256=Tj_n4HS57m4mTr8EKApucV9UW7KUbgpgjWUmh8noLpY,451
imapclient/examples/tls_cacert.py,sha256=9Pe_IteVi2Npt-KQl7TDzP4FNzOaARX2nyewL8Sm1EY,453
imapclient/examples/tls_no_checks.py,sha256=QXYscimmLUb5rNbyBK4_126x43FqGWYY4rHU347x0I8,659
imapclient/exceptions.py,sha256=qTeJA3pis4CvBvP20_Rwo9O2WCFE2dTohBNyec5htz4,1192
imapclient/fixed_offset.py,sha256=A-JNOSDk6R5uZzLpztZIhk6pLfFBgpW7eoO1HK7l_eM,1141
imapclient/imap4.py,sha256=RF8pn8lkFAgnMSlahVprzR1OJdVJ6cL9sRwYdUJkYwY,789
imapclient/imap_utf7.py,sha256=tcKSpr7AC2SZklgzIVrv3ZEEfdIgKRqd0k5pCzCQtkg,3696
imapclient/imapclient.py,sha256=5pnhit2pd0r5hjYiG1ZcqHgG-CA5C-KOlydDFMAwsks,73673
imapclient/imaplib_ssl_fix.py,sha256=A05N0jOjxSf1HJN7j3Ng-a-UJyVi-mEr6v-gWtpZRhQ,1494
imapclient/interact.py,sha256=Ybkt_CH-fR3SDlTXA2O9NvipGVfMJ4gwp2u5nJAG_X4,3806
imapclient/livetest.py,sha256=jyjcaJu41q3XUElVZTQOjpDtJqJPIx1ibXVB-fSOo-o,37951
imapclient/response_lexer.py,sha256=GqnzGctip6uLzck1wpUG9mAQojTXFEDvuQszLZ9lgQc,5450
imapclient/response_parser.py,sha256=koJ1eW1Tm9emQwE1R3vr1Gf_12pQJvEnb7dR0-c6ZV0,7011
imapclient/response_types.py,sha256=m2aF5kgJ2H0byVAu8VAO6onWu5ahvdBeECdGaKw4rbA,4510
imapclient/test/__init__.py,sha256=8z9HfDtKcT49Bir1Plc1hFZ8_822aHey6q_-oHIrUYc,131
imapclient/test/__pycache__/__init__.cpython-311.pyc,,
imapclient/test/__pycache__/__init__.cpython-37.pyc,sha256=4Xayrfl1GDc6P6UfP6iQ0u7_jVoHiyEBiE_SZkvKAtY,126
imapclient/test/__pycache__/imapclient_test.cpython-311.pyc,,
imapclient/test/__pycache__/imapclient_test.cpython-37.pyc,sha256=q_TZ6Czz2rwD7WW24gohS2G9xoaqp9tBx-avOewgoek,547
imapclient/test/__pycache__/test_auth.cpython-311.pyc,,
imapclient/test/__pycache__/test_auth.cpython-37.pyc,sha256=Xgx58SEEoa5_8ihZuirCFP-eWqoEjLM6sRSQ9FdJWSo,1642
imapclient/test/__pycache__/test_datetime_util.cpython-311.pyc,,
imapclient/test/__pycache__/test_datetime_util.cpython-37.pyc,sha256=_xlMcgX7ZCjwzH0GEznXEDElvDX5dM93G5kozcHo9hA,3576
imapclient/test/__pycache__/test_fixed_offset.cpython-311.pyc,,
imapclient/test/__pycache__/test_fixed_offset.cpython-37.pyc,sha256=DdketmkJ1F-eHWX1ZWVwJT7veNBhW3ezkKcGEF7hz3k,2602
imapclient/test/__pycache__/test_folder_status.cpython-311.pyc,,
imapclient/test/__pycache__/test_folder_status.cpython-37.pyc,sha256=rvJZ-MwDuzGQt1zntAKQKoIpbDsCxqlt96SPeBrOmIg,1784
imapclient/test/__pycache__/test_imap_utf7.cpython-311.pyc,,
imapclient/test/__pycache__/test_imap_utf7.cpython-37.pyc,sha256=E57m4XyI_tt1PZ7gmESbacZHgX9eYEdXDX-M5bDcWLk,1955
imapclient/test/__pycache__/test_imapclient.cpython-311.pyc,,
imapclient/test/__pycache__/test_imapclient.cpython-37.pyc,sha256=oXZLP_T42j4_sMlOVi6tKh30T50ueMMZ_sGwZOLTgZw,22749
imapclient/test/__pycache__/test_init.cpython-311.pyc,,
imapclient/test/__pycache__/test_init.cpython-37.pyc,sha256=FNIeq5wofCDhGZ-hIAPWxVuykYUwLFl1VgmqlW76ZHU,2469
imapclient/test/__pycache__/test_response_lexer.cpython-311.pyc,,
imapclient/test/__pycache__/test_response_lexer.cpython-37.pyc,sha256=CCi5sOhpPLacLjfQ6YuGMZfQM0i5kgfQJTm5mbE9cMc,5081
imapclient/test/__pycache__/test_response_parser.cpython-311.pyc,,
imapclient/test/__pycache__/test_response_parser.cpython-37.pyc,sha256=JDRny07cweCFGM8bqFJgOchO8CmL5a8B5Z4hPTgcD-g,19790
imapclient/test/__pycache__/test_search.cpython-311.pyc,,
imapclient/test/__pycache__/test_search.cpython-37.pyc,sha256=j7-rQd6H3DKrggEcPuZPiiJhU_exrcUvUzM7c0W1ssc,5657
imapclient/test/__pycache__/test_sort.cpython-311.pyc,,
imapclient/test/__pycache__/test_sort.cpython-37.pyc,sha256=ymWeppaGwW4Lox1L6eNHEFlzNv6hzA03N_JoiFvzF3I,1917
imapclient/test/__pycache__/test_starttls.cpython-311.pyc,,
imapclient/test/__pycache__/test_starttls.cpython-37.pyc,sha256=Hfcy1KNvqkjQCs0zIkdGDL4rV5U3biB8Tallz00aHjo,2465
imapclient/test/__pycache__/test_store.cpython-311.pyc,,
imapclient/test/__pycache__/test_store.cpython-37.pyc,sha256=Dn-LlzNaN95wGobKyuAi1mP5rinAm0NIPacsXIeJ-j8,4858
imapclient/test/__pycache__/test_thread.cpython-311.pyc,,
imapclient/test/__pycache__/test_thread.cpython-37.pyc,sha256=vM_64T4wrDV4LzXh53WIJLl5cKaKU92jy80vNYjCM9g,1958
imapclient/test/__pycache__/test_util_functions.cpython-311.pyc,,
imapclient/test/__pycache__/test_util_functions.cpython-37.pyc,sha256=mvznPdalkluVgSE6n3Epy_B5V0W1yX0cRw6mDRXUV0k,8647
imapclient/test/__pycache__/test_version.cpython-311.pyc,,
imapclient/test/__pycache__/test_version.cpython-37.pyc,sha256=snKA__iWyk1VqawHH7tEuGVWaLJHyIvnfLBWr_2mLXw,1411
imapclient/test/__pycache__/testable_imapclient.cpython-311.pyc,,
imapclient/test/__pycache__/testable_imapclient.cpython-37.pyc,sha256=KCgrelHh5xuPiT-_BRuhKP93jF5c2I-h-vF8BNUNemA,1435
imapclient/test/__pycache__/util.cpython-311.pyc,,
imapclient/test/__pycache__/util.cpython-37.pyc,sha256=EiHVoqXon_t4ICkthvbEkjhsV7qSWN7-WE2OSZ2pVlQ,730
imapclient/test/imapclient_test.py,sha256=gXKBqaKKlmSdOnUKKk4WAf8dVdJCxOca_S_8vXXkE6w,193
imapclient/test/test_auth.py,sha256=CaKHvgssrZOku2nXQwjwsPMqo2GHZ5IXE5UL-PK9i6Y,1375
imapclient/test/test_datetime_util.py,sha256=RHJey-nAd9pS7AGR-OXz3p1YmDvo6vfC6rPi86rhEtI,2859
imapclient/test/test_fixed_offset.py,sha256=aC4WtbUu_FlKzH-8x-fxUvUtt9II5HPv1YrUAJ_hTF0,2469
imapclient/test/test_folder_status.py,sha256=X87au9peM9sUyuuBzzPE4Sg8JLz_FYAtupZLcxWNWWg,2084
imapclient/test/test_imap_utf7.py,sha256=00nrxd1LlAZiUrPbFr_WDZDp1JpkVjMsKOnOBIrY6cE,1834
imapclient/test/test_imapclient.py,sha256=1ww10K9dWpnJRc7XXpmNuBGEU7tjkIhJn9tmMWKF7vE,23275
imapclient/test/test_init.py,sha256=3qnsWPoti0TVavGSJZDuNHyD16KrZOEmBpKZ179RTxc,2648
imapclient/test/test_response_lexer.py,sha256=V-APOy1aG8WXRWGezk1xb6zclSbekhO_6LAFPaUX5d0,4022
imapclient/test/test_response_parser.py,sha256=_iLJg3ho5-Yjxhe1zBN-t3_ErrC2T8qV50m1aoj3yL8,20073
imapclient/test/test_search.py,sha256=EQ_-dgj0a7MIobyu0iAkKTCxb9KLl_ttwwyalLNgj3E,4014
imapclient/test/test_sort.py,sha256=7XjkjDY3uFn6kKBKUPTuPweKEOOWoRFquD1mNs0QLJU,1324
imapclient/test/test_starttls.py,sha256=oSsvLIqrgyDZ6nbqycIPy-hNWCNEWpgeoZ5nz1mxsOI,2156
imapclient/test/test_store.py,sha256=JUSzeeWFWbo3Huqe0mOwN2ktvoSqaW73PoTgOsgFsAU,4277
imapclient/test/test_thread.py,sha256=LyxOUj5w_vniczxtjRUUsXTvq5tNUNAOb5LUmuB4IoE,1424
imapclient/test/test_util_functions.py,sha256=49wMj9bA2Kuto8GutEAbxSXEB6ojTZDjWPqyRWQ7cDQ,4219
imapclient/test/test_version.py,sha256=bXVd3z8paUTZGAmyu09wdTpRhGVKHY48NW0jkxnaT-0,864
imapclient/test/testable_imapclient.py,sha256=YbVbdvKWTIAX1RfVH-SUDC90BUsIX8CaW82mdLCAl9w,826
imapclient/test/util.py,sha256=Hh8UeV562QDjpm9xzVVsiY2rnnygCmVmSdLQ41J3KQY,1029
imapclient/testable_imapclient.py,sha256=hIUoewFhY2azRDVpAgy93Fh6tPCA0BNWVk1QAOCZ-Yw,1348
imapclient/tls.py,sha256=GwIBGvjKJPxO2mC-nnDzMHdbEhjtiLw7PyQnDRN2XnM,1874
imapclient/util.py,sha256=rEMTTr-M_FnUxHvLbNxfLSmFCPD9YC53dAbQRuVFWUY,1228
imapclient/version.py,sha256=AarLIgqF-0yHBPGrkwXXrb0FMoXUz3ySnmUW6jJAypI,611
