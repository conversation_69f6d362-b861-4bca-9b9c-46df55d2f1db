"""
aiosmtplib
==========

An asyncio SMTP client.

Originally based on smtplib from the Python 3 standard library by:
The Dragon De Monsyne <<EMAIL>>

Author: <PERSON> <<EMAIL>>
"""
from .api import send
from .errors import (
    SMTPAuthenticationError,
    SMTPConnectError,
    SMTPConnectTimeoutError,
    SMTPDataError,
    SMTPException,
    SMTPHeloError,
    SMTPNotSupported,
    SMTPReadTimeoutError,
    SMTPRecipientRefused,
    SMTPRecipientsRefused,
    SMTPResponseException,
    SMTPSenderRefused,
    SMTPServerDisconnected,
    SMTPTimeoutError,
    SMTPConnectResponseError,
)
from .response import SMTPResponse
from .smtp import SMTP
from .typing import SMTPStatus


__title__ = "aiosmtplib"
__version__ = "3.0.1"
__author__ = "Cole Maclean"
__license__ = "MIT"
__copyright__ = "Copyright 2022 Cole Maclean"
__all__ = (
    "send",
    "<PERSON>TP",
    "SMTPResponse",
    "SMTPStatus",
    "SMTPAuthenticationError",
    "SMTPConnectError",
    "SMTPDataError",
    "SMTPException",
    "SMTPHeloError",
    "SMTPNotSupported",
    "SMTPRecipientRefused",
    "SMTPRecipientsRefused",
    "SMTPResponseException",
    "SMTPSenderRefused",
    "SMTPServerDisconnected",
    "SMTPTimeoutError",
    "SMTPConnectTimeoutError",
    "SMTPReadTimeoutError",
    "SMTPConnectResponseError",
)
