#!/usr/bin/env python3
"""
Create a sample user via API
"""

import requests
import json

def create_sample_user():
    """Create a sample user"""
    
    api_url = "http://localhost:8000"
    
    # Sample user data
    user_data = {
        "email": "<EMAIL>",
        "full_name": "<PERSON>",
        "username": "joh<PERSON><PERSON>",
        "hashed_password": "hashed_password_123",
        "company": "Tech Company Inc",
        "job_title": "Marketing Manager",
        "phone": "******-0123"
    }
    
    print("👤 Creating sample user...")
    print(f"📧 Email: {user_data['email']}")
    print(f"👨 Name: {user_data['full_name']}")
    print(f"🏢 Company: {user_data['company']}")
    
    try:
        response = requests.post(
            f"{api_url}/api/users",
            json=user_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                user = data.get("user", {})
                print(f"\n✅ User created successfully!")
                print(f"🆔 User ID: {user.get('id')}")
                print(f"📧 Email: {user.get('email')}")
                print(f"📅 Created: {user.get('created_at')}")
                return True
            else:
                print(f"\n❌ Failed: {data.get('error')}")
                return False
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return False

def get_all_users():
    """Get all users from the database"""
    
    api_url = "http://localhost:8000"
    
    print("\n📋 Getting all users...")
    
    try:
        response = requests.get(f"{api_url}/api/users", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            users = data.get("users", [])
            
            print(f"👥 Total users: {len(users)}")
            print("\n📊 User List:")
            print("-" * 60)
            
            for i, user in enumerate(users, 1):
                print(f"{i}. {user.get('full_name', 'N/A')} ({user.get('email')})")
                print(f"   ID: {user.get('id')} | Role: {user.get('role')} | Status: {user.get('status')}")
                if user.get('company'):
                    print(f"   Company: {user.get('company')}")
                print()
            
            return True
        else:
            print(f"❌ Failed to get users: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Creating Sample User")
    print("=" * 50)
    
    # Create user
    success = create_sample_user()
    
    if success:
        # Show all users
        get_all_users()
        
        print("=" * 50)
        print("🎉 Sample user created successfully!")
        print("🌐 You can now test the API at: http://localhost:8000/docs")
    else:
        print("=" * 50)
        print("❌ Failed to create sample user")
