#!/usr/bin/env python3
"""
Create database schema using Supabase REST API
"""

import requests
import json

def create_schema_via_supabase():
    """Create database schema using Supabase SQL API"""
    
    # Your Supabase configuration
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFhdnRzeXVuZW9xd3Ftc2J1bmVmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDU4MzYyMiwiZXhwIjoyMDY2MTU5NjIyfQ.thXiE7y2rSY7rrhn5Q3hTQPMUVfXiQeXefHyPHjnh3w"
    
    headers = {
        "apikey": service_role_key,
        "Authorization": f"Bearer {service_role_key}",
        "Content-Type": "application/json"
    }
    
    print("🏗️  Creating Database Schema via Supabase API")
    print("=" * 60)
    print(f"🌐 Project URL: {supabase_url}")
    print(f"🔑 Using service role key: {service_role_key[:20]}...")
    print()
    
    # SQL to create all tables
    create_tables_sql = """
-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255),
    full_name VARCHAR(255),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    role VARCHAR(50) DEFAULT 'user',
    status VARCHAR(50) DEFAULT 'active',
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    bio TEXT,
    default_from_name VARCHAR(255),
    default_from_email VARCHAR(255),
    default_reply_to VARCHAR(255),
    subscription_plan VARCHAR(50) DEFAULT 'free',
    monthly_email_limit INTEGER DEFAULT 1000,
    emails_sent_this_month INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS campaigns (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'DRAFT',
    campaign_type VARCHAR(50) NOT NULL DEFAULT 'OUTREACH',
    from_name VARCHAR(255) NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    reply_to_email VARCHAR(255),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    daily_limit INTEGER DEFAULT 100,
    hourly_limit INTEGER DEFAULT 10,
    send_weekdays_only BOOLEAN DEFAULT true,
    send_time_start TIME DEFAULT '09:00',
    send_time_end TIME DEFAULT '17:00',
    use_ai_optimization BOOLEAN DEFAULT true,
    ai_personalization_level VARCHAR(50) DEFAULT 'MEDIUM',
    ai_subject_optimization BOOLEAN DEFAULT true,
    track_opens BOOLEAN DEFAULT true,
    track_clicks BOOLEAN DEFAULT true,
    track_replies BOOLEAN DEFAULT true,
    total_contacts INTEGER DEFAULT 0,
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    emails_replied INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    emails_unsubscribed INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_sent_at TIMESTAMP WITH TIME ZONE
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    full_name VARCHAR(255),
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    linkedin_url VARCHAR(255),
    twitter_handle VARCHAR(255),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    postal_code VARCHAR(50),
    country VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    source VARCHAR(255),
    email_verified BOOLEAN DEFAULT false,
    accepts_marketing BOOLEAN DEFAULT true,
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50),
    last_opened_at TIMESTAMP WITH TIME ZONE,
    last_clicked_at TIMESTAMP WITH TIME ZONE,
    last_replied_at TIMESTAMP WITH TIME ZONE,
    total_opens INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    total_replies INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    lead_score INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, email)
);
"""
    
    # Execute the SQL
    try:
        print("🔨 Creating tables...")
        
        # Use the SQL endpoint (this might not exist, let's try RPC)
        rpc_payload = {
            "sql": create_tables_sql
        }
        
        # Try using the RPC endpoint for SQL execution
        response = requests.post(
            f"{supabase_url}/rest/v1/rpc/exec_sql",
            headers=headers,
            json=rpc_payload,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ Tables created successfully!")
            return True
        else:
            print("❌ Failed to create tables via RPC")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_tables():
    """Test if tables were created by querying them"""
    
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFhdnRzeXVuZW9xd3Ftc2J1bmVmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1ODM2MjIsImV4cCI6MjA2NjE1OTYyMn0.DM2_9DZoh_NlV7qya5tWtRKQh4zE8qvGwlS8cFOAhB8"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    print("\n🧪 Testing table access...")
    
    tables_to_test = ['users', 'campaigns', 'contacts']
    
    for table in tables_to_test:
        try:
            response = requests.get(
                f"{supabase_url}/rest/v1/{table}?select=count",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"  ✅ {table} - accessible")
            else:
                print(f"  ❌ {table} - {response.status_code}: {response.text[:50]}...")
                
        except Exception as e:
            print(f"  ❌ {table} - Error: {e}")

def create_via_dashboard_instructions():
    """Provide instructions for creating schema via Supabase dashboard"""
    
    print("\n📋 Alternative: Create Schema via Supabase Dashboard")
    print("=" * 60)
    print("Since the API approach might not work, here's how to create the schema manually:")
    print()
    print("1. Go to: https://app.supabase.com/project/qavtsyuneoqwqmsbunef")
    print("2. Click on 'SQL Editor' in the left sidebar")
    print("3. Click 'New Query'")
    print("4. Copy and paste the SQL from the file: create_tables.sql")
    print("5. Click 'Run' to execute the SQL")
    print()
    print("I'll create the SQL file for you...")
    
    # Create SQL file
    sql_content = """-- AI Email Outreach Tool Database Schema
-- Execute this in Supabase SQL Editor

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255),
    full_name VARCHAR(255),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    role VARCHAR(50) DEFAULT 'user',
    status VARCHAR(50) DEFAULT 'active',
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    bio TEXT,
    default_from_name VARCHAR(255),
    default_from_email VARCHAR(255),
    default_reply_to VARCHAR(255),
    subscription_plan VARCHAR(50) DEFAULT 'free',
    monthly_email_limit INTEGER DEFAULT 1000,
    emails_sent_this_month INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS campaigns (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'DRAFT',
    campaign_type VARCHAR(50) NOT NULL DEFAULT 'OUTREACH',
    from_name VARCHAR(255) NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    reply_to_email VARCHAR(255),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    daily_limit INTEGER DEFAULT 100,
    hourly_limit INTEGER DEFAULT 10,
    send_weekdays_only BOOLEAN DEFAULT true,
    send_time_start TIME DEFAULT '09:00',
    send_time_end TIME DEFAULT '17:00',
    use_ai_optimization BOOLEAN DEFAULT true,
    ai_personalization_level VARCHAR(50) DEFAULT 'MEDIUM',
    ai_subject_optimization BOOLEAN DEFAULT true,
    track_opens BOOLEAN DEFAULT true,
    track_clicks BOOLEAN DEFAULT true,
    track_replies BOOLEAN DEFAULT true,
    total_contacts INTEGER DEFAULT 0,
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    emails_replied INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    emails_unsubscribed INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_sent_at TIMESTAMP WITH TIME ZONE
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    full_name VARCHAR(255),
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    linkedin_url VARCHAR(255),
    twitter_handle VARCHAR(255),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    postal_code VARCHAR(50),
    country VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    source VARCHAR(255),
    email_verified BOOLEAN DEFAULT false,
    accepts_marketing BOOLEAN DEFAULT true,
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50),
    last_opened_at TIMESTAMP WITH TIME ZONE,
    last_clicked_at TIMESTAMP WITH TIME ZONE,
    last_replied_at TIMESTAMP WITH TIME ZONE,
    total_opens INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    total_replies INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    lead_score INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, email)
);

-- Create email_sequences table
CREATE TABLE IF NOT EXISTS email_sequences (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    content_html TEXT,
    content_text TEXT,
    delay_days INTEGER DEFAULT 0,
    delay_hours INTEGER DEFAULT 0,
    delay_minutes INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    is_active BOOLEAN DEFAULT true,
    use_ai_subject BOOLEAN DEFAULT false,
    use_ai_content BOOLEAN DEFAULT false,
    ai_personalization_prompt TEXT,
    is_ab_test BOOLEAN DEFAULT false,
    ab_test_percentage INTEGER DEFAULT 50,
    ab_variant VARCHAR(10) DEFAULT 'A',
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    emails_replied INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_sent_at TIMESTAMP WITH TIME ZONE
);

-- Create campaign_contacts table
CREATE TABLE IF NOT EXISTS campaign_contacts (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id) ON DELETE CASCADE,
    contact_id INTEGER REFERENCES contacts(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'pending',
    current_sequence_step INTEGER DEFAULT 0,
    last_email_sent_at TIMESTAMP WITH TIME ZONE,
    next_email_scheduled_at TIMESTAMP WITH TIME ZONE,
    total_opens INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    replied BOOLEAN DEFAULT false,
    unsubscribed BOOLEAN DEFAULT false,
    bounced BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(campaign_id, contact_id)
);

-- Create email_logs table
CREATE TABLE IF NOT EXISTS email_logs (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id) ON DELETE CASCADE,
    sequence_id INTEGER REFERENCES email_sequences(id) ON DELETE CASCADE,
    contact_id INTEGER REFERENCES contacts(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    to_email VARCHAR(255) NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_stats table
CREATE TABLE IF NOT EXISTS daily_stats (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    emails_replied INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    unique_opens INTEGER DEFAULT 0,
    unique_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_campaigns_user_id ON campaigns(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_email_sequences_campaign_id ON email_sequences(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_contacts_campaign_id ON campaign_contacts(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_contacts_contact_id ON campaign_contacts(contact_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_campaign_id ON email_logs(campaign_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_contact_id ON email_logs(contact_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
CREATE INDEX IF NOT EXISTS idx_daily_stats_user_date ON daily_stats(user_id, date);

-- Create function for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_sequences_updated_at BEFORE UPDATE ON email_sequences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaign_contacts_updated_at BEFORE UPDATE ON campaign_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_logs_updated_at BEFORE UPDATE ON email_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_stats_updated_at BEFORE UPDATE ON daily_stats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Success message
SELECT 'Database schema created successfully!' as message;
"""
    
    with open('create_tables.sql', 'w') as f:
        f.write(sql_content)
    
    print("✅ Created file: create_tables.sql")
    print("📁 You can now copy this file content to Supabase SQL Editor")

if __name__ == "__main__":
    print("🚀 Supabase Schema Creation")
    print("=" * 60)
    
    # Try API approach first
    api_success = create_schema_via_supabase()
    
    if not api_success:
        print("\n⚠️  API approach failed, providing manual instructions...")
        create_via_dashboard_instructions()
    
    # Test tables regardless
    test_tables()
    
    print("\n" + "=" * 60)
    print("📝 Next Steps:")
    print("1. Create the database schema using Supabase SQL Editor")
    print("2. Copy the SQL from create_tables.sql")
    print("3. Execute it in your Supabase dashboard")
    print("4. Then your backend will work with the database!")
