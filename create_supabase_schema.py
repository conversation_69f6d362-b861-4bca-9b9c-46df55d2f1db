#!/usr/bin/env python3
"""
Create database schema in Supabase
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database import engine, Base


async def create_schema():
    """Create all database tables in Supabase"""
    
    print("🏗️  Creating Supabase Database Schema...")
    print("=" * 50)
    print(f"🗄️  Database: {settings.DATABASE_URL.split('@')[0]}@***")
    print(f"🔗 Supabase: {settings.SUPABASE_URL}")
    print()
    
    try:
        # Import all models to ensure they are registered
        print("📋 Importing models...")
        from app.models import user, campaign, contact, sequence, analytics
        
        models = [
            ("User", user.User),
            ("Campaign", campaign.Campaign),
            ("Contact", contact.Contact),
            ("EmailSequence", sequence.EmailSequence),
            ("CampaignContact", sequence.CampaignContact),
            ("EmailLog", analytics.EmailLog),
            ("DailyStats", analytics.DailyStats)
        ]
        
        for name, model in models:
            print(f"  ✅ {name} -> {model.__tablename__}")
        
        print("\n🔨 Creating database tables...")
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        print("✅ Database schema created successfully!")
        
        # Test the connection
        print("\n🧪 Testing database connection...")
        from app.core.database_utils import test_supabase_connection
        
        connection_ok = await test_supabase_connection()
        
        if connection_ok:
            print("✅ Database connection test passed!")
        else:
            print("❌ Database connection test failed!")
            return False
        
        print("\n🎉 Supabase database is ready!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating schema: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        await engine.dispose()


async def verify_tables():
    """Verify that tables were created"""
    
    print("\n🔍 Verifying tables...")
    
    try:
        from sqlalchemy import text
        
        async with engine.begin() as conn:
            # Get list of tables
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            
            tables = [row[0] for row in result.fetchall()]
            
            expected_tables = [
                'users', 'campaigns', 'contacts', 'email_sequences',
                'campaign_contacts', 'email_logs', 'daily_stats'
            ]
            
            print(f"📊 Found {len(tables)} tables:")
            for table in tables:
                status = "✅" if table in expected_tables else "ℹ️"
                print(f"  {status} {table}")
            
            missing = set(expected_tables) - set(tables)
            if missing:
                print(f"\n⚠️  Missing tables: {', '.join(missing)}")
                return False
            else:
                print(f"\n✅ All expected tables found!")
                return True
                
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False


async def main():
    """Main function"""
    
    print("🚀 Supabase Schema Setup")
    print("=" * 50)
    
    # Create schema
    schema_success = await create_schema()
    
    if not schema_success:
        print("\n❌ Schema creation failed!")
        return False
    
    # Verify tables
    verify_success = await verify_tables()
    
    if not verify_success:
        print("\n❌ Table verification failed!")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 SUCCESS!")
    print("Your Supabase database is fully configured and ready!")
    print("\n📝 Next steps:")
    print("1. Restart your backend: python main.py")
    print("2. Test your API: http://localhost:8000/docs")
    print("3. Start building your email campaigns!")
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
