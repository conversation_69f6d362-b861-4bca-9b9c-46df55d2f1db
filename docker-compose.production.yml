version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: email-outreach-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: email_outreach_prod
      POSTGRES_USER: email_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - email-outreach-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U email_user -d email_outreach_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: email-outreach-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - email-outreach-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: email-outreach-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql+asyncpg://email_user:${DB_PASSWORD}@postgres:5432/email_outreach_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_FROM_EMAIL=${SMTP_FROM_EMAIL}
      - SMTP_FROM_NAME=${SMTP_FROM_NAME}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - email-outreach-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
      args:
        - VITE_API_BASE_URL=${FRONTEND_API_URL}
        - VITE_APP_NAME=AI Email Outreach Tool
        - VITE_ENVIRONMENT=production
    container_name: email-outreach-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - email-outreach-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Alternative setup)
  nginx:
    image: nginx:alpine
    container_name: email-outreach-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./frontend/dist:/usr/share/nginx/html:ro
    networks:
      - email-outreach-network
    depends_on:
      - backend
      - frontend
    profiles:
      - nginx-proxy

  # Background Worker (for email sending)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: email-outreach-worker
    restart: unless-stopped
    command: python -m app.worker
    environment:
      - DATABASE_URL=postgresql+asyncpg://email_user:${DB_PASSWORD}@postgres:5432/email_outreach_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_FROM_EMAIL=${SMTP_FROM_EMAIL}
      - SMTP_FROM_NAME=${SMTP_FROM_NAME}
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - email-outreach-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Monitoring (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: email-outreach-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - email-outreach-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: email-outreach-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - email-outreach-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  email-outreach-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
