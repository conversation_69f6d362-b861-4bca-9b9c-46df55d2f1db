
import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ProtectedRoute } from './components/auth';
import { DashboardLayout } from './components/dashboard';
import { ToastContainer, ErrorBoundary } from './components/ui';
import PageLoadingSpinner from './components/ui/PageLoadingSpinner';

// Lazy load pages for code splitting
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const CampaignsPage = React.lazy(() => import('./pages/CampaignsPage'));
const SequenceBuilderPage = React.lazy(() => import('./pages/SequenceBuilderPage'));
const ContactsPage = React.lazy(() => import('./pages/ContactsPage'));
const ContactDetailPage = React.lazy(() => import('./pages/ContactDetailPage'));
const TemplatesPage = React.lazy(() => import('./pages/TemplatesPage'));
const SettingsPage = React.lazy(() => import('./pages/SettingsPage'));
const AnalyticsPage = React.lazy(() => import('./pages/AnalyticsPage'));

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <NotificationProvider>
              <Router>
                <ToastContainer />
                <Suspense fallback={<PageLoadingSpinner />}>
                  <Routes>
                    {/* Public routes */}
                    <Route path="/login" element={<LoginPage />} />

                    {/* Protected routes */}
                    <Route
                      path="/"
                      element={
                        <ProtectedRoute>
                          <DashboardLayout />
                        </ProtectedRoute>
                      }
                    >
                      <Route index element={<Navigate to="/dashboard" replace />} />
                      <Route path="dashboard" element={<DashboardPage />} />

                      {/* Campaign routes */}
                      <Route path="campaigns" element={<CampaignsPage />} />
                      <Route path="campaigns/:campaignId/sequences" element={<SequenceBuilderPage />} />

                      {/* Other routes */}
                      <Route path="contacts" element={<ContactsPage />} />
                      <Route path="contacts/:id" element={<ContactDetailPage />} />
                      <Route path="templates" element={<TemplatesPage />} />
                      <Route path="analytics" element={<AnalyticsPage />} />
                      <Route path="settings" element={<SettingsPage />} />
                    </Route>

                    {/* Catch all route */}
                    <Route path="*" element={<Navigate to="/dashboard" replace />} />
                  </Routes>
                </Suspense>
        </Router>
            </NotificationProvider>
        </AuthProvider>
      </QueryClientProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
