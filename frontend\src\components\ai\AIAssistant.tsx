import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Input, showToast } from '../ui';
import { apiService } from '../../services/api';
import type { EmailGenerationRequest, EmailGenerationResponse, SubjectOptimizationRequest, SubjectOptimizationResponse } from '../../types';

interface AIAssistantProps {
  onEmailGenerated?: (subject: string, content: string) => void;
  onSubjectOptimized?: (subjects: string[]) => void;
  className?: string;
}

const AIAssistant: React.FC<AIAssistantProps> = ({
  onEmailGenerated,
  onSubjectOptimized,
  className = '',
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [subjectToOptimize, setSubjectToOptimize] = useState('');
  const [tone, setTone] = useState<'professional' | 'friendly' | 'casual'>('professional');
  const [length, setLength] = useState<'short' | 'medium' | 'long'>('medium');
  const [generatedContent, setGeneratedContent] = useState<EmailGenerationResponse | null>(null);
  const [optimizedSubjects, setOptimizedSubjects] = useState<string[]>([]);

  const handleGenerateEmail = async () => {
    if (!prompt.trim()) {
      showToast.error('Please enter a prompt for email generation');
      return;
    }

    setIsGenerating(true);
    try {
      const request: EmailGenerationRequest = {
        prompt: prompt.trim(),
        context: 'Email campaign',
        tone,
        length,
        include_subject: true,
      };

      const response = await apiService.generateEmailContent(request);
      setGeneratedContent(response);
      
      if (onEmailGenerated) {
        onEmailGenerated(response.subject, response.content);
      }

      showToast.success('Email generated successfully!');
    } catch (error: any) {
      showToast.error(error.message || 'Failed to generate email');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptimizeSubject = async () => {
    if (!subjectToOptimize.trim()) {
      showToast.error('Please enter a subject line to optimize');
      return;
    }

    setIsOptimizing(true);
    try {
      const request: SubjectOptimizationRequest = {
        subject: subjectToOptimize.trim(),
        context: 'Email campaign',
        target_audience: 'Business professionals',
      };

      const response = await apiService.optimizeSubjectLine(request);
      setOptimizedSubjects(response.optimized_subjects);
      
      if (onSubjectOptimized) {
        onSubjectOptimized(response.optimized_subjects);
      }

      showToast.success('Subject line optimized successfully!');
    } catch (error: any) {
      showToast.error(error.message || 'Failed to optimize subject line');
    } finally {
      setIsOptimizing(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showToast.success('Copied to clipboard!');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Email Generation */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            AI Email Generator
          </h3>
          <p className="text-sm text-gray-600">Generate professional emails with AI assistance</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Prompt
              </label>
              <textarea
                className="w-full rounded-xl border border-gray-200 px-4 py-3 text-sm placeholder-gray-400 shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20"
                rows={3}
                placeholder="e.g., Write a professional email introducing our new product to potential customers"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tone
                </label>
                <select
                  className="w-full rounded-xl border border-gray-200 px-4 py-3 text-sm shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20"
                  value={tone}
                  onChange={(e) => setTone(e.target.value as any)}
                >
                  <option value="professional">Professional</option>
                  <option value="friendly">Friendly</option>
                  <option value="casual">Casual</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Length
                </label>
                <select
                  className="w-full rounded-xl border border-gray-200 px-4 py-3 text-sm shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20"
                  value={length}
                  onChange={(e) => setLength(e.target.value as any)}
                >
                  <option value="short">Short</option>
                  <option value="medium">Medium</option>
                  <option value="long">Long</option>
                </select>
              </div>
            </div>

            <Button
              onClick={handleGenerateEmail}
              isLoading={isGenerating}
              className="w-full"
              style={{
                background: 'linear-gradient(to right, #8b5cf6, #a855f7)',
                borderRadius: '0.75rem'
              }}
            >
              {isGenerating ? 'Generating...' : 'Generate Email'}
            </Button>

            {generatedContent && (
              <div className="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Subject:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedContent.subject)}
                    >
                      Copy
                    </Button>
                  </div>
                  <div className="text-sm font-medium text-gray-900">{generatedContent.subject}</div>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Content:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedContent.content)}
                    >
                      Copy
                    </Button>
                  </div>
                  <div className="text-sm text-gray-900 whitespace-pre-wrap">{generatedContent.content}</div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Subject Line Optimization */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Subject Line Optimizer
          </h3>
          <p className="text-sm text-gray-600">Optimize your subject lines for better open rates</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Input
              label="Subject Line to Optimize"
              placeholder="e.g., Quick question about your business"
              value={subjectToOptimize}
              onChange={(e) => setSubjectToOptimize(e.target.value)}
            />

            <Button
              onClick={handleOptimizeSubject}
              isLoading={isOptimizing}
              className="w-full"
              style={{
                background: 'linear-gradient(to right, #0284c7, #0ea5e9)',
                borderRadius: '0.75rem'
              }}
            >
              {isOptimizing ? 'Optimizing...' : 'Optimize Subject Line'}
            </Button>

            {optimizedSubjects.length > 0 && (
              <div className="mt-4 space-y-2">
                <span className="text-sm font-medium text-gray-700">Optimized Suggestions:</span>
                {optimizedSubjects.map((subject, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                  >
                    <span className="text-sm text-gray-900">{subject}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(subject)}
                    >
                      Copy
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIAssistant;
