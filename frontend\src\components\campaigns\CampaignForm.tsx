import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Card, CardHeader, CardContent } from '../ui';
import type { Campaign } from '../../types';
import type { CampaignTemplate } from '../../data/campaignTemplates';

const campaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  campaign_type: z.enum(['outreach', 'follow_up', 'nurture', 'transactional']),
  from_name: z.string().min(1, 'From name is required'),
  from_email: z.string().email('Please enter a valid email address'),
  reply_to_email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

interface CampaignFormProps {
  campaign?: Campaign;
  template?: CampaignTemplate;
  onSubmit: (data: CampaignFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const CampaignForm: React.FC<CampaignFormProps> = ({
  campaign,
  template,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: campaign?.name || template?.campaign.name || '',
      description: campaign?.description || template?.campaign.description || '',
      campaign_type: campaign?.campaign_type || 'outreach',
      from_name: campaign?.from_name || 'Your Name',
      from_email: campaign?.from_email || '<EMAIL>',
      reply_to_email: campaign?.reply_to_email || '',
    },
  });

  const handleFormSubmit = async (data: CampaignFormData) => {
    try {
      setError(null);
      await onSubmit(data);
    } catch (err: any) {
      setError(err.message || 'Failed to save campaign');
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {campaign ? 'Edit Campaign' : template ? `Create Campaign from "${template.name}"` : 'Create New Campaign'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {campaign ? 'Update your campaign details' : template ? template.description : 'Set up your email outreach campaign'}
          </p>
          {template && (
            <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Using Template</span>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                This campaign will include {template.campaign.sequences.length} pre-written email{template.campaign.sequences.length !== 1 ? 's' : ''} that you can customize.
              </p>
            </div>
          )}
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {error && (
              <div 
                className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-xl flex items-center"
                style={{
                  background: 'linear-gradient(to right, #fef2f2, #fee2e2)',
                  borderColor: '#fca5a5'
                }}
              >
                <svg className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {error}
              </div>
            )}

            {/* Campaign Basic Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Campaign Details</h3>
              
              <Input
                label="Campaign Name"
                placeholder="e.g., Product Launch Outreach"
                error={errors.name?.message}
                {...register('name')}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign Type
                </label>
                <select
                  className="block w-full rounded-xl border border-gray-200 px-4 py-3 text-sm shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 hover:border-gray-300"
                  {...register('campaign_type')}
                >
                  <option value="outreach">Outreach</option>
                  <option value="follow_up">Follow-up</option>
                  <option value="nurture">Nurture</option>
                  <option value="transactional">Transactional</option>
                </select>
                {errors.campaign_type && (
                  <p className="mt-1 text-sm text-red-600">{errors.campaign_type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  className="block w-full rounded-xl border border-gray-200 px-4 py-3 text-sm placeholder-gray-400 shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 hover:border-gray-300"
                  rows={3}
                  placeholder="Describe the purpose and goals of this campaign..."
                  {...register('description')}
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>
            </div>

            {/* Email Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Email Settings</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="From Name"
                  placeholder="Your Name"
                  error={errors.from_name?.message}
                  {...register('from_name')}
                />

                <Input
                  label="From Email"
                  type="email"
                  placeholder="<EMAIL>"
                  error={errors.from_email?.message}
                  {...register('from_email')}
                />
              </div>

              <Input
                label="Reply-To Email (Optional)"
                type="email"
                placeholder="Leave empty to use From Email"
                error={errors.reply_to_email?.message}
                {...register('reply_to_email')}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isLoading}
                disabled={isLoading}
                style={{
                  background: 'linear-gradient(to right, #0284c7, #a855f7)',
                  borderRadius: '1rem'
                }}
              >
                {isLoading ? 'Saving...' : campaign ? 'Update Campaign' : 'Create Campaign'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignForm;
