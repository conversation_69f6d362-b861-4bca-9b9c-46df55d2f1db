import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, SkeletonCard, useConfirmDialog, Checkbox, BulkActionsToolbar } from '../ui';
import { useMultiSelect, usePerformanceMonitor } from '../../hooks';
import type { Campaign } from '../../types';

interface CampaignListProps {
  campaigns: Campaign[];
  onEdit: (campaign: Campaign) => void;
  onDelete: (campaignId: string) => void;
  onDuplicate: (campaign: Campaign) => void;
  onViewAnalytics: (campaign: Campaign) => void;
  onEditSequences: (campaign: Campaign) => void;
  onBulkDelete?: (campaignIds: string[]) => void;
  onBulkDuplicate?: (campaigns: Campaign[]) => void;
  onBulkStatusChange?: (campaignIds: string[], status: string) => void;
  isLoading?: boolean;
  enableBulkActions?: boolean;
}

const CampaignList: React.FC<CampaignListProps> = ({
  campaigns,
  onEdit,
  onDelete,
  onDuplicate,
  onViewAnalytics,
  onEditSequences,
  onBulkDelete,
  onBulkDuplicate,
  onBulkStatusChange,
  isLoading = false,
  enableBulkActions = true,
}) => {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { confirm, ConfirmDialog } = useConfirmDialog();

  // Performance monitoring
  usePerformanceMonitor('CampaignList', {
    threshold: 16, // Log if render takes more than 16ms
  });

  const multiSelect = useMultiSelect({
    items: campaigns,
    getItemId: (campaign) => campaign.id,
  });

  const handleDelete = (campaign: Campaign) => {
    confirm({
      title: 'Delete Campaign',
      message: `Are you sure you want to delete "${campaign.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      variant: 'danger',
      onConfirm: async () => {
        setDeletingId(campaign.id);
        try {
          await onDelete(campaign.id);
        } finally {
          setDeletingId(null);
        }
      },
    });
  };

  const handleBulkDelete = () => {
    const selectedCampaigns = multiSelect.selectedItems;
    confirm({
      title: 'Delete Campaigns',
      message: `Are you sure you want to delete ${selectedCampaigns.length} campaigns? This action cannot be undone.`,
      confirmText: 'Delete All',
      variant: 'danger',
      onConfirm: async () => {
        const campaignIds = selectedCampaigns.map(c => c.id);
        await onBulkDelete?.(campaignIds);
        multiSelect.clearSelection();
      },
    });
  };

  const handleBulkDuplicate = () => {
    const selectedCampaigns = multiSelect.selectedItems;
    onBulkDuplicate?.(selectedCampaigns);
    multiSelect.clearSelection();
  };

  const handleBulkStatusChange = (status: string) => {
    const campaignIds = multiSelect.selectedItems.map(c => c.id);
    onBulkStatusChange?.(campaignIds, status);
    multiSelect.clearSelection();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const bulkActions = [
    {
      id: 'duplicate',
      label: 'Duplicate',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      ),
      onClick: handleBulkDuplicate,
      variant: 'secondary' as const,
    },
    {
      id: 'activate',
      label: 'Activate',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z" />
        </svg>
      ),
      onClick: () => handleBulkStatusChange('active'),
      variant: 'primary' as const,
    },
    {
      id: 'pause',
      label: 'Pause',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      onClick: () => handleBulkStatusChange('paused'),
      variant: 'secondary' as const,
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      ),
      onClick: handleBulkDelete,
      variant: 'danger' as const,
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <SkeletonCard key={i} className="p-6" />
        ))}
      </div>
    );
  }

  if (campaigns.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No campaigns yet</h3>
          <p className="text-gray-600 mb-6">Create your first email campaign to start reaching out to prospects.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      {ConfirmDialog}

      {/* Bulk Actions Toolbar */}
      {enableBulkActions && (
        <BulkActionsToolbar
          selectedCount={multiSelect.selectedCount}
          totalCount={campaigns.length}
          actions={bulkActions}
          onClearSelection={multiSelect.clearSelection}
        />
      )}

      {/* Select All Checkbox */}
      {enableBulkActions && campaigns.length > 0 && (
        <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-xl">
          <Checkbox
            checked={multiSelect.isAllSelected}
            indeterminate={multiSelect.isIndeterminate}
            onChange={multiSelect.toggleAll}
            label={`Select all ${campaigns.length} campaigns`}
          />
        </div>
      )}

      <div className="space-y-4">
        {campaigns.map((campaign) => (
        <Card key={campaign.id} className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
              {/* Selection Checkbox */}
              {enableBulkActions && (
                <div className="flex items-start mr-4">
                  <Checkbox
                    checked={multiSelect.isSelected(campaign)}
                    onChange={() => multiSelect.toggleItem(campaign)}
                    className="mt-1"
                  />
                </div>
              )}

              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{campaign.name}</h3>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(campaign.status)}`}>
                    {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                  </span>
                </div>
                
                {campaign.description && (
                  <p className="text-gray-600 mb-3">{campaign.description}</p>
                )}

                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 text-xs sm:text-sm">Created:</span>
                    <div className="font-medium text-sm sm:text-base">{formatDate(campaign.created_at)}</div>
                  </div>
                  <div>
                    <span className="text-gray-500 text-xs sm:text-sm">Contacts:</span>
                    <div className="font-medium text-sm sm:text-base">{campaign.contacts_count || 0}</div>
                  </div>
                  <div>
                    <span className="text-gray-500 text-xs sm:text-sm">Sent:</span>
                    <div className="font-medium text-sm sm:text-base">{campaign.sent_count || 0}</div>
                  </div>
                  <div>
                    <span className="text-gray-500 text-xs sm:text-sm">Open Rate:</span>
                    <div className="font-medium text-sm sm:text-base">
                      {campaign.sent_count > 0
                        ? `${Math.round(((campaign.opened_count || 0) / campaign.sent_count) * 100)}%`
                        : '0%'
                      }
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 ml-0 sm:ml-6 mt-4 sm:mt-0">
                {/* Primary Actions - Always visible */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditSequences(campaign)}
                    className="flex-1 sm:flex-none text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                  >
                    <svg className="w-4 h-4 sm:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="hidden sm:inline">Sequences</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewAnalytics(campaign)}
                    className="flex-1 sm:flex-none text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  >
                    <svg className="w-4 h-4 sm:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className="hidden sm:inline">Analytics</span>
                  </Button>
                </div>

                {/* Secondary Actions */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(campaign)}
                    className="flex-1 sm:flex-none text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                  >
                    <svg className="w-4 h-4 sm:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span className="hidden sm:inline">Edit</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDuplicate(campaign)}
                    className="flex-1 sm:flex-none text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                  >
                    <svg className="w-4 h-4 sm:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span className="hidden sm:inline">Duplicate</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(campaign)}
                    disabled={deletingId === campaign.id}
                    className="flex-1 sm:flex-none text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    {deletingId === campaign.id ? (
                      <div className="w-4 h-4 sm:mr-1 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <svg className="w-4 h-4 sm:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    )}
                    <span className="hidden sm:inline">Delete</span>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
    </>
  );
};

export default CampaignList;
