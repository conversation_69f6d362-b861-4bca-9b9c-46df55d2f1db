import React, { useState } from 'react';
import { But<PERSON>, Select } from '../ui';

interface BulkActionsToolbarProps {
  selectedCount: number;
  onBulkStatusUpdate: (status: string) => Promise<void>;
  onClearSelection: () => void;
  isLoading?: boolean;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedCount,
  onBulkStatusUpdate,
  onClearSelection,
  isLoading = false,
}) => {
  const [selectedStatus, setSelectedStatus] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async () => {
    if (!selectedStatus) return;
    
    setIsUpdating(true);
    try {
      await onBulkStatusUpdate(selectedStatus);
      setSelectedStatus('');
    } finally {
      setIsUpdating(false);
    }
  };

  const statusOptions = [
    { value: '', label: 'Select new status...' },
    { value: 'active', label: 'Active' },
    { value: 'unsubscribed', label: 'Unsubscribed' },
    { value: 'bounced', label: 'Bounced' },
    { value: 'complained', label: 'Complained' },
    { value: 'suppressed', label: 'Suppressed' },
  ];

  if (selectedCount === 0) return null;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-blue-900">
            {selectedCount} contact{selectedCount !== 1 ? 's' : ''} selected
          </span>
          
          <div className="flex items-center space-x-2">
            <Select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              options={statusOptions}
              className="min-w-[200px]"
              disabled={isUpdating || isLoading}
            />
            
            <Button
              onClick={handleStatusUpdate}
              disabled={!selectedStatus || isUpdating || isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 text-sm"
            >
              {isUpdating ? 'Updating...' : 'Update Status'}
            </Button>
          </div>
        </div>
        
        <Button
          onClick={onClearSelection}
          variant="outline"
          className="text-gray-600 hover:text-gray-800 px-3 py-2 text-sm"
          disabled={isUpdating || isLoading}
        >
          Clear Selection
        </Button>
      </div>
    </div>
  );
};

export default BulkActionsToolbar;
