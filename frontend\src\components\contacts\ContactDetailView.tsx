import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { Contact } from '../../types/index';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Input from '../ui/Input';
import Label from '../ui/Label';
import Textarea from '../ui/TextArea';
import Select from '../ui/Select';
import Checkbox from '../ui/Checkbox';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../ui/Tabs';
import { toast } from 'react-hot-toast';
import {
  User,
  Mail,
  Phone,
  Building,
  MapPin,
  Globe,
  Calendar,
  BarChart3,
  Tag,
  FileText,
  Settings,
  ExternalLink,
  Edit,
  ArrowLeft,
  Save,
  X,
  Plus
} from 'lucide-react';

// Validation schema for contact editing
const contactSchema = z.object({
  // Basic Information
  email: z.string().email('Please enter a valid email address'),
  first_name: z.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),
  last_name: z.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),
  full_name: z.string().optional(),
  company: z.string().max(255, 'Company name must be less than 255 characters').optional(),
  job_title: z.string().max(255, 'Job title must be less than 255 characters').optional(),
  phone: z.string().max(50, 'Phone number must be less than 50 characters').optional(),
  website: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),

  // Social Media
  linkedin_url: z.string().url('Please enter a valid LinkedIn URL').or(z.literal('')).optional(),
  twitter_handle: z.string().max(100, 'Twitter handle must be less than 100 characters').optional(),

  // Address Information
  address_line1: z.string().max(255, 'Address line 1 must be less than 255 characters').optional(),
  address_line2: z.string().max(255, 'Address line 2 must be less than 255 characters').optional(),
  city: z.string().max(100, 'City must be less than 100 characters').optional(),
  state: z.string().max(100, 'State must be less than 100 characters').optional(),
  postal_code: z.string().max(20, 'Postal code must be less than 20 characters').optional(),
  country: z.string().max(100, 'Country must be less than 100 characters').optional(),

  // Contact Status & Preferences
  status: z.enum(['active', 'unsubscribed', 'bounced', 'complained', 'suppressed']),
  source: z.string().optional(),
  email_verified: z.boolean().optional(),
  accepts_marketing: z.boolean().optional(),
  preferred_language: z.string().max(10, 'Language code must be less than 10 characters').optional(),
  timezone: z.string().max(50, 'Timezone must be less than 50 characters').optional(),

  // Advanced Features
  lead_score: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
});

type ContactFormData = z.infer<typeof contactSchema>;

interface ContactDetailViewProps {
  contact: Contact;
  onEdit: () => void;
  onBack: () => void;
  onUpdate?: (formData: any) => Promise<Contact>;
  isLoading?: boolean;
}

export const ContactDetailView: React.FC<ContactDetailViewProps> = ({
  contact,
  onEdit,
  onBack,
  onUpdate,
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [tags, setTags] = useState<string[]>(contact.tags || []);
  const [newTag, setNewTag] = useState('');
  const [customFields, setCustomFields] = useState<Record<string, any>>(contact.custom_fields || {});

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
    setValue,
    watch
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      email: contact.email || '',
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      full_name: contact.full_name || '',
      company: contact.company || '',
      job_title: contact.job_title || '',
      phone: contact.phone || '',
      website: contact.website || '',
      linkedin_url: contact.linkedin_url || '',
      twitter_handle: contact.twitter_handle || '',
      address_line1: contact.address_line1 || '',
      address_line2: contact.address_line2 || '',
      city: contact.city || '',
      state: contact.state || '',
      postal_code: contact.postal_code || '',
      country: contact.country || '',
      status: contact.status,
      source: contact.source || '',
      email_verified: contact.email_verified || false,
      accepts_marketing: contact.accepts_marketing || false,
      preferred_language: contact.preferred_language || '',
      timezone: contact.timezone || '',
      lead_score: contact.lead_score || 0,
      notes: contact.notes || '',
    }
  });

  // Update form when contact changes
  useEffect(() => {
    reset({
      email: contact.email || '',
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      full_name: contact.full_name || '',
      company: contact.company || '',
      job_title: contact.job_title || '',
      phone: contact.phone || '',
      website: contact.website || '',
      linkedin_url: contact.linkedin_url || '',
      twitter_handle: contact.twitter_handle || '',
      address_line1: contact.address_line1 || '',
      address_line2: contact.address_line2 || '',
      city: contact.city || '',
      state: contact.state || '',
      postal_code: contact.postal_code || '',
      country: contact.country || '',
      status: contact.status,
      source: contact.source || '',
      email_verified: contact.email_verified || false,
      accepts_marketing: contact.accepts_marketing || false,
      preferred_language: contact.preferred_language || '',
      timezone: contact.timezone || '',
      lead_score: contact.lead_score || 0,
      notes: contact.notes || '',
    });
    setTags(contact.tags || []);
    setCustomFields(contact.custom_fields || {});
  }, [contact, reset]);

  const handleSaveSection = async (data: ContactFormData) => {
    if (!onUpdate) {
      toast.error('Update functionality not available');
      return;
    }

    try {
      setIsSaving(true);

      // Prepare the update data
      const updateData = {
        ...data,
        tags,
        custom_fields: customFields
      };

      // Call the parent's update function
      await onUpdate(updateData);

      setEditingSection(null);
      toast.success('Contact updated successfully!');

    } catch (error) {
      console.error('Error updating contact:', error);
      toast.error('Failed to update contact');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingSection(null);
    reset();
    setTags(contact.tags || []);
    setCustomFields(contact.custom_fields || {});
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleAddCustomField = (key: string, value: any) => {
    setCustomFields({ ...customFields, [key]: value });
  };

  const handleRemoveCustomField = (key: string) => {
    const newFields = { ...customFields };
    delete newFields[key];
    setCustomFields(newFields);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'unsubscribed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'bounced':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'complained':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'suppressed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDisplayName = () => {
    if (contact?.full_name) return contact.full_name;
    if (contact?.first_name && contact?.last_name) {
      return `${contact.first_name} ${contact.last_name}`;
    }
    if (contact?.first_name) return contact.first_name;
    return contact?.email || 'Unknown Contact';
  };

  const getEngagementRate = (total: number, base: number) => {
    if (!base || base === 0) return 0;
    return Math.round((total / base) * 100);
  };

  // Inline editing component
  const InlineEditField: React.FC<{
    label: string;
    value: string | number | boolean | undefined;
    fieldName: keyof ContactFormData;
    type?: 'text' | 'email' | 'url' | 'textarea' | 'select' | 'checkbox' | 'number';
    options?: { value: string; label: string }[];
    section: string;
  }> = ({ label, value, fieldName, type = 'text', options, section }) => {
    const isEditing = editingSection === section;
    const fieldError = errors[fieldName];

    if (isEditing) {
      return (
        <div className="space-y-1">
          <Label htmlFor={fieldName}>{label}</Label>
          <Controller
            name={fieldName}
            control={control}
            render={({ field }) => {
              switch (type) {
                case 'textarea':
                  return (
                    <Textarea
                      {...field}
                      id={fieldName}
                      placeholder={`Enter ${label.toLowerCase()}`}
                      className={fieldError ? 'border-red-500' : ''}
                    />
                  );
                case 'select':
                  return (
                    <Select
                      {...field}
                      id={fieldName}
                      className={fieldError ? 'border-red-500' : ''}
                    >
                      <option value="">Select {label.toLowerCase()}</option>
                      {options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </Select>
                  );
                case 'checkbox':
                  return (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        {...field}
                        id={fieldName}
                        checked={field.value as boolean}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                      <Label htmlFor={fieldName}>{label}</Label>
                    </div>
                  );
                case 'number':
                  return (
                    <Input
                      {...field}
                      id={fieldName}
                      type="number"
                      placeholder={`Enter ${label.toLowerCase()}`}
                      className={fieldError ? 'border-red-500' : ''}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  );
                default:
                  return (
                    <Input
                      {...field}
                      id={fieldName}
                      type={type}
                      placeholder={`Enter ${label.toLowerCase()}`}
                      className={fieldError ? 'border-red-500' : ''}
                    />
                  );
              }
            }}
          />
          {fieldError && (
            <p className="text-sm text-red-600">{fieldError.message}</p>
          )}
        </div>
      );
    }

    // Display mode
    const displayValue = () => {
      if (type === 'checkbox') {
        return value ? 'Yes' : 'No';
      }
      if (type === 'select' && options) {
        const option = options.find(opt => opt.value === value);
        return option?.label || value || 'Not set';
      }
      return value || 'Not set';
    };

    return (
      <div className="space-y-1">
        <Label className="text-sm font-medium text-gray-700">{label}</Label>
        <p className="text-sm text-gray-900">{displayValue()}</p>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{getDisplayName()}</h1>
            <p className="text-gray-600">{contact.email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge className={getStatusColor(contact.status || 'active')}>
            {(contact.status || 'active').charAt(0).toUpperCase() + (contact.status || 'active').slice(1)}
          </Badge>
          <Button onClick={onEdit} className="flex items-center space-x-2">
            <Edit className="w-4 h-4" />
            <span>Edit Contact</span>
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contact">Contact Info</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="notes">Notes & Tags</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Basic Info Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
                  <User className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'basic' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('basic')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <InlineEditField
                    label="First Name"
                    value={contact.first_name}
                    fieldName="first_name"
                    section="basic"
                  />
                  <InlineEditField
                    label="Last Name"
                    value={contact.last_name}
                    fieldName="last_name"
                    section="basic"
                  />
                  <InlineEditField
                    label="Email"
                    value={contact.email}
                    fieldName="email"
                    type="email"
                    section="basic"
                  />
                  <InlineEditField
                    label="Company"
                    value={contact.company}
                    fieldName="company"
                    section="basic"
                  />
                  <InlineEditField
                    label="Job Title"
                    value={contact.job_title}
                    fieldName="job_title"
                    section="basic"
                  />
                  <InlineEditField
                    label="Status"
                    value={contact.status}
                    fieldName="status"
                    type="select"
                    section="basic"
                    options={[
                      { value: 'active', label: 'Active' },
                      { value: 'unsubscribed', label: 'Unsubscribed' },
                      { value: 'bounced', label: 'Bounced' },
                      { value: 'complained', label: 'Complained' },
                      { value: 'suppressed', label: 'Suppressed' }
                    ]}
                  />
              </CardContent>
            </Card>

            {/* Contact Methods Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Contact Methods</CardTitle>
                  <Phone className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'contact' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('contact')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <InlineEditField
                    label="Phone"
                    value={contact.phone}
                    fieldName="phone"
                    section="contact"
                  />
                  <InlineEditField
                    label="Website"
                    value={contact.website}
                    fieldName="website"
                    type="url"
                    section="contact"
                  />
                  <InlineEditField
                    label="LinkedIn URL"
                    value={contact.linkedin_url}
                    fieldName="linkedin_url"
                    type="url"
                    section="contact"
                  />
                  <InlineEditField
                    label="Twitter Handle"
                    value={contact.twitter_handle}
                    fieldName="twitter_handle"
                    section="contact"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Engagement Summary Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Engagement Summary</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'engagement' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('engagement')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Total Opens</Label>
                    <p className="text-sm text-gray-900">{contact.total_opens || 0}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Total Clicks</Label>
                    <p className="text-sm text-gray-900">{contact.total_clicks || 0}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Total Replies</Label>
                    <p className="text-sm text-gray-900">{contact.total_replies || 0}</p>
                  </div>
                  <InlineEditField
                    label="Lead Score (0-100)"
                    value={contact.lead_score}
                    fieldName="lead_score"
                    type="number"
                    section="engagement"
                  />
                  <InlineEditField
                    label="Source"
                    value={contact.source}
                    fieldName="source"
                    section="engagement"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tags */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-sm font-medium">Tags</CardTitle>
                <Tag className="h-4 w-4 text-muted-foreground" />
              </div>
              {editingSection === 'tags' ? (
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    onClick={handleSubmit(handleSaveSection)}
                    disabled={isSaving}
                    className="flex items-center space-x-1"
                  >
                    <Save className="w-3 h-3" />
                    <span>Save</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="flex items-center space-x-1"
                  >
                    <X className="w-3 h-3" />
                    <span>Cancel</span>
                  </Button>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setEditingSection('tags')}
                  className="flex items-center space-x-1"
                >
                  <Edit className="w-3 h-3" />
                  <span>Edit</span>
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {editingSection === 'tags' ? (
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <span>{tag}</span>
                        <button
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 text-gray-500 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add new tag"
                      onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      onClick={handleAddTag}
                      disabled={!newTag.trim()}
                      className="flex items-center space-x-1"
                    >
                      <Plus className="w-3 h-3" />
                      <span>Add</span>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {tags.length > 0 ? (
                    tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No tags assigned</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Info Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Address Information */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Address Information</CardTitle>
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'address' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('address')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <InlineEditField
                    label="Address Line 1"
                    value={contact.address_line1}
                    fieldName="address_line1"
                    section="address"
                  />
                  <InlineEditField
                    label="Address Line 2"
                    value={contact.address_line2}
                    fieldName="address_line2"
                    section="address"
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <InlineEditField
                      label="City"
                      value={contact.city}
                      fieldName="city"
                      section="address"
                    />
                    <InlineEditField
                      label="State"
                      value={contact.state}
                      fieldName="state"
                      section="address"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <InlineEditField
                      label="Postal Code"
                      value={contact.postal_code}
                      fieldName="postal_code"
                      section="address"
                    />
                    <InlineEditField
                      label="Country"
                      value={contact.country}
                      fieldName="country"
                      section="address"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Social Media */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Social Media</CardTitle>
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'social' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('social')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <InlineEditField
                    label="LinkedIn URL"
                    value={contact.linkedin_url}
                    fieldName="linkedin_url"
                    type="url"
                    section="social"
                  />
                  <InlineEditField
                    label="Twitter Handle"
                    value={contact.twitter_handle}
                    fieldName="twitter_handle"
                    section="social"
                  />
                  <InlineEditField
                    label="Website"
                    value={contact.website}
                    fieldName="website"
                    type="url"
                    section="social"
                  />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Email Preferences */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Email Preferences</CardTitle>
                  <Mail className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'email-prefs' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('email-prefs')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <InlineEditField
                    label="Email Verified"
                    value={contact.email_verified}
                    fieldName="email_verified"
                    type="checkbox"
                    section="email-prefs"
                  />
                  <InlineEditField
                    label="Accepts Marketing"
                    value={contact.accepts_marketing}
                    fieldName="accepts_marketing"
                    type="checkbox"
                    section="email-prefs"
                  />
                  <InlineEditField
                    label="Preferred Language"
                    value={contact.preferred_language}
                    fieldName="preferred_language"
                    section="email-prefs"
                  />
                  <InlineEditField
                    label="Timezone"
                    value={contact.timezone}
                    fieldName="timezone"
                    section="email-prefs"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Additional Information</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'additional' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('additional')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Created</Label>
                    <p className="text-sm text-gray-900">{formatDate(contact.created_at)}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Last Updated</Label>
                    <p className="text-sm text-gray-900">{formatDate(contact.updated_at)}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">Last Contacted</Label>
                    <p className="text-sm text-gray-900">{formatDate(contact.last_contacted_at)}</p>
                  </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Notes & Tags Tab */}
        <TabsContent value="notes" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Notes */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Notes</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'notes' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('notes')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <InlineEditField
                  label="Notes"
                  value={contact.notes}
                  fieldName="notes"
                  type="textarea"
                  section="notes"
                />
              </CardContent>
            </Card>

            {/* Custom Fields */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-sm font-medium">Custom Fields</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </div>
                {editingSection === 'custom' ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSubmit(handleSaveSection)}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <Save className="w-3 h-3" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="flex items-center space-x-1"
                    >
                      <X className="w-3 h-3" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingSection('custom')}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="w-3 h-3" />
                    <span>Edit</span>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {editingSection === 'custom' ? (
                  <div className="space-y-4">
                    {Object.entries(customFields).map(([key, value]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Input
                          value={key}
                          onChange={(e) => {
                            const newFields = { ...customFields };
                            delete newFields[key];
                            newFields[e.target.value] = value;
                            setCustomFields(newFields);
                          }}
                          placeholder="Field name"
                          className="flex-1"
                        />
                        <Input
                          value={value}
                          onChange={(e) => handleAddCustomField(key, e.target.value)}
                          placeholder="Field value"
                          className="flex-1"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveCustomField(key)}
                          className="flex items-center space-x-1"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      size="sm"
                      onClick={() => handleAddCustomField(`field_${Date.now()}`, '')}
                      className="flex items-center space-x-1"
                    >
                      <Plus className="w-3 h-3" />
                      <span>Add Field</span>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(customFields).length > 0 ? (
                      Object.entries(customFields).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700">{key}:</span>
                          <span className="text-sm text-gray-900">{value}</span>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No custom fields</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
        </TabsContent>

        {/* Engagement Tab */}
        <TabsContent value="engagement" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Email Engagement Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span>Email Engagement</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-500">Total Opens</label>
                    <p className="text-2xl font-bold text-blue-600">{contact.total_opens || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">Total Clicks</label>
                    <p className="text-2xl font-bold text-green-600">{contact.total_clicks || 0}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Total Replies</label>
                  <p className="text-2xl font-bold text-purple-600">{contact.total_replies || 0}</p>
                </div>
              </CardContent>
            </Card>

            {/* Last Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Last Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Last Opened</label>
                  <p className="font-medium">{formatDate(contact.last_opened_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Clicked</label>
                  <p className="font-medium">{formatDate(contact.last_clicked_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Replied</label>
                  <p className="font-medium">{formatDate(contact.last_replied_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Contacted</label>
                  <p className="font-medium">{formatDate(contact.last_contacted_at)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContactDetailView;
