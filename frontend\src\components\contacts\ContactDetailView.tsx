import React, { useState, useEffect } from 'react';
import { Contact } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { 
  User, 
  Mail, 
  Phone, 
  Building, 
  MapPin, 
  Globe, 
  Calendar,
  BarChart3,
  Tag,
  FileText,
  Settings,
  ExternalLink,
  Edit,
  ArrowLeft
} from 'lucide-react';

interface ContactDetailViewProps {
  contact: Contact;
  onEdit: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

export const ContactDetailView: React.FC<ContactDetailViewProps> = ({
  contact,
  onEdit,
  onBack,
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'unsubscribed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'bounced':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'complained':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'suppressed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDisplayName = () => {
    if (contact.full_name) return contact.full_name;
    if (contact.first_name && contact.last_name) {
      return `${contact.first_name} ${contact.last_name}`;
    }
    if (contact.first_name) return contact.first_name;
    return contact.email;
  };

  const getEngagementRate = (total: number, base: number) => {
    if (!base || base === 0) return 0;
    return Math.round((total / base) * 100);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{getDisplayName()}</h1>
            <p className="text-gray-600">{contact.email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge className={getStatusColor(contact.status)}>
            {contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}
          </Badge>
          <Button onClick={onEdit} className="flex items-center space-x-2">
            <Edit className="w-4 h-4" />
            <span>Edit Contact</span>
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contact">Contact Info</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="notes">Notes & Tags</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Basic Info Card */}
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Name:</span>
                    <p className="font-medium">{getDisplayName()}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Email:</span>
                    <p className="font-medium">{contact.email}</p>
                  </div>
                  {contact.company && (
                    <div>
                      <span className="text-sm text-gray-500">Company:</span>
                      <p className="font-medium">{contact.company}</p>
                    </div>
                  )}
                  {contact.job_title && (
                    <div>
                      <span className="text-sm text-gray-500">Job Title:</span>
                      <p className="font-medium">{contact.job_title}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Contact Methods Card */}
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Contact Methods</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {contact.phone && (
                    <div>
                      <span className="text-sm text-gray-500">Phone:</span>
                      <p className="font-medium">{contact.phone}</p>
                    </div>
                  )}
                  {contact.website && (
                    <div>
                      <span className="text-sm text-gray-500">Website:</span>
                      <a 
                        href={contact.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-medium text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                      >
                        <span>{contact.website}</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </div>
                  )}
                  {contact.linkedin_url && (
                    <div>
                      <span className="text-sm text-gray-500">LinkedIn:</span>
                      <a 
                        href={contact.linkedin_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-medium text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                      >
                        <span>LinkedIn Profile</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Engagement Summary Card */}
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Engagement Summary</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Total Opens:</span>
                    <p className="font-medium">{contact.total_opens || 0}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Total Clicks:</span>
                    <p className="font-medium">{contact.total_clicks || 0}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Total Replies:</span>
                    <p className="font-medium">{contact.total_replies || 0}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Lead Score:</span>
                    <p className="font-medium">{contact.lead_score || 0}/100</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tags */}
          {contact.tags && contact.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium flex items-center space-x-2">
                  <Tag className="h-4 w-4" />
                  <span>Tags</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {contact.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Contact Info Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-500">First Name</label>
                    <p className="font-medium">{contact.first_name || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">Last Name</label>
                    <p className="font-medium">{contact.last_name || 'Not provided'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Full Name</label>
                  <p className="font-medium">{contact.full_name || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Email</label>
                  <p className="font-medium">{contact.email}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Phone</label>
                  <p className="font-medium">{contact.phone || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Building className="h-5 w-5" />
                  <span>Professional Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Company</label>
                  <p className="font-medium">{contact.company || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Job Title</label>
                  <p className="font-medium">{contact.job_title || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Website</label>
                  {contact.website ? (
                    <a
                      href={contact.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                    >
                      <span>{contact.website}</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  ) : (
                    <p className="font-medium">Not provided</p>
                  )}
                </div>
                <div>
                  <label className="text-sm text-gray-500">LinkedIn</label>
                  {contact.linkedin_url ? (
                    <a
                      href={contact.linkedin_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                    >
                      <span>LinkedIn Profile</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  ) : (
                    <p className="font-medium">Not provided</p>
                  )}
                </div>
                <div>
                  <label className="text-sm text-gray-500">Twitter</label>
                  <p className="font-medium">{contact.twitter_handle || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span>Address Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Address Line 1</label>
                  <p className="font-medium">{contact.address_line1 || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Address Line 2</label>
                  <p className="font-medium">{contact.address_line2 || 'Not provided'}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-500">City</label>
                    <p className="font-medium">{contact.city || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">State</label>
                    <p className="font-medium">{contact.state || 'Not provided'}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-500">Postal Code</label>
                    <p className="font-medium">{contact.postal_code || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">Country</label>
                    <p className="font-medium">{contact.country || 'Not provided'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Source & Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Timeline</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Source</label>
                  <p className="font-medium">{contact.source || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Created</label>
                  <p className="font-medium">{formatDate(contact.created_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Updated</label>
                  <p className="font-medium">{formatDate(contact.updated_at)}</p>
                </div>
                {contact.unsubscribed_at && (
                  <div>
                    <label className="text-sm text-gray-500">Unsubscribed</label>
                    <p className="font-medium">{formatDate(contact.unsubscribed_at)}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Engagement Tab */}
        <TabsContent value="engagement" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Email Engagement */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span>Email Engagement</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-500">Total Opens</label>
                    <p className="text-2xl font-bold text-blue-600">{contact.total_opens || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">Total Clicks</label>
                    <p className="text-2xl font-bold text-green-600">{contact.total_clicks || 0}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Total Replies</label>
                  <p className="text-2xl font-bold text-purple-600">{contact.total_replies || 0}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Lead Score</label>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${contact.lead_score || 0}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{contact.lead_score || 0}/100</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Last Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Last Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Last Opened</label>
                  <p className="font-medium">{formatDate(contact.last_opened_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Clicked</label>
                  <p className="font-medium">{formatDate(contact.last_clicked_at)}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Last Replied</label>
                  <p className="font-medium">{formatDate(contact.last_replied_at)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Email Preferences */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Email Preferences</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Email Verified</label>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${contact.email_verified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <p className="font-medium">{contact.email_verified ? 'Verified' : 'Not Verified'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Accepts Marketing</label>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${contact.accepts_marketing ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <p className="font-medium">{contact.accepts_marketing ? 'Yes' : 'No'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Preferred Language</label>
                  <p className="font-medium">{contact.preferred_language || 'en'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Timezone</label>
                  <p className="font-medium">{contact.timezone || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-500">Contact Status</label>
                  <Badge className={getStatusColor(contact.status)}>
                    {contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Custom Fields */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Custom Fields</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {contact.custom_fields && Object.keys(contact.custom_fields).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(contact.custom_fields).map(([key, value]) => (
                      <div key={key}>
                        <label className="text-sm text-gray-500 capitalize">{key.replace(/_/g, ' ')}</label>
                        <p className="font-medium">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No custom fields defined</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Notes & Tags Tab */}
        <TabsContent value="notes" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Tag className="h-5 w-5" />
                  <span>Tags</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {contact.tags && contact.tags.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {contact.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No tags assigned</p>
                )}
              </CardContent>
            </Card>

            {/* Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Notes</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {contact.notes ? (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="whitespace-pre-wrap">{contact.notes}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No notes available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContactDetailView;
