import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Card, CardHeader, CardContent } from '../ui';
import type { Contact } from '../../types';

const contactSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  first_name: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  company: z.string().optional(),
  job_title: z.string().optional(),
  phone: z.string().optional(),
});

type ContactFormData = z.infer<typeof contactSchema>;

interface ContactFormProps {
  contact?: Contact;
  onSubmit: (data: ContactFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const ContactForm: React.FC<ContactFormProps> = ({
  contact,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      email: contact?.email || '',
      first_name: contact?.first_name || '',
      last_name: contact?.last_name || '',
      company: contact?.company || '',
      job_title: contact?.job_title || '',
      phone: contact?.phone || '',
    },
  });

  const handleFormSubmit = async (data: ContactFormData) => {
    try {
      setError(null);
      await onSubmit(data);
    } catch (err: any) {
      setError(err.message || 'Failed to save contact');
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">
            {contact ? 'Edit Contact' : 'Add New Contact'}
          </h2>
          <p className="text-gray-600">
            {contact ? 'Update contact information' : 'Add a new contact to your database'}
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {error && (
              <div 
                className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-xl flex items-center"
                style={{
                  background: 'linear-gradient(to right, #fef2f2, #fee2e2)',
                  borderColor: '#fca5a5'
                }}
              >
                <svg className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {error}
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                error={errors.email?.message}
                {...register('email')}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="First Name"
                  placeholder="John"
                  error={errors.first_name?.message}
                  {...register('first_name')}
                />

                <Input
                  label="Last Name"
                  placeholder="Doe"
                  error={errors.last_name?.message}
                  {...register('last_name')}
                />
              </div>
            </div>

            {/* Professional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Professional Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Company"
                  placeholder="Acme Corp"
                  error={errors.company?.message}
                  {...register('company')}
                />

                <Input
                  label="Job Title"
                  placeholder="Marketing Manager"
                  error={errors.job_title?.message}
                  {...register('job_title')}
                />
              </div>

              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                error={errors.phone?.message}
                {...register('phone')}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isLoading}
                disabled={isLoading}
                style={{
                  background: 'linear-gradient(to right, #0284c7, #a855f7)',
                  borderRadius: '1rem'
                }}
              >
                {isLoading ? 'Saving...' : contact ? 'Update Contact' : 'Add Contact'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactForm;
