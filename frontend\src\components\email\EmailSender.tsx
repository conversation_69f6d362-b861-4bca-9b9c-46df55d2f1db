import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, showToast } from '../ui';
import { apiService } from '../../services/api';
import type { Campaign, Contact, EmailSequence } from '../../types';

interface EmailSenderProps {
  campaign: Campaign;
  sequences: EmailSequence[];
  contacts: Contact[];
  onSendComplete?: (results: any) => void;
  className?: string;
}

interface SendingProgress {
  total: number;
  sent: number;
  failed: number;
  current?: string;
}

const EmailSender: React.FC<EmailSenderProps> = ({
  campaign,
  sequences,
  contacts,
  onSendComplete,
  className = '',
}) => {
  const [isSending, setIsSending] = useState(false);
  const [progress, setProgress] = useState<SendingProgress>({ total: 0, sent: 0, failed: 0 });
  const [sendingLogs, setSendingLogs] = useState<string[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<number[]>([]);
  const [selectedSequence, setSelectedSequence] = useState<number | null>(null);
  const [testMode, setTestMode] = useState(true);

  useEffect(() => {
    // Select all contacts by default
    setSelectedContacts(contacts.map(c => c.id));
    // Select first sequence by default
    if (sequences.length > 0) {
      setSelectedSequence(sequences[0].id);
    }
  }, [contacts, sequences]);

  const handleSendEmails = async () => {
    if (selectedContacts.length === 0) {
      showToast.error('Please select at least one contact');
      return;
    }

    if (!selectedSequence) {
      showToast.error('Please select an email sequence');
      return;
    }

    setIsSending(true);
    setProgress({ total: selectedContacts.length, sent: 0, failed: 0 });
    setSendingLogs([]);

    try {
      const sequence = sequences.find(s => s.id === selectedSequence);
      if (!sequence) {
        throw new Error('Selected sequence not found');
      }

      // Send emails in batches to avoid overwhelming the server
      const batchSize = 10;
      const batches = [];
      for (let i = 0; i < selectedContacts.length; i += batchSize) {
        batches.push(selectedContacts.slice(i, i + batchSize));
      }

      let totalSent = 0;
      let totalFailed = 0;

      for (const batch of batches) {
        const batchPromises = batch.map(async (contactId) => {
          const contact = contacts.find(c => c.id === contactId);
          if (!contact) return { success: false, error: 'Contact not found' };

          try {
            setProgress(prev => ({ ...prev, current: contact.email }));
            
            // Personalize email content
            const personalizedSubject = sequence.subject_line
              .replace(/\{\{firstName\}\}/g, contact.first_name || '')
              .replace(/\{\{lastName\}\}/g, contact.last_name || '')
              .replace(/\{\{company\}\}/g, contact.company || '')
              .replace(/\{\{email\}\}/g, contact.email);

            const personalizedContent = sequence.email_content
              .replace(/\{\{firstName\}\}/g, contact.first_name || '')
              .replace(/\{\{lastName\}\}/g, contact.last_name || '')
              .replace(/\{\{company\}\}/g, contact.company || '')
              .replace(/\{\{email\}\}/g, contact.email);

            if (testMode) {
              // In test mode, just simulate sending
              await new Promise(resolve => setTimeout(resolve, 100));
              setSendingLogs(prev => [...prev, `✅ Test: Would send to ${contact.email}`]);
              return { success: true };
            } else {
              // Actually send the email
              const emailData = {
                campaign_id: campaign.id,
                sequence_id: sequence.id,
                contact_id: contact.id,
                to_email: contact.email,
                to_name: `${contact.first_name} ${contact.last_name}`.trim(),
                subject: personalizedSubject,
                content: personalizedContent,
                from_name: campaign.from_name,
                from_email: campaign.from_email,
                reply_to: campaign.reply_to_email || campaign.from_email,
              };

              await apiService.sendEmail(emailData);
              setSendingLogs(prev => [...prev, `✅ Sent to ${contact.email}`]);
              return { success: true };
            }
          } catch (error: any) {
            setSendingLogs(prev => [...prev, `❌ Failed to send to ${contact.email}: ${error.message}`]);
            return { success: false, error: error.message };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        const batchSent = batchResults.filter(r => r.success).length;
        const batchFailed = batchResults.filter(r => !r.success).length;

        totalSent += batchSent;
        totalFailed += batchFailed;

        setProgress({
          total: selectedContacts.length,
          sent: totalSent,
          failed: totalFailed,
        });

        // Small delay between batches
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const results = {
        total: selectedContacts.length,
        sent: totalSent,
        failed: totalFailed,
        testMode,
      };

      if (onSendComplete) {
        onSendComplete(results);
      }

      if (testMode) {
        showToast.success(`Test completed: ${totalSent} emails would be sent`);
      } else {
        showToast.success(`Campaign sent: ${totalSent} emails sent successfully`);
      }

    } catch (error: any) {
      showToast.error(error.message || 'Failed to send emails');
      setSendingLogs(prev => [...prev, `❌ Error: ${error.message}`]);
    } finally {
      setIsSending(false);
      setProgress(prev => ({ ...prev, current: undefined }));
    }
  };

  const toggleContactSelection = (contactId: number) => {
    setSelectedContacts(prev => 
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const selectAllContacts = () => {
    setSelectedContacts(contacts.map(c => c.id));
  };

  const deselectAllContacts = () => {
    setSelectedContacts([]);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Email Configuration */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
            Send Campaign: {campaign.name}
          </h3>
          <p className="text-sm text-gray-600">Configure and send your email campaign</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Test Mode Toggle */}
            <div className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <input
                type="checkbox"
                id="testMode"
                checked={testMode}
                onChange={(e) => setTestMode(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="testMode" className="text-sm font-medium text-yellow-800">
                Test Mode (emails won't actually be sent)
              </label>
            </div>

            {/* Sequence Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Sequence
              </label>
              <select
                className="w-full rounded-xl border border-gray-200 px-4 py-3 text-sm shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20"
                value={selectedSequence || ''}
                onChange={(e) => setSelectedSequence(Number(e.target.value))}
              >
                <option value="">Select a sequence</option>
                {sequences.map((sequence) => (
                  <option key={sequence.id} value={sequence.id}>
                    {sequence.name} - {sequence.subject_line}
                  </option>
                ))}
              </select>
            </div>

            {/* Contact Selection */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">
                  Recipients ({selectedContacts.length} of {contacts.length} selected)
                </label>
                <div className="space-x-2">
                  <Button variant="outline" size="sm" onClick={selectAllContacts}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={deselectAllContacts}>
                    Deselect All
                  </Button>
                </div>
              </div>
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-2 space-y-1">
                {contacts.map((contact) => (
                  <label
                    key={contact.id}
                    className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={selectedContacts.includes(contact.id)}
                      onChange={() => toggleContactSelection(contact.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-900">
                      {contact.first_name} {contact.last_name} ({contact.email})
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Send Button */}
            <Button
              onClick={handleSendEmails}
              isLoading={isSending}
              disabled={isSending || selectedContacts.length === 0 || !selectedSequence}
              className="w-full"
              style={{
                background: testMode 
                  ? 'linear-gradient(to right, #f59e0b, #d97706)' 
                  : 'linear-gradient(to right, #dc2626, #b91c1c)',
                borderRadius: '0.75rem'
              }}
            >
              {isSending 
                ? `${testMode ? 'Testing' : 'Sending'}... (${progress.sent}/${progress.total})`
                : testMode 
                  ? `Test Campaign (${selectedContacts.length} recipients)`
                  : `Send Campaign (${selectedContacts.length} recipients)`
              }
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress and Logs */}
      {(isSending || sendingLogs.length > 0) && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              {testMode ? 'Test' : 'Sending'} Progress
            </h3>
          </CardHeader>
          <CardContent>
            {isSending && (
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{progress.sent + progress.failed}/{progress.total}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${((progress.sent + progress.failed) / progress.total) * 100}%`
                    }}
                  ></div>
                </div>
                {progress.current && (
                  <p className="text-sm text-gray-600 mt-2">
                    Currently processing: {progress.current}
                  </p>
                )}
              </div>
            )}

            {sendingLogs.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Logs</h4>
                <div className="max-h-40 overflow-y-auto bg-gray-50 rounded-lg p-3 text-sm font-mono">
                  {sendingLogs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EmailSender;
