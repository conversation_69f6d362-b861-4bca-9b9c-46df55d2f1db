import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, Input } from '../ui';
import { apiService } from '../../services/api';
import type { EmailSequence } from '../../types';

interface AISequenceGeneratorProps {
  campaignId: number;
  onGenerate: (sequences: EmailSequence[]) => void;
  onClose: () => void;
  isOpen: boolean;
}

const AISequenceGenerator: React.FC<AISequenceGeneratorProps> = ({
  campaignId,
  onGenerate,
  onClose,
  isOpen,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [formData, setFormData] = useState({
    campaignGoal: '',
    targetAudience: '',
    productService: '',
    tone: 'professional',
    sequenceLength: '3',
    industry: '',
    callToAction: '',
    companyName: '',
    senderName: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateSequence = async () => {
    if (!formData.campaignGoal.trim() || !formData.targetAudience.trim()) {
      alert('Please fill in at least the campaign goal and target audience.');
      return;
    }

    setIsGenerating(true);

    try {
      const sequenceCount = parseInt(formData.sequenceLength);
      const sequences: EmailSequence[] = [];

      // Generate each email in the sequence
      for (let i = 0; i < sequenceCount; i++) {
        const emailType = i === 0 ? 'initial outreach' : 
                         i === 1 ? 'follow-up' : 
                         i === 2 ? 'final follow-up' : 
                         `follow-up ${i}`;

        const prompt = `Create a ${emailType} email for a ${formData.campaignGoal} campaign targeting ${formData.targetAudience}. 
        
        Context:
        - Product/Service: ${formData.productService}
        - Industry: ${formData.industry}
        - Company: ${formData.companyName}
        - Sender: ${formData.senderName}
        - Call to Action: ${formData.callToAction}
        - Email ${i + 1} of ${sequenceCount} in the sequence
        
        Make it ${formData.tone} in tone and include personalization placeholders like {{firstName}}, {{company}}, etc.`;

        const response = await apiService.generateEmailContent({
          prompt,
          context: `Email sequence step ${i + 1}`,
          tone: formData.tone as any,
          length: 'medium',
          include_subject: true,
        });

        const sequence: EmailSequence = {
          id: Date.now() + i, // Temporary ID
          campaign_id: campaignId,
          name: `${emailType.charAt(0).toUpperCase() + emailType.slice(1)} Email`,
          subject_line: response.subject || `${emailType} - ${formData.campaignGoal}`,
          email_content: response.content || '',
          order: i,
          delay_days: i === 0 ? 0 : i * 2, // 0, 2, 4, 6 days delay
          delay_hours: 0,
          status: 'draft',
          emails_sent: 0,
          emails_delivered: 0,
          emails_opened: 0,
          emails_clicked: 0,
          emails_replied: 0,
          emails_bounced: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        sequences.push(sequence);
      }

      onGenerate(sequences);
      onClose();
    } catch (error: any) {
      console.error('AI sequence generation error:', error);
      alert(error.message || 'Failed to generate sequence. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">🤖 Generate Email Sequence with AI</h3>
              <p className="text-gray-600 text-sm mt-1">
                Let AI create a complete email sequence for your campaign
              </p>
            </div>
            <Button variant="outline" onClick={onClose} disabled={isGenerating}>
              ✕
            </Button>
          </div>

          <div className="space-y-4">
            {/* Campaign Goal */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Campaign Goal *
              </label>
              <textarea
                value={formData.campaignGoal}
                onChange={(e) => handleInputChange('campaignGoal', e.target.value)}
                placeholder="e.g., Generate leads for our new SaaS product, Book demo calls, Promote our webinar..."
                rows={2}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-sm text-gray-500">What do you want to achieve with this email sequence?</p>
            </div>

            {/* Target Audience */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Target Audience *
              </label>
              <textarea
                value={formData.targetAudience}
                onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                placeholder="e.g., Marketing managers at B2B companies, Startup founders, E-commerce business owners..."
                rows={2}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-sm text-gray-500">Who are you targeting with this campaign?</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product/Service */}
              <Input
                label="Product/Service"
                value={formData.productService}
                onChange={(e) => handleInputChange('productService', e.target.value)}
                placeholder="e.g., Email marketing platform"
              />

              {/* Industry */}
              <Input
                label="Industry"
                value={formData.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                placeholder="e.g., SaaS, E-commerce, Consulting"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Company Name */}
              <Input
                label="Company Name"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                placeholder="Your company name"
              />

              {/* Sender Name */}
              <Input
                label="Sender Name"
                value={formData.senderName}
                onChange={(e) => handleInputChange('senderName', e.target.value)}
                placeholder="Your name"
              />
            </div>

            {/* Call to Action */}
            <Input
              label="Call to Action"
              value={formData.callToAction}
              onChange={(e) => handleInputChange('callToAction', e.target.value)}
              placeholder="e.g., Book a demo, Download our guide, Schedule a call"
              helperText="What action do you want recipients to take?"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Tone */}
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Tone
                </label>
                <select
                  value={formData.tone}
                  onChange={(e) => handleInputChange('tone', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="professional">Professional</option>
                  <option value="friendly">Friendly</option>
                  <option value="casual">Casual</option>
                  <option value="formal">Formal</option>
                  <option value="persuasive">Persuasive</option>
                </select>
              </div>

              {/* Sequence Length */}
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Number of Emails
                </label>
                <select
                  value={formData.sequenceLength}
                  onChange={(e) => handleInputChange('sequenceLength', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="2">2 Emails</option>
                  <option value="3">3 Emails</option>
                  <option value="4">4 Emails</option>
                  <option value="5">5 Emails</option>
                </select>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
            <Button variant="outline" onClick={onClose} disabled={isGenerating}>
              Cancel
            </Button>
            <Button
              onClick={generateSequence}
              isLoading={isGenerating}
              disabled={!formData.campaignGoal.trim() || !formData.targetAudience.trim()}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
              }}
            >
              {isGenerating ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </span>
              ) : (
                <span className="flex items-center">
                  🤖 Generate Sequence
                </span>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AISequenceGenerator;
