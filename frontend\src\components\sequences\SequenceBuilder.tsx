import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Content } from '../ui';
import SequenceStep from './SequenceStep';
import AISequenceGenerator from './AISequenceGenerator';
import type { EmailSequence } from '../../types';

interface SequenceBuilderProps {
  campaignId: number;
  sequences: EmailSequence[];
  onSave: (sequences: EmailSequence[]) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const SequenceBuilder: React.FC<SequenceBuilderProps> = ({
  campaignId,
  sequences: initialSequences,
  onSave,
  onCancel,
  isLoading = false,
}) => {
  const [sequences, setSequences] = useState<EmailSequence[]>(initialSequences);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [showAIGenerator, setShowAIGenerator] = useState(false);

  const addSequence = () => {
    const newSequence: EmailSequence = {
      id: Date.now(), // Will be replaced by backend
      campaign_id: campaignId,
      name: `Email ${sequences.length + 1}`,
      subject_line: '',
      email_content: '',
      order: sequences.length,
      delay_days: sequences.length === 0 ? 0 : 1,
      delay_hours: 0,
      status: 'draft',
      emails_sent: 0,
      emails_delivered: 0,
      emails_opened: 0,
      emails_clicked: 0,
      emails_replied: 0,
      emails_bounced: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setSequences([...sequences, newSequence]);
  };

  const updateSequence = (index: number, updates: Partial<EmailSequence>) => {
    setSequences(prev => prev.map((seq, i) => 
      i === index ? { ...seq, ...updates, updated_at: new Date().toISOString() } : seq
    ));
  };

  const removeSequence = (index: number) => {
    setSequences(prev => {
      const newSequences = prev.filter((_, i) => i !== index);
      // Update order for remaining sequences
      return newSequences.map((seq, i) => ({ ...seq, order: i }));
    });
  };

  const duplicateSequence = (index: number) => {
    const sequenceToDuplicate = sequences[index];
    const duplicatedSequence: EmailSequence = {
      ...sequenceToDuplicate,
      id: `temp-${Date.now()}`,
      name: `${sequenceToDuplicate.name} (Copy)`,
      order: sequences.length,
    };
    setSequences([...sequences, duplicatedSequence]);
  };

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newSequences = [...sequences];
    const draggedSequence = newSequences[draggedIndex];
    
    // Remove dragged item
    newSequences.splice(draggedIndex, 1);
    // Insert at new position
    newSequences.splice(dropIndex, 0, draggedSequence);
    
    // Update order for all sequences
    const reorderedSequences = newSequences.map((seq, index) => ({
      ...seq,
      order: index,
    }));
    
    setSequences(reorderedSequences);
    setDraggedIndex(null);
  };

  const handleAIGenerate = (generatedSequences: EmailSequence[]) => {
    // Replace current sequences with AI-generated ones
    setSequences(generatedSequences);
  };

  const handleSave = async () => {
    // Validate sequences before saving
    const errors: string[] = [];
    sequences.forEach((seq, index) => {
      if (!seq.subject_line.trim()) {
        errors.push(`Email ${index + 1}: Subject line is required`);
      }
      if (!seq.email_content.trim()) {
        errors.push(`Email ${index + 1}: Content is required`);
      }
    });

    if (errors.length > 0) {
      alert('Please fix the following errors:\n\n' + errors.join('\n'));
      return;
    }

    await onSave(sequences);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Email Sequence Builder</h2>
              <p className="text-gray-600">Create a series of emails to send to your prospects</p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowAIGenerator(true)}
                disabled={isLoading}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '1rem'
                }}
              >
                🤖 Generate with AI
              </Button>
              <Button
                onClick={handleSave}
                isLoading={isLoading}
                style={{
                  background: 'linear-gradient(to right, #0284c7, #a855f7)',
                  borderRadius: '1rem'
                }}
              >
                Save Sequence
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Sequence Steps */}
          <div className="space-y-4">
            {sequences.map((sequence, index) => (
              <div
                key={sequence.id}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                className="relative"
              >
                <SequenceStep
                  sequence={sequence}
                  index={index}
                  isFirst={index === 0}
                  isLast={index === sequences.length - 1}
                  onUpdate={(updates) => updateSequence(index, updates)}
                  onRemove={() => removeSequence(index)}
                  onDuplicate={() => duplicateSequence(index)}
                  isDragging={draggedIndex === index}
                />
                
                {/* Connection Line */}
                {index < sequences.length - 1 && (
                  <div className="flex justify-center py-2">
                    <div className="w-px h-8 bg-gray-300 relative">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gray-400 rounded-full"></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Add New Step Button */}
          <div className="flex justify-center pt-6">
            <Button
              variant="outline"
              onClick={addSequence}
              className="border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50 text-gray-600 hover:text-blue-600"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Email Step
            </Button>
          </div>

          {/* Sequence Tips */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">💡 Sequence Tips</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• The first email is sent immediately when the campaign starts</li>
              <li>• Subsequent emails are sent after the specified delay</li>
              <li>• Use personalization variables like {'{'}firstName{'}'}, {'{'}company{'}'}</li>
              <li>• Keep emails concise and focused on one main message</li>
              <li>• Test your sequence with a small group first</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SequenceBuilder;
