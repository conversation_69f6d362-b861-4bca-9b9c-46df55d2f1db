import React, { useState } from 'react';
import { Button, Input, Card, CardContent, RichTextEditor } from '../ui';
import type { EmailSequence } from '../../types';

interface SequenceStepProps {
  sequence: EmailSequence;
  index: number;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updates: Partial<EmailSequence>) => void;
  onRemove: () => void;
  onDuplicate: () => void;
  isDragging?: boolean;
}

const SequenceStep: React.FC<SequenceStepProps> = ({
  sequence,
  index,
  isFirst,
  onUpdate,
  onRemove,
  onDuplicate,
  isDragging = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ subject_line: e.target.value });
  };

  const handleContentChange = (content: string) => {
    onUpdate({ email_content: content });
  };

  const handleDelayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const days = parseInt(e.target.value) || 0;
    onUpdate({ delay_days: Math.max(0, days) });
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ name: e.target.value });
  };

  const getDelayText = () => {
    if (isFirst) return 'Sent immediately';
    if (sequence.delay_days === 0) return 'Sent immediately';
    if (sequence.delay_days === 1) return 'Sent 1 day later';
    return `Sent ${sequence.delay_days} days later`;
  };

  const renderPreview = () => {
    const processedSubject = (sequence.subject_line || '')
      .replace(/\{\{firstName\}\}/g, 'John')
      .replace(/\{\{lastName\}\}/g, 'Doe')
      .replace(/\{\{company\}\}/g, 'Acme Corp');

    const processedContent = (sequence.email_content || '')
      .replace(/\{\{firstName\}\}/g, 'John')
      .replace(/\{\{lastName\}\}/g, 'Doe')
      .replace(/\{\{company\}\}/g, 'Acme Corp');

    return (
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
        <div className="mb-3 pb-3 border-b border-gray-200">
          <div className="text-sm text-gray-600 mb-1">Subject:</div>
          <div className="font-medium">{processedSubject || 'No subject'}</div>
        </div>
        <div className="text-sm text-gray-600 mb-1">Content:</div>
        <div className="whitespace-pre-wrap text-sm">
          {processedContent || 'No content'}
        </div>
      </div>
    );
  };

  return (
    <Card 
      className={`transition-all duration-200 ${
        isDragging ? 'opacity-50 scale-95' : 'hover:shadow-md'
      }`}
    >
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {/* Drag Handle */}
            <div className="cursor-move text-gray-400 hover:text-gray-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
              </svg>
            </div>
            
            {/* Step Number */}
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold text-white"
              style={{ background: 'linear-gradient(to right, #0284c7, #a855f7)' }}
            >
              {index + 1}
            </div>
            
            {/* Step Info */}
            <div>
              <div className="font-medium text-gray-900">{sequence.name}</div>
              <div className="text-sm text-gray-500">{getDelayText()}</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Preview
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
            >
              <svg 
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onDuplicate}
              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </Button>

            {!isFirst && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRemove}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </Button>
            )}
          </div>
        </div>

        {/* Preview */}
        {showPreview && (
          <div className="mb-4">
            {renderPreview()}
          </div>
        )}

        {/* Expanded Form */}
        {isExpanded && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Step Name"
                value={sequence.name}
                onChange={handleNameChange}
                placeholder="e.g., Introduction Email"
              />
              
              {!isFirst && (
                <Input
                  label="Delay (Days)"
                  type="number"
                  min="0"
                  value={sequence.delay_days.toString()}
                  onChange={handleDelayChange}
                  helperText="Days to wait before sending this email"
                />
              )}
            </div>

            <Input
              label="Subject Line"
              value={sequence.subject_line || ''}
              onChange={handleSubjectChange}
              placeholder="e.g., Quick question about {{company}}"
              helperText="Use {{firstName}}, {{lastName}}, {{company}} for personalization"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Content
              </label>
              <RichTextEditor
                content={sequence.email_content || ''}
                onChange={handleContentChange}
                placeholder="Hi {{firstName}},

I noticed that {{company}} is doing great work in [industry]. I'd love to share how we've helped similar companies...

Best regards,
[Your Name]"
                className="w-full"
                showVariables={true}
              />
            </div>
          </div>
        )}

        {/* Collapsed Summary */}
        {!isExpanded && (
          <div className="text-sm text-gray-600">
            <div className="mb-1">
              <span className="font-medium">Subject:</span> {sequence.subject_line || 'No subject set'}
            </div>
            <div>
              <span className="font-medium">Content:</span> {
                sequence.email_content
                  ? `${sequence.email_content.substring(0, 100)}${sequence.email_content.length > 100 ? '...' : ''}`
                  : 'No content set'
              }
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SequenceStep;
