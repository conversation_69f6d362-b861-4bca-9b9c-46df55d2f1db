import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Card, CardHeader, CardContent } from '../ui';
import type { EmailTemplate } from '../../types';

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name must be less than 100 characters'),
  subject: z.string().min(1, 'Subject line is required'),
  content: z.string().min(1, 'Email content is required'),
  category: z.string().optional(),
});

type TemplateFormData = z.infer<typeof templateSchema>;

interface TemplateEditorProps {
  template?: EmailTemplate;
  onSave: (data: TemplateFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  onSave,
  onCancel,
  isLoading = false,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: template?.name || '',
      subject: template?.subject || '',
      content: template?.content || '',
      category: template?.category || '',
    },
  });

  const watchedContent = watch('content');
  const watchedSubject = watch('subject');

  const handleFormSubmit = async (data: TemplateFormData) => {
    try {
      setError(null);
      await onSave(data);
    } catch (err: any) {
      setError(err.message || 'Failed to save template');
    }
  };

  const insertVariable = (variable: string) => {
    const contentTextarea = document.getElementById('content') as HTMLTextAreaElement;
    if (contentTextarea) {
      const start = contentTextarea.selectionStart;
      const end = contentTextarea.selectionEnd;
      const currentValue = contentTextarea.value;
      const newValue = currentValue.substring(0, start) + variable + currentValue.substring(end);
      
      // Update the form value
      contentTextarea.value = newValue;
      contentTextarea.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        contentTextarea.focus();
        contentTextarea.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  const renderPreview = () => {
    const processedSubject = watchedSubject
      .replace(/\{\{firstName\}\}/g, 'John')
      .replace(/\{\{lastName\}\}/g, 'Doe')
      .replace(/\{\{company\}\}/g, 'Acme Corp')
      .replace(/\{\{position\}\}/g, 'Marketing Manager');
    
    const processedContent = watchedContent
      .replace(/\{\{firstName\}\}/g, 'John')
      .replace(/\{\{lastName\}\}/g, 'Doe')
      .replace(/\{\{company\}\}/g, 'Acme Corp')
      .replace(/\{\{position\}\}/g, 'Marketing Manager');

    return (
      <div className="border border-gray-200 rounded-lg p-6 bg-white">
        <div className="mb-4 pb-4 border-b border-gray-200">
          <div className="text-sm text-gray-600 mb-2">Subject:</div>
          <div className="font-semibold text-lg">{processedSubject || 'No subject'}</div>
        </div>
        <div className="text-sm text-gray-600 mb-2">Content:</div>
        <div className="whitespace-pre-wrap text-gray-900 leading-relaxed">
          {processedContent || 'No content'}
        </div>
      </div>
    );
  };

  const variables = [
    { label: 'First Name', value: '{{firstName}}' },
    { label: 'Last Name', value: '{{lastName}}' },
    { label: 'Company', value: '{{company}}' },
    { label: 'Position', value: '{{position}}' },
    { label: 'Email', value: '{{email}}' },
    { label: 'Phone', value: '{{phone}}' },
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {template ? 'Edit Template' : 'Create Email Template'}
              </h2>
              <p className="text-gray-600">
                {template ? 'Update your email template' : 'Create a reusable email template'}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPreview(!showPreview)}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Form */}
            <div className="lg:col-span-2">
              <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
                {error && (
                  <div 
                    className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-xl flex items-center"
                    style={{
                      background: 'linear-gradient(to right, #fef2f2, #fee2e2)',
                      borderColor: '#fca5a5'
                    }}
                  >
                    <svg className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {error}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Template Name"
                    placeholder="e.g., Welcome Email"
                    error={errors.name?.message}
                    {...register('name')}
                  />

                  <Input
                    label="Category (Optional)"
                    placeholder="e.g., Welcome, Follow-up"
                    error={errors.category?.message}
                    {...register('category')}
                  />
                </div>

                <Input
                  label="Subject Line"
                  placeholder="e.g., Welcome to {{company}}, {{firstName}}!"
                  error={errors.subject?.message}
                  {...register('subject')}
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Content
                  </label>
                  <textarea
                    id="content"
                    className="block w-full rounded-xl border border-gray-200 px-4 py-3 text-sm placeholder-gray-400 shadow-sm transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 hover:border-gray-300"
                    rows={12}
                    placeholder={`Hi {{firstName}},

Welcome to our platform! We're excited to have {{company}} as part of our community.

Here's what you can expect:
• Personalized onboarding
• 24/7 customer support
• Access to premium features

If you have any questions, feel free to reach out.

Best regards,
[Your Name]`}
                    {...register('content')}
                  />
                  {errors.content && (
                    <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    disabled={isLoading}
                    style={{
                      background: 'linear-gradient(to right, #0284c7, #a855f7)',
                      borderRadius: '1rem'
                    }}
                  >
                    {isLoading ? 'Saving...' : template ? 'Update Template' : 'Create Template'}
                  </Button>
                </div>
              </form>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Variables */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Variables</h3>
                  <p className="text-sm text-gray-600">Click to insert into your template</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-2">
                    {variables.map((variable) => (
                      <button
                        key={variable.value}
                        type="button"
                        onClick={() => insertVariable(variable.value)}
                        className="text-left px-3 py-2 text-sm bg-gray-50 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-colors"
                      >
                        <div className="font-medium">{variable.label}</div>
                        <div className="text-xs text-gray-500">{variable.value}</div>
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Preview */}
              {showPreview && (
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold text-gray-900">Preview</h3>
                    <p className="text-sm text-gray-600">How your email will look</p>
                  </CardHeader>
                  <CardContent>
                    {renderPreview()}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TemplateEditor;
