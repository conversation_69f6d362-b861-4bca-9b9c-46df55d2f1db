import React from 'react';
import Button from './Button';

interface BulkAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger';
  onClick: () => void;
  disabled?: boolean;
}

interface BulkActionsToolbarProps {
  selectedCount: number;
  totalCount: number;
  actions: BulkAction[];
  onClearSelection: () => void;
  className?: string;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedCount,
  totalCount,
  actions,
  onClearSelection,
  className = '',
}) => {
  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-xl p-4 mb-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <span className="text-sm font-medium text-blue-900">
              {selectedCount} of {totalCount} selected
            </span>
          </div>
          
          <button
            onClick={onClearSelection}
            className="text-sm text-blue-700 hover:text-blue-800 underline"
          >
            Clear selection
          </button>
        </div>

        <div className="flex items-center space-x-2">
          {actions.map((action) => (
            <Button
              key={action.id}
              onClick={action.onClick}
              disabled={action.disabled}
              variant={action.variant === 'danger' ? 'outline' : 'outline'}
              size="sm"
              className={`
                ${action.variant === 'danger' 
                  ? 'text-red-600 border-red-200 hover:bg-red-50' 
                  : action.variant === 'primary'
                  ? 'text-blue-600 border-blue-200 hover:bg-blue-50'
                  : 'text-gray-600 border-gray-200 hover:bg-gray-50'
                }
              `}
            >
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BulkActionsToolbar;
