import React from 'react';
import { cn } from '../../utils';

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  indeterminate?: boolean;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  id?: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  indeterminate = false,
  disabled = false,
  className = '',
  size = 'md',
  label,
  id,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.checked);
  };

  const checkboxElement = (
    <div className="relative">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={handleChange}
        disabled={disabled}
        className="sr-only"
      />
      <div
        className={cn(
          'border-2 rounded transition-all duration-200 flex items-center justify-center cursor-pointer',
          sizeClasses[size],
          {
            'border-blue-600 bg-blue-600': checked || indeterminate,
            'border-gray-300 bg-white hover:border-gray-400': !checked && !indeterminate,
            'opacity-50 cursor-not-allowed': disabled,
          },
          className
        )}
        onClick={() => !disabled && onChange(!checked)}
      >
        {(checked || indeterminate) && (
          <svg
            className={cn('text-white', iconSizeClasses[size])}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {indeterminate ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M20 12H4" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            )}
          </svg>
        )}
      </div>
    </div>
  );

  if (label) {
    return (
      <label
        htmlFor={id}
        className={cn(
          'flex items-center space-x-2 cursor-pointer',
          { 'cursor-not-allowed opacity-50': disabled }
        )}
      >
        {checkboxElement}
        <span className="text-sm text-gray-700 select-none">{label}</span>
      </label>
    );
  }

  return checkboxElement;
};

export default Checkbox;
