import React, { useState } from 'react';
import { usePerformanceMetrics, useMemoryMonitor } from '../../hooks';
import { <PERSON>, CardContent, CardHeader, Button } from './';

interface PerformanceDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  isOpen,
  onClose,
}) => {
  const { metrics, slowComponents, getAverageRenderTime, clearMetrics } = usePerformanceMetrics();
  const memoryInfo = useMemoryMonitor();
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  if (!isOpen) return null;

  const componentNames = Array.from(new Set(metrics.map(m => m.componentName)));
  const recentMetrics = metrics.slice(-20); // Show last 20 metrics

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)}ms`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Performance Dashboard</h3>
            <p className="text-gray-600 dark:text-gray-400 mt-1">Monitor application performance and identify bottlenecks</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={clearMetrics}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Clear Metrics
            </Button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Memory Usage */}
            {memoryInfo && (
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Memory Usage</h4>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Used JS Heap:</span>
                      <span className="font-mono text-sm">{formatBytes(memoryInfo.usedJSHeapSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Total JS Heap:</span>
                      <span className="font-mono text-sm">{formatBytes(memoryInfo.totalJSHeapSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">JS Heap Limit:</span>
                      <span className="font-mono text-sm">{formatBytes(memoryInfo.jsHeapSizeLimit)}</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${(memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Component Performance */}
            <Card>
              <CardHeader>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Component Performance</h4>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {componentNames.slice(0, 5).map(name => (
                    <div key={name} className="flex justify-between items-center">
                      <button
                        onClick={() => setSelectedComponent(name)}
                        className="text-left text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                      >
                        {name}
                      </button>
                      <span className="font-mono text-sm text-gray-600 dark:text-gray-400">
                        {formatTime(getAverageRenderTime(name))}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Slow Components */}
            <Card>
              <CardHeader>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Slow Renders (&gt;16ms)</h4>
              </CardHeader>
              <CardContent>
                {slowComponents.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    No slow renders detected! 🎉
                  </p>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {slowComponents.slice(-10).map((metric, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700 dark:text-gray-300">{metric.componentName}</span>
                        <span className="font-mono text-red-600 dark:text-red-400">
                          {formatTime(metric.renderTime)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Metrics */}
            <Card>
              <CardHeader>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Recent Activity</h4>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {recentMetrics.reverse().map((metric, index) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="text-gray-700 dark:text-gray-300">{metric.componentName}</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-gray-600 dark:text-gray-400">
                          {formatTime(metric.renderTime)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-500">
                          {new Date(metric.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Component View */}
          {selectedComponent && (
            <Card className="mt-6">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {selectedComponent} Details
                  </h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedComponent(null)}
                  >
                    Close
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {formatTime(getAverageRenderTime(selectedComponent))}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Average Render Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {metrics.filter(m => m.componentName === selectedComponent).length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Renders</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                      {slowComponents.filter(m => m.componentName === selectedComponent).length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Slow Renders</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {metrics.length} total measurements • {slowComponents.length} slow renders
          </p>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Performance monitoring active</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
