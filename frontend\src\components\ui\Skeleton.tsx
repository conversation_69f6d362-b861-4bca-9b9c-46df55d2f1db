import React from 'react';
import { cn } from '../../utils';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({
  className,
  width,
  height,
  rounded = 'md',
  animate = true,
}) => {
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full',
  };

  return (
    <div
      className={cn(
        'bg-gray-200',
        roundedClasses[rounded],
        animate && 'animate-pulse',
        className
      )}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

// Specific skeleton components for common use cases
export const SkeletonText: React.FC<{
  lines?: number;
  className?: string;
}> = ({ lines = 1, className }) => (
  <div className={cn('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        height="1rem"
        width={i === lines - 1 ? '75%' : '100%'}
        className="last:w-3/4"
      />
    ))}
  </div>
);

export const SkeletonCard: React.FC<{
  className?: string;
  showAvatar?: boolean;
}> = ({ className, showAvatar = false }) => (
  <div className={cn('p-6 border border-gray-200 rounded-xl bg-white', className)}>
    <div className="flex items-start space-x-4">
      {showAvatar && (
        <Skeleton width={48} height={48} rounded="full" />
      )}
      <div className="flex-1 space-y-3">
        <Skeleton height="1.25rem" width="60%" />
        <SkeletonText lines={2} />
        <div className="flex space-x-2">
          <Skeleton height="2rem" width="5rem" rounded="lg" />
          <Skeleton height="2rem" width="5rem" rounded="lg" />
        </div>
      </div>
    </div>
  </div>
);

export const SkeletonTable: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className }) => (
  <div className={cn('space-y-3', className)}>
    {/* Header */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, i) => (
        <Skeleton key={`header-${i}`} height="1rem" width="80%" />
      ))}
    </div>
    
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div
        key={`row-${rowIndex}`}
        className="grid gap-4 py-3 border-b border-gray-100"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton
            key={`cell-${rowIndex}-${colIndex}`}
            height="1rem"
            width={colIndex === 0 ? '90%' : '70%'}
          />
        ))}
      </div>
    ))}
  </div>
);

export const SkeletonChart: React.FC<{
  className?: string;
  type?: 'line' | 'bar' | 'pie';
}> = ({ className, type = 'line' }) => (
  <div className={cn('p-6 border border-gray-200 rounded-xl bg-white', className)}>
    <div className="space-y-4">
      {/* Chart Title */}
      <Skeleton height="1.5rem" width="40%" />
      
      {/* Chart Area */}
      <div className="relative h-64 bg-gray-50 rounded-lg overflow-hidden">
        {type === 'line' && (
          <div className="absolute inset-0 flex items-end justify-between px-4 pb-4">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="flex flex-col items-center space-y-2">
                <Skeleton
                  height={Math.random() * 120 + 40}
                  width="0.25rem"
                  className="bg-gray-300"
                />
                <Skeleton height="0.75rem" width="2rem" />
              </div>
            ))}
          </div>
        )}
        
        {type === 'bar' && (
          <div className="absolute inset-0 flex items-end justify-between px-4 pb-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton
                key={i}
                height={Math.random() * 120 + 40}
                width="3rem"
                className="bg-gray-300"
              />
            ))}
          </div>
        )}
        
        {type === 'pie' && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Skeleton width={120} height={120} rounded="full" className="bg-gray-300" />
          </div>
        )}
      </div>
      
      {/* Legend */}
      <div className="flex space-x-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-2">
            <Skeleton width="0.75rem" height="0.75rem" rounded="sm" />
            <Skeleton height="0.875rem" width="4rem" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const SkeletonStats: React.FC<{
  count?: number;
  className?: string;
}> = ({ count = 4, className }) => (
  <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6', className)}>
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="p-6 border border-gray-200 rounded-xl bg-white">
        <div className="flex items-center">
          <Skeleton width={48} height={48} rounded="xl" className="bg-gray-300" />
          <div className="ml-4 flex-1">
            <Skeleton height="0.875rem" width="60%" className="mb-2" />
            <Skeleton height="1.5rem" width="40%" />
            <Skeleton height="0.75rem" width="80%" className="mt-1" />
          </div>
        </div>
      </div>
    ))}
  </div>
);

export const SkeletonList: React.FC<{
  items?: number;
  className?: string;
  showAvatar?: boolean;
}> = ({ items = 5, className, showAvatar = true }) => (
  <div className={cn('space-y-4', className)}>
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-xl">
        {showAvatar && (
          <Skeleton width={40} height={40} rounded="full" />
        )}
        <div className="flex-1 space-y-2">
          <Skeleton height="1rem" width="30%" />
          <Skeleton height="0.875rem" width="60%" />
        </div>
        <div className="flex space-x-2">
          <Skeleton width={24} height={24} rounded="sm" />
          <Skeleton width={24} height={24} rounded="sm" />
        </div>
      </div>
    ))}
  </div>
);

export default Skeleton;
