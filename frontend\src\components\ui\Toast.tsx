import toast, { Toaster, type ToastOptions } from 'react-hot-toast';

// Custom toast styles that match our design system
const baseToastOptions: ToastOptions = {
  duration: 4000,
  style: {
    background: '#ffffff',
    color: '#374151',
    border: '1px solid #e5e7eb',
    borderRadius: '1rem',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    padding: '16px',
    fontSize: '14px',
    fontWeight: '500',
  },
};

const successOptions: ToastOptions = {
  ...baseToastOptions,
  iconTheme: {
    primary: '#10b981',
    secondary: '#ffffff',
  },
  style: {
    ...baseToastOptions.style,
    border: '1px solid #d1fae5',
    background: 'linear-gradient(to right, #f0fdf4, #ffffff)',
  },
};

const errorOptions: ToastOptions = {
  ...baseToastOptions,
  iconTheme: {
    primary: '#ef4444',
    secondary: '#ffffff',
  },
  style: {
    ...baseToastOptions.style,
    border: '1px solid #fecaca',
    background: 'linear-gradient(to right, #fef2f2, #ffffff)',
  },
};

const loadingOptions: ToastOptions = {
  ...baseToastOptions,
  iconTheme: {
    primary: '#3b82f6',
    secondary: '#ffffff',
  },
  style: {
    ...baseToastOptions.style,
    border: '1px solid #dbeafe',
    background: 'linear-gradient(to right, #eff6ff, #ffffff)',
  },
};

// Enhanced toast functions with our styling
export const showToast = {
  success: (message: string, options?: ToastOptions) => {
    return toast.success(message, { ...successOptions, ...options });
  },

  error: (message: string, options?: ToastOptions) => {
    return toast.error(message, { ...errorOptions, ...options });
  },

  loading: (message: string, options?: ToastOptions) => {
    return toast.loading(message, { ...loadingOptions, ...options });
  },
  
  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => {
    return toast.promise(promise, messages, { ...baseToastOptions, ...options });
  },
  
  custom: (message: string, options?: ToastOptions) => {
    return toast(message, { ...baseToastOptions, ...options });
  },
  
  dismiss: (toastId?: string) => {
    return toast.dismiss(toastId);
  },
  
  remove: (toastId?: string) => {
    return toast.remove(toastId);
  },
};

// Toast container component
export const ToastContainer: React.FC = () => {
  return (
    <Toaster
      position="top-right"
      reverseOrder={false}
      gutter={8}
      containerClassName=""
      containerStyle={{
        top: 20,
        right: 20,
      }}
      toastOptions={baseToastOptions}
    />
  );
};

// Action toast with custom buttons
export const showActionToast = (
  message: string,
  actions: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary';
  }>,
  options?: ToastOptions
) => {
  return toast.custom(
    (t) => (
      <div
        className={`${
          t.visible ? 'animate-enter' : 'animate-leave'
        } max-w-md w-full bg-white shadow-lg rounded-2xl pointer-events-auto flex ring-1 ring-black ring-opacity-5 border border-gray-200`}
      >
        <div className="flex-1 w-0 p-4">
          <div className="flex items-start">
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-900">{message}</p>
              <div className="mt-3 flex space-x-2">
                {actions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      action.action();
                      toast.dismiss(t.id);
                    }}
                    className={`text-sm font-medium rounded-lg px-3 py-1.5 transition-colors ${
                      action.style === 'primary'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="flex border-l border-gray-200">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="w-full border border-transparent rounded-none rounded-r-2xl p-4 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    ),
    { duration: 6000, ...options }
  );
};

export default showToast;
