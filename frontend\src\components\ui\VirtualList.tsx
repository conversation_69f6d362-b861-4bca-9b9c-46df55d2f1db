import React, { useState, useRef, useCallback } from 'react';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  // Scroll to specific item (for future use)
  // const scrollToItem = useCallback((index: number) => {
  //   if (scrollElementRef.current) {
  //     const scrollTop = index * itemHeight;
  //     scrollElementRef.current.scrollTop = scrollTop;
  //     setScrollTop(scrollTop);
  //   }
  // }, [itemHeight]);

  // Expose scroll methods (for future use)
  // const scrollMethods = {
  //   scrollToItem,
  //   scrollToTop: () => scrollToItem(0),
  //   scrollToBottom: () => scrollToItem(items.length - 1),
  // };

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${startIndex * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) => (
            <div
              key={startIndex + index}
              style={{ height: itemHeight }}
              className="flex items-center"
            >
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default VirtualList;

// Hook for virtual list with dynamic item heights
export const useVirtualList = <T,>(
  items: T[],
  estimatedItemHeight: number = 50,
  containerHeight: number = 400
) => {
  const [itemHeights, setItemHeights] = useState<number[]>([]);
  const [scrollTop, setScrollTop] = useState(0);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Measure item heights
  const measureItem = useCallback((index: number, element: HTMLDivElement | null) => {
    if (element) {
      const height = element.getBoundingClientRect().height;
      setItemHeights(prev => {
        const newHeights = [...prev];
        newHeights[index] = height;
        return newHeights;
      });
    }
    itemRefs.current[index] = element;
  }, []);

  // Calculate visible range with dynamic heights
  const getVisibleRange = useCallback(() => {
    let accumulatedHeight = 0;
    let startIndex = 0;
    let endIndex = 0;

    // Find start index
    for (let i = 0; i < items.length; i++) {
      const itemHeight = itemHeights[i] || estimatedItemHeight;
      if (accumulatedHeight + itemHeight > scrollTop) {
        startIndex = Math.max(0, i - 2); // Add some overscan
        break;
      }
      accumulatedHeight += itemHeight;
    }

    // Find end index
    accumulatedHeight = 0;
    for (let i = 0; i < items.length; i++) {
      const itemHeight = itemHeights[i] || estimatedItemHeight;
      accumulatedHeight += itemHeight;
      if (accumulatedHeight > scrollTop + containerHeight) {
        endIndex = Math.min(items.length - 1, i + 2); // Add some overscan
        break;
      }
    }

    return { startIndex, endIndex };
  }, [items.length, itemHeights, estimatedItemHeight, scrollTop, containerHeight]);

  // Calculate total height
  const totalHeight = items.reduce((total, _, index) => {
    return total + (itemHeights[index] || estimatedItemHeight);
  }, 0);

  // Calculate offset for visible items
  const getOffsetTop = useCallback((index: number) => {
    let offset = 0;
    for (let i = 0; i < index; i++) {
      offset += itemHeights[i] || estimatedItemHeight;
    }
    return offset;
  }, [itemHeights, estimatedItemHeight]);

  return {
    measureItem,
    getVisibleRange,
    totalHeight,
    getOffsetTop,
    scrollTop,
    setScrollTop,
  };
};
