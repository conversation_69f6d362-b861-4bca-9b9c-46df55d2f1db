import React, { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import { apiService } from '../services/api';
import { storage } from '../utils';
import type { User, LoginCredentials, RegisterCredentials } from '../types';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      const token = storage.get<string>('auth_token');
      const savedUser = storage.get<User>('user');

      if (token && savedUser) {
        setUser(savedUser);
        try {
          // Verify token is still valid by fetching current user
          const currentUser = await apiService.getCurrentUser();
          setUser(currentUser);
          storage.set('user', currentUser);
        } catch (error) {
          // Token is invalid, clear storage
          storage.remove('auth_token');
          storage.remove('user');
          setUser(null);
        }
      }
      
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      const response = await apiService.login(credentials);
      
      // Store token and user data
      storage.set('auth_token', response.access_token);
      storage.set('user', response.user);
      setUser(response.user);
    } catch (error) {
      // Clear any existing auth data on login failure
      storage.remove('auth_token');
      storage.remove('user');
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);
      const response = await apiService.register(credentials);

      // Registration successful - but don't auto-login
      // User should manually login after registration
      console.log('Registration successful:', response.user?.email);

      // Don't store token or set user - let them login manually
      // This ensures they go through the login flow
      return response; // Return response for success handling
    } catch (error) {
      // Clear any existing auth data on register failure
      storage.remove('auth_token');
      storage.remove('user');
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      // Even if logout fails on server, clear local data
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state
      storage.remove('auth_token');
      storage.remove('user');
      setUser(null);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await apiService.getCurrentUser();
      setUser(currentUser);
      storage.set('user', currentUser);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, user might need to re-authenticate
      await logout();
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
