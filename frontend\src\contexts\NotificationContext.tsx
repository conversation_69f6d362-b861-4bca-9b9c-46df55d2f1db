import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import type { Notification } from '../components/ui/NotificationCenter';

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  unreadCount: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Load notifications from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('notifications');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setNotifications(parsed.map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp),
        })));
      } catch (error) {
        console.error('Failed to load notifications from localStorage:', error);
      }
    }
  }, []);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(notifications));
  }, [notifications]);

  // Simulate real-time notifications (in a real app, this would be WebSocket/SSE)
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add notifications for demo purposes
      if (Math.random() < 0.1) { // 10% chance every 30 seconds
        const sampleNotifications = [
          {
            type: 'success' as const,
            title: 'Campaign Update',
            message: 'Your "Product Launch" campaign has new opens!',
          },
          {
            type: 'info' as const,
            title: 'New Reply',
            message: 'You received a reply to your outreach email.',
          },
          {
            type: 'warning' as const,
            title: 'Low Credits',
            message: 'Your email credits are running low. Consider upgrading.',
          },
          {
            type: 'success' as const,
            title: 'Sequence Complete',
            message: 'Email sequence "Follow-up Series" has completed.',
          },
        ];
        
        const randomNotification = sampleNotifications[Math.floor(Math.random() * sampleNotifications.length)];
        addNotification(randomNotification);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep only last 50 notifications
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  // Add some initial demo notifications
  useEffect(() => {
    if (notifications.length === 0) {
      const initialNotifications = [
        {
          type: 'success' as const,
          title: 'Welcome!',
          message: 'Your email outreach platform is ready to use.',
        },
        {
          type: 'info' as const,
          title: 'Getting Started',
          message: 'Create your first campaign to start reaching out to prospects.',
          actionUrl: '/campaigns',
          actionText: 'Create Campaign',
        },
      ];

      initialNotifications.forEach(notification => {
        setTimeout(() => addNotification(notification), 1000);
      });
    }
  }, []);

  const value: NotificationContextType = {
    notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    unreadCount,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook for campaign-specific notifications
export const useCampaignNotifications = () => {
  const { addNotification } = useNotifications();

  const notifyCampaignCreated = (campaignName: string) => {
    addNotification({
      type: 'success',
      title: 'Campaign Created',
      message: `Campaign "${campaignName}" has been created successfully.`,
      actionUrl: '/campaigns',
      actionText: 'View Campaigns',
    });
  };

  const notifyCampaignStarted = (campaignName: string) => {
    addNotification({
      type: 'info',
      title: 'Campaign Started',
      message: `Campaign "${campaignName}" is now active and sending emails.`,
    });
  };

  const notifyCampaignCompleted = (campaignName: string) => {
    addNotification({
      type: 'success',
      title: 'Campaign Completed',
      message: `Campaign "${campaignName}" has finished sending all emails.`,
    });
  };

  const notifyEmailOpened = (campaignName: string, count: number) => {
    addNotification({
      type: 'success',
      title: 'New Email Opens',
      message: `${count} new opens for campaign "${campaignName}".`,
    });
  };

  const notifyEmailReplied = (campaignName: string) => {
    addNotification({
      type: 'success',
      title: 'New Reply',
      message: `You received a reply from campaign "${campaignName}".`,
    });
  };

  const notifyEmailBounced = (campaignName: string, count: number) => {
    addNotification({
      type: 'warning',
      title: 'Email Bounced',
      message: `${count} emails bounced in campaign "${campaignName}".`,
    });
  };

  return {
    notifyCampaignCreated,
    notifyCampaignStarted,
    notifyCampaignCompleted,
    notifyEmailOpened,
    notifyEmailReplied,
    notifyEmailBounced,
  };
};

export default NotificationProvider;
