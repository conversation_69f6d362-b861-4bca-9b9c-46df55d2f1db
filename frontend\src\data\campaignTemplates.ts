import type { Campaign } from '../types';

export interface TemplateSequence {
  id: string;
  step: number;
  subject: string;
  content: string;
  delay_days: number;
  is_active: boolean;
}

export interface CampaignTemplate {
  id: string;
  name: string;
  description: string;
  category: 'cold-outreach' | 'follow-up' | 'nurture' | 'sales' | 'networking';
  tags: string[];
  preview: string;
  campaign: Omit<Campaign, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'contacts_count' | 'sent_count' | 'opened_count' | 'replied_count' | 'bounced_count' | 'sequences'> & {
    sequences: TemplateSequence[];
  };
}

export const campaignTemplates: CampaignTemplate[] = [
  {
    id: 'cold-outreach-basic',
    name: 'Basic Cold Outreach',
    description: 'A simple 3-step cold outreach sequence for initial contact',
    category: 'cold-outreach',
    tags: ['beginner', 'simple', 'effective'],
    preview: 'Hi {{firstName}}, I noticed {{company}} is doing great work in...',
    campaign: {
      name: 'Cold Outreach Campaign',
      description: 'Basic cold outreach to potential prospects',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: 'Quick question about {{company}}',
          content: `Hi {{firstName}},

I noticed that {{company}} is doing great work in the {{industry}} space. I'd love to share how we've helped similar companies increase their revenue by 30-50%.

Would you be open to a quick 15-minute call this week to discuss how we might be able to help {{company}} achieve similar results?

Best regards,
[Your Name]`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'Following up on {{company}}',
          content: `Hi {{firstName}},

I wanted to follow up on my previous email about helping {{company}} increase revenue.

I understand you're probably busy, but I thought you might be interested in seeing how [Similar Company] increased their revenue by 45% using our approach.

Would a brief call make sense?

Best,
[Your Name]`,
          delay_days: 3,
          is_active: true,
        },
        {
          id: '3',
          step: 3,
          subject: 'Last follow-up for {{company}}',
          content: `Hi {{firstName}},

This will be my last follow-up. I understand priorities change and timing isn't always right.

If you'd ever like to explore how we can help {{company}} grow, feel free to reach out.

Best of luck with everything!

[Your Name]`,
          delay_days: 7,
          is_active: true,
        },
      ],
    },
  },
  {
    id: 'warm-introduction',
    name: 'Warm Introduction',
    description: 'Perfect for reaching out to warm leads or referrals',
    category: 'networking',
    tags: ['warm-leads', 'referrals', 'networking'],
    preview: 'Hi {{firstName}}, {{referrerName}} suggested I reach out...',
    campaign: {
      name: 'Warm Introduction Campaign',
      description: 'Outreach to warm leads and referrals',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: '{{referrerName}} suggested I reach out',
          content: `Hi {{firstName}},

{{referrerName}} suggested I reach out to you. They mentioned that {{company}} might be interested in [your solution/service].

I'd love to learn more about your current challenges and see if there's a way we can help.

Would you be available for a brief call this week?

Best regards,
[Your Name]

P.S. {{referrerName}} sends their regards!`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'Quick follow-up from {{referrerName}}\'s introduction',
          content: `Hi {{firstName}},

I wanted to follow up on {{referrerName}}'s introduction. 

I know how valuable your time is, so I'll keep this brief. We've helped companies like {{company}} [specific benefit/result].

Would a 10-minute call work for you this week?

Best,
[Your Name]`,
          delay_days: 4,
          is_active: true,
        },
      ],
    },
  },
  {
    id: 'product-launch',
    name: 'Product Launch Announcement',
    description: 'Announce new products or features to your audience',
    category: 'sales',
    tags: ['product-launch', 'announcement', 'sales'],
    preview: 'Exciting news! We just launched something amazing...',
    campaign: {
      name: 'Product Launch Campaign',
      description: 'Announce new product launch to prospects and customers',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: 'Exciting news for {{company}}!',
          content: `Hi {{firstName}},

I have some exciting news to share with you!

We just launched [Product Name] - a solution specifically designed to help companies like {{company}} [solve specific problem/achieve specific goal].

Early users are already seeing [specific results/benefits].

Would you like to be among the first to see how this could benefit {{company}}?

Best regards,
[Your Name]

P.S. We're offering early access to select companies this month.`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'Don\'t miss out - {{company}} early access',
          content: `Hi {{firstName}},

I wanted to make sure you didn't miss the opportunity to get early access to [Product Name].

We're only offering this to a select group of companies, and I thought {{company}} would be a perfect fit.

The early access period ends soon. Would you like to schedule a quick demo?

Best,
[Your Name]`,
          delay_days: 5,
          is_active: true,
        },
      ],
    },
  },
  {
    id: 'event-invitation',
    name: 'Event Invitation',
    description: 'Invite prospects to webinars, conferences, or networking events',
    category: 'nurture',
    tags: ['events', 'webinar', 'networking'],
    preview: 'You\'re invited to our exclusive event...',
    campaign: {
      name: 'Event Invitation Campaign',
      description: 'Invite prospects to upcoming events',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: 'Exclusive invitation for {{company}}',
          content: `Hi {{firstName}},

You're invited to our exclusive [Event Name] on [Date].

We'll be covering [key topics] that are directly relevant to companies like {{company}}.

The event is limited to [number] attendees, and we'd love to have {{company}} represented.

Can I reserve a spot for you?

Best regards,
[Your Name]

[Event Details]
Date: [Date]
Time: [Time]
Location: [Location/Virtual]`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'Last chance to join us - {{company}}',
          content: `Hi {{firstName}},

Just a quick reminder about our [Event Name] happening on [Date].

We have a few spots left, and I'd hate for {{company}} to miss out on this opportunity to [key benefit].

Can I secure your spot today?

Best,
[Your Name]`,
          delay_days: 3,
          is_active: true,
        },
      ],
    },
  },
  {
    id: 'customer-success-story',
    name: 'Customer Success Story',
    description: 'Share success stories and case studies with prospects',
    category: 'sales',
    tags: ['case-study', 'social-proof', 'results'],
    preview: 'I thought you\'d be interested in how [Customer] achieved...',
    campaign: {
      name: 'Success Story Campaign',
      description: 'Share customer success stories with prospects',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: 'How [Customer Company] achieved [specific result]',
          content: `Hi {{firstName}},

I thought you'd be interested in how [Customer Company], a company similar to {{company}}, achieved [specific result] in just [timeframe].

They were facing [similar challenge] and were able to [specific outcome] using our solution.

I'd love to share the full case study with you and discuss how {{company}} might achieve similar results.

Would you be interested in a brief call this week?

Best regards,
[Your Name]`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'The [Customer Company] case study for {{company}}',
          content: `Hi {{firstName}},

I wanted to follow up and share the full [Customer Company] case study I mentioned.

The results they achieved are quite impressive:
• [Specific metric 1]
• [Specific metric 2]  
• [Specific metric 3]

I believe {{company}} could see similar results. Would you like to discuss how?

Best,
[Your Name]

[Attach case study or include link]`,
          delay_days: 4,
          is_active: true,
        },
      ],
    },
  },
  {
    id: 'reengagement',
    name: 'Re-engagement Campaign',
    description: 'Re-engage cold prospects or dormant leads',
    category: 'follow-up',
    tags: ['re-engagement', 'dormant-leads', 'second-chance'],
    preview: 'It\'s been a while since we last connected...',
    campaign: {
      name: 'Re-engagement Campaign',
      description: 'Re-engage with dormant prospects and leads',
      status: 'draft',
      sequences: [
        {
          id: '1',
          step: 1,
          subject: 'It\'s been a while, {{firstName}}',
          content: `Hi {{firstName}},

It's been a while since we last connected about {{company}}'s [relevant topic/challenge].

A lot has changed since then, and I wanted to reach out to see how things are going.

We've helped several companies in the {{industry}} space achieve [relevant results] recently, and I thought you might be interested in hearing about it.

Would you be open to a quick catch-up call?

Best regards,
[Your Name]`,
          delay_days: 0,
          is_active: true,
        },
        {
          id: '2',
          step: 2,
          subject: 'New developments that might interest {{company}}',
          content: `Hi {{firstName}},

I hope you're doing well!

Since we last spoke, we've made some significant improvements to our solution that directly address the challenges companies like {{company}} face.

I'd love to share these updates with you and see if they might be relevant to {{company}}'s current priorities.

Would a brief call make sense?

Best,
[Your Name]`,
          delay_days: 5,
          is_active: true,
        },
      ],
    },
  },
];

export const getTemplatesByCategory = (category: CampaignTemplate['category']) => {
  return campaignTemplates.filter(template => template.category === category);
};

export const getTemplateById = (id: string) => {
  return campaignTemplates.find(template => template.id === id);
};

export const getAllCategories = () => {
  return Array.from(new Set(campaignTemplates.map(template => template.category)));
};
