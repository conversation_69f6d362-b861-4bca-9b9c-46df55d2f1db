import React, { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
  props?: any;
}

interface PerformanceConfig {
  enabled?: boolean;
  threshold?: number; // Log only if render time exceeds this (ms)
  logToConsole?: boolean;
  onMetric?: (metric: PerformanceMetrics) => void;
}

// Global performance store
class PerformanceStore {
  private metrics: PerformanceMetrics[] = [];
  private listeners: ((metrics: PerformanceMetrics[]) => void)[] = [];

  addMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
    this.notifyListeners();
  }

  getMetrics() {
    return [...this.metrics];
  }

  subscribe(listener: (metrics: PerformanceMetrics[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.metrics));
  }

  clear() {
    this.metrics = [];
    this.notifyListeners();
  }

  getSlowComponents(threshold: number = 16) {
    return this.metrics.filter(m => m.renderTime > threshold);
  }

  getAverageRenderTime(componentName?: string) {
    const relevantMetrics = componentName 
      ? this.metrics.filter(m => m.componentName === componentName)
      : this.metrics;
    
    if (relevantMetrics.length === 0) return 0;
    
    const total = relevantMetrics.reduce((sum, m) => sum + m.renderTime, 0);
    return total / relevantMetrics.length;
  }
}

const performanceStore = new PerformanceStore();

// Hook for monitoring component render performance
export const usePerformanceMonitor = (
  componentName: string,
  config: PerformanceConfig = {}
) => {
  const {
    enabled = import.meta.env.DEV,
    threshold = 0,
    logToConsole = import.meta.env.DEV,
    onMetric,
  } = config;

  const renderStartTime = useRef<number>(0);
  const propsRef = useRef<any>(null);

  // Start timing
  const startTiming = useCallback((props?: any) => {
    if (!enabled) return;
    renderStartTime.current = performance.now();
    propsRef.current = props;
  }, [enabled]);

  // End timing and record metric
  const endTiming = useCallback(() => {
    if (!enabled || renderStartTime.current === 0) return;
    
    const renderTime = performance.now() - renderStartTime.current;
    
    if (renderTime >= threshold) {
      const metric: PerformanceMetrics = {
        renderTime,
        componentName,
        timestamp: Date.now(),
        props: propsRef.current,
      };

      performanceStore.addMetric(metric);
      
      if (logToConsole) {
        console.log(`🔍 ${componentName} rendered in ${renderTime.toFixed(2)}ms`, metric);
      }
      
      onMetric?.(metric);
    }
    
    renderStartTime.current = 0;
  }, [enabled, threshold, componentName, logToConsole, onMetric]);

  // Auto-start timing on component mount/update
  useEffect(() => {
    startTiming();
    return endTiming;
  });

  return { startTiming, endTiming };
};

// Hook for accessing performance metrics
export const usePerformanceMetrics = () => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics[]>([]);

  useEffect(() => {
    setMetrics(performanceStore.getMetrics());
    return performanceStore.subscribe(setMetrics);
  }, []);

  return {
    metrics,
    slowComponents: performanceStore.getSlowComponents(),
    getAverageRenderTime: performanceStore.getAverageRenderTime,
    clearMetrics: performanceStore.clear,
  };
};

// HOC for automatic performance monitoring
export const withPerformanceMonitor = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const WrappedComponent = (props: P) => {
    const name = componentName || Component.displayName || Component.name || 'Unknown';
    const { startTiming, endTiming } = usePerformanceMonitor(name);

    useEffect(() => {
      startTiming(props);
      return endTiming;
    }, [props, startTiming, endTiming]);

    return React.createElement(Component, props as any);
  };

  WrappedComponent.displayName = `withPerformanceMonitor(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
};

// Web Vitals monitoring
export const useWebVitals = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Monitor Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('🎯 LCP:', entry.startTime);
        }
        if (entry.entryType === 'first-input') {
          console.log('⚡ FID:', (entry as any).processingStart - entry.startTime);
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
    } catch (e) {
      // Browser doesn't support these metrics
    }

    // Monitor Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      console.log('📐 CLS:', clsValue);
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      // Browser doesn't support layout-shift
    }

    return () => {
      observer.disconnect();
      clsObserver.disconnect();
    };
  }, []);
};

// Memory usage monitoring
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = React.useState<any>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory);
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

export default performanceStore;
