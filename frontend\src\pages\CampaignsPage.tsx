import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, showToast, KeyboardShortcutsHelp, AdvancedFilters, type FilterState } from '../components/ui';
import { CampaignForm, CampaignList, TemplateSelector } from '../components/campaigns';
import { useKeyboardShortcuts, createCommonShortcuts } from '../hooks';
import { useCampaignNotifications } from '../contexts/NotificationContext';
import { apiService } from '../services/api';
import type { CampaignTemplate } from '../data/campaignTemplates';
import type { Campaign } from '../types';

const CampaignsPage: React.FC = () => {
  const navigate = useNavigate();
  const { notifyCampaignCreated } = useCampaignNotifications();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<CampaignTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [showHelp, setShowHelp] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterState>({
    search: '',
    status: [],
    tags: [],
    dateRange: { start: '', end: '' },
    sortBy: 'created_at',
    sortOrder: 'desc',
  });
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Load campaigns from API
  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getCampaigns(1, 100); // Load first 100 campaigns
      setCampaigns(response.data);
    } catch (error) {
      console.error('Failed to load campaigns:', error);
      showToast.error('Failed to load campaigns. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Keyboard shortcuts
  const shortcuts = createCommonShortcuts({
    onSearch: () => {
      searchInputRef.current?.focus();
    },
    onNew: () => {
      if (!showForm) {
        setShowForm(true);
        setEditingCampaign(null);
      }
    },
    onRefresh: () => {
      loadCampaigns();
    },
    onHelp: () => {
      setShowHelp(true);
    },
    onEscape: () => {
      if (showForm) {
        setShowForm(false);
        setEditingCampaign(null);
      } else if (showHelp) {
        setShowHelp(false);
      }
    },
  });

  useKeyboardShortcuts({
    shortcuts,
    enabled: !showForm, // Disable shortcuts when form is open
  });

  const handleCreateCampaign = async (data: any) => {
    const toastId = showToast.loading('Creating campaign...');

    try {
      const newCampaign = await apiService.createCampaign(data);
      setCampaigns(prev => [newCampaign, ...prev]);
      setShowForm(false);

      showToast.dismiss(toastId);
      showToast.success(`Campaign "${data.name}" created successfully!`);
      notifyCampaignCreated(data.name);
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to create campaign. Please try again.');
    }
  };

  const handleEditCampaign = async (data: any) => {
    if (!editingCampaign) return;

    const toastId = showToast.loading('Updating campaign...');

    try {
      const updatedCampaign = await apiService.updateCampaign(editingCampaign.id.toString(), data);
      setCampaigns(prev => prev.map(campaign =>
        campaign.id === editingCampaign.id
          ? updatedCampaign
          : campaign
      ));
      setEditingCampaign(null);
      setShowForm(false);

      showToast.dismiss(toastId);
      showToast.success(`Campaign "${data.name}" updated successfully!`);
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to update campaign. Please try again.');
    }
  };

  const handleDeleteCampaign = async (campaignId: string) => {
    const campaign = campaigns.find(c => c.id.toString() === campaignId);
    const toastId = showToast.loading('Deleting campaign...');

    try {
      await apiService.deleteCampaign(campaignId);
      setCampaigns(prev => prev.filter(campaign => campaign.id.toString() !== campaignId));

      showToast.dismiss(toastId);
      showToast.success(`Campaign "${campaign?.name}" deleted successfully!`);
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to delete campaign. Please try again.');
    }
  };

  const handleDuplicateCampaign = (campaign: Campaign) => {
    const duplicatedCampaign: Campaign = {
      ...campaign,
      id: Date.now().toString(),
      name: `${campaign.name} (Copy)`,
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      contacts_count: 0,
      sent_count: 0,
      opened_count: 0,
      replied_count: 0,
      bounced_count: 0,
    };
    setCampaigns(prev => [duplicatedCampaign, ...prev]);
    showToast.success(`Campaign "${campaign.name}" duplicated successfully!`);
  };

  const handleBulkDelete = async (campaignIds: string[]) => {
    const toastId = showToast.loading(`Deleting ${campaignIds.length} campaigns...`);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCampaigns(prev => prev.filter(campaign => !campaignIds.includes(campaign.id)));

      showToast.dismiss(toastId);
      showToast.success(`${campaignIds.length} campaigns deleted successfully!`);
    } catch (error) {
      showToast.dismiss(toastId);
      showToast.error('Failed to delete campaigns. Please try again.');
    }
  };

  const handleBulkDuplicate = (campaignsToClone: Campaign[]) => {
    const duplicatedCampaigns = campaignsToClone.map(campaign => ({
      ...campaign,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      name: `${campaign.name} (Copy)`,
      status: 'draft' as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      contacts_count: 0,
      sent_count: 0,
      opened_count: 0,
      replied_count: 0,
      bounced_count: 0,
    }));

    setCampaigns(prev => [...duplicatedCampaigns, ...prev]);
    showToast.success(`${campaignsToClone.length} campaigns duplicated successfully!`);
  };

  const handleBulkStatusChange = async (campaignIds: string[], status: string) => {
    const toastId = showToast.loading(`Updating ${campaignIds.length} campaigns...`);

    try {
      // Update each campaign status via API
      const updatePromises = campaignIds.map(id =>
        apiService.updateCampaignStatus(id, status)
      );

      const updatedCampaigns = await Promise.all(updatePromises);

      // Update local state with the API response data
      setCampaigns(prev => prev.map(campaign => {
        const updatedCampaign = updatedCampaigns.find(updated => updated.id === campaign.id);
        return updatedCampaign || campaign;
      }));

      showToast.dismiss(toastId);
      showToast.success(`${campaignIds.length} campaigns updated to ${status}!`);
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to update campaigns. Please try again.');
      console.error('Bulk status change error:', error);
    }
  };

  const handleViewAnalytics = (campaign: Campaign) => {
    // Navigate to analytics page
    console.log('View analytics for campaign:', campaign.id);
  };

  const handleEditSequences = (campaign: Campaign) => {
    navigate(`/campaigns/${campaign.id}/sequences`);
  };

  const handleApplyFilters = (filters: FilterState) => {
    setActiveFilters(filters);
    setSearchTerm(filters.search); // Sync with basic search
  };

  const handleResetFilters = () => {
    setActiveFilters({
      search: '',
      status: [],
      tags: [],
      dateRange: { start: '', end: '' },
      sortBy: 'created_at',
      sortOrder: 'desc',
    });
    setSearchTerm('');
    setStatusFilter('all');
  };

  const handleSelectTemplate = (template: CampaignTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateSelector(false);
    setShowForm(true);
    setEditingCampaign(null);
  };

  const handleCreateFromTemplate = () => {
    setShowTemplateSelector(true);
  };

  const handleCreateFromScratch = () => {
    setSelectedTemplate(null);
    setEditingCampaign(null);
    setShowForm(true);
  };

  // Advanced filtering logic
  const filteredCampaigns = campaigns.filter(campaign => {
    // Basic search
    const searchQuery = activeFilters.search || searchTerm;
    const matchesSearch = !searchQuery ||
      campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (campaign.description?.toLowerCase().includes(searchQuery.toLowerCase()) || false);

    // Status filter
    const matchesStatus = (statusFilter === 'all' || campaign.status === statusFilter) &&
      (activeFilters.status.length === 0 || activeFilters.status.includes(campaign.status));

    // Date range filter
    const matchesDateRange = !activeFilters.dateRange.start || !activeFilters.dateRange.end ||
      (new Date(campaign.created_at) >= new Date(activeFilters.dateRange.start) &&
       new Date(campaign.created_at) <= new Date(activeFilters.dateRange.end));

    return matchesSearch && matchesStatus && matchesDateRange;
  }).sort((a, b) => {
    // Apply sorting
    const { sortBy, sortOrder } = activeFilters;
    let aValue: any = a[sortBy as keyof Campaign];
    let bValue: any = b[sortBy as keyof Campaign];

    // Handle date sorting
    if (sortBy === 'created_at' || sortBy === 'updated_at') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // Handle string sorting
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  if (showForm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {editingCampaign ? 'Edit Campaign' : 'Create Campaign'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {editingCampaign ? 'Update your campaign settings' : 'Set up a new email outreach campaign'}
            </p>
          </div>
        </div>

        <CampaignForm
          campaign={editingCampaign || undefined}
          template={selectedTemplate || undefined}
          onSubmit={editingCampaign ? handleEditCampaign : handleCreateCampaign}
          onCancel={() => {
            setShowForm(false);
            setEditingCampaign(null);
            setSelectedTemplate(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Email Campaigns</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your email outreach campaigns</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowHelp(true)}
            className="text-gray-600 hover:text-gray-700"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Help (?)
          </Button>

          {/* Create Campaign Dropdown */}
          <div className="relative group">
            <Button
              onClick={handleCreateFromTemplate}
              style={{
                background: 'linear-gradient(to right, #0284c7, #a855f7)',
                borderRadius: '1rem'
              }}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Campaign (Ctrl+N)
            </Button>

            {/* Quick Actions */}
            <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
              <div className="py-2">
                <button
                  onClick={handleCreateFromTemplate}
                  className="w-full flex items-center px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg className="w-4 h-4 mr-3 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Use Template
                  <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">Recommended</span>
                </button>
                <button
                  onClick={handleCreateFromScratch}
                  className="w-full flex items-center px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg className="w-4 h-4 mr-3 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Start from Scratch
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search campaigns... (Ctrl+K)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div className="flex space-x-3">
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <Button
            variant="outline"
            onClick={() => setShowAdvancedFilters(true)}
            className="text-gray-600 hover:text-gray-700"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Advanced Filters
            {(activeFilters.status.length > 0 || activeFilters.tags.length > 0 || activeFilters.dateRange.start) && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Active
              </span>
            )}
          </Button>
        </div>
      </div>

      {/* Campaign List */}
      <CampaignList
        campaigns={filteredCampaigns}
        onEdit={(campaign) => {
          setEditingCampaign(campaign);
          setShowForm(true);
        }}
        onDelete={handleDeleteCampaign}
        onDuplicate={handleDuplicateCampaign}
        onViewAnalytics={handleViewAnalytics}
        onEditSequences={handleEditSequences}
        onBulkDelete={handleBulkDelete}
        onBulkDuplicate={handleBulkDuplicate}
        onBulkStatusChange={handleBulkStatusChange}
        isLoading={isLoading}
        enableBulkActions={true}
      />

      {/* Keyboard Shortcuts Help Modal */}
      <KeyboardShortcutsHelp
        shortcuts={shortcuts}
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />

      {/* Advanced Filters Modal */}
      <AdvancedFilters
        isOpen={showAdvancedFilters}
        onClose={() => setShowAdvancedFilters(false)}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
        initialFilters={activeFilters}
      />

      {/* Template Selector Modal */}
      <TemplateSelector
        isOpen={showTemplateSelector}
        onClose={() => setShowTemplateSelector(false)}
        onSelectTemplate={handleSelectTemplate}
      />
    </div>
  );
};

export default CampaignsPage;
