import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Contact } from '../types';
import { apiService } from '../services/api';
import { ContactDetailView } from '../components/contacts/ContactDetailView';
import { ContactEditForm } from '../components/contacts/ContactEditForm';
import { toast } from 'react-hot-toast';

export const ContactDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [contact, setContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (id) {
      loadContact();
    }
  }, [id]);

  const loadContact = async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      const response = await apiService.getContact(id);
      setContact(response.contact);
    } catch (error) {
      console.error('Error loading contact:', error);
      toast.error('Failed to load contact details');
      navigate('/contacts');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSave = async (formData: any) => {
    if (!contact || !id) return;

    try {
      setIsSaving(true);
      
      // Prepare the update data
      const updateData = {
        ...formData,
        // Ensure we maintain the contact ID
        id: contact.id
      };

      console.log('Saving contact with data:', updateData);
      
      const updatedContact = await apiService.updateContact(id, updateData);
      
      setContact(updatedContact);
      setIsEditing(false);
      toast.success('Contact updated successfully!');
      
    } catch (error) {
      console.error('Error updating contact:', error);
      toast.error('Failed to update contact');
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    navigate('/contacts');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading contact details...</p>
        </div>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Contact Not Found</h1>
          <p className="text-gray-600 mb-6">The contact you're looking for doesn't exist.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Back to Contacts
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {isEditing ? (
          <ContactEditForm
            contact={contact}
            onSubmit={handleSave}
            onCancel={handleCancelEdit}
            isLoading={isSaving}
          />
        ) : (
          <ContactDetailView
            contact={contact}
            onEdit={handleEdit}
            onBack={handleBack}
            isLoading={false}
          />
        )}
      </div>
    </div>
  );
};

export default ContactDetailPage;
