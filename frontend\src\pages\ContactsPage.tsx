import React, { useState, useEffect } from 'react';

import { Button, showToast } from '../components/ui';
import { ContactForm, ContactList, ContactImport } from '../components/contacts';
import { apiService } from '../services/api';
import type { Contact } from '../types';

const ContactsPage: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Load contacts from API
  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getContacts(1, 100); // Load first 100 contacts
      setContacts(response.data);
    } catch (error) {
      console.error('Failed to load contacts:', error);
      showToast.error('Failed to load contacts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateContact = async (data: any) => {
    const toastId = showToast.loading('Creating contact...');

    try {
      const newContact = await apiService.createContact(data);
      setContacts(prev => [newContact, ...prev]);
      setShowForm(false);

      showToast.dismiss(toastId);
      showToast.success('Contact created successfully!');
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to create contact. Please try again.');
    }
  };

  const handleEditContact = async (data: any) => {
    if (!editingContact) return;

    const toastId = showToast.loading('Updating contact...');

    try {
      const updatedContact = await apiService.updateContact(editingContact.id.toString(), data);
      setContacts(prev => prev.map(contact =>
        contact.id === editingContact.id
          ? updatedContact
          : contact
      ));
      setEditingContact(null);
      setShowForm(false);

      showToast.dismiss(toastId);
      showToast.success('Contact updated successfully!');
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to update contact. Please try again.');
    }
  };

  const handleDeleteContact = async (contactId: string) => {
    const toastId = showToast.loading('Deleting contact...');

    try {
      await apiService.deleteContact(contactId);
      setContacts(prev => prev.filter(contact => contact.id.toString() !== contactId));
      setSelectedContacts(prev => prev.filter(id => id !== contactId));

      showToast.dismiss(toastId);
      showToast.success('Contact deleted successfully!');
    } catch (error: any) {
      showToast.dismiss(toastId);
      showToast.error(error.message || 'Failed to delete contact. Please try again.');
    }
  };

  const handleImportContacts = async (file: File) => {
    try {
      setIsLoading(true);

      // Call the real API to import contacts
      const result = await apiService.importContacts(file);

      // Refresh the contacts list
      await loadContacts();

      setShowImport(false);

      // Show success message with details
      const message = `Successfully imported ${result.imported} contacts!` +
        (result.skipped > 0 ? `\n${result.skipped} contacts were skipped (duplicates).` : '') +
        (result.errors.length > 0 ? `\n${result.errors.length} errors occurred.` : '');

      alert(message);

      // Log errors if any
      if (result.errors.length > 0) {
        console.warn('Import errors:', result.errors);
      }

    } catch (error: any) {
      console.error('Import failed:', error);
      alert(`Import failed: ${error.message || 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch =
      (contact.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
      (contact.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (contact.company?.toLowerCase().includes(searchTerm.toLowerCase()) || false);
    
    const matchesStatus = statusFilter === 'all' || contact.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (showForm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {editingContact ? 'Edit Contact' : 'Add Contact'}
            </h1>
            <p className="text-gray-600">
              {editingContact ? 'Update contact information' : 'Add a new contact to your database'}
            </p>
          </div>
        </div>

        <ContactForm
          contact={editingContact || undefined}
          onSubmit={editingContact ? handleEditContact : handleCreateContact}
          onCancel={() => {
            setShowForm(false);
            setEditingContact(null);
          }}
        />
      </div>
    );
  }

  if (showImport) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Import Contacts</h1>
            <p className="text-gray-600">Upload a CSV file to import multiple contacts</p>
          </div>
        </div>

        <ContactImport
          onImport={handleImportContacts}
          onCancel={() => setShowImport(false)}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
          <p className="text-gray-600">Manage your email contacts and lists</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowImport(true)}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Import CSV
          </Button>
          <Button
            onClick={() => setShowForm(true)}
            style={{
              background: 'linear-gradient(to right, #0284c7, #a855f7)',
              borderRadius: '1rem'
            }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Contact
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-600">Total Contacts</p>
              <p className="text-2xl font-bold text-blue-900">{contacts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-600">Active</p>
              <p className="text-2xl font-bold text-green-900">
                {contacts.filter(c => c.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-600">Unsubscribed</p>
              <p className="text-2xl font-bold text-red-900">
                {contacts.filter(c => c.status === 'unsubscribed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-600">Bounced</p>
              <p className="text-2xl font-bold text-yellow-900">
                {contacts.filter(c => c.status === 'bounced').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div className="sm:w-48">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="unsubscribed">Unsubscribed</option>
            <option value="bounced">Bounced</option>
          </select>
        </div>
      </div>

      {/* Contact List */}
      <ContactList
        contacts={filteredContacts}
        selectedContacts={selectedContacts}
        onSelect={setSelectedContacts}
        onEdit={(contact) => {
          setEditingContact(contact);
          setShowForm(true);
        }}
        onDelete={handleDeleteContact}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ContactsPage;
