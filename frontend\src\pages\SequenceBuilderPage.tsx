import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { SequenceBuilder } from '../components/sequences';
import type { EmailSequence, Campaign } from '../types';

const SequenceBuilderPage: React.FC = () => {
  const { campaignId } = useParams<{ campaignId: string }>();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [sequences, setSequences] = useState<EmailSequence[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (!campaignId) {
      navigate('/campaigns');
      return;
    }

    // Mock data for development
    setTimeout(() => {
      // Mock campaign data
      setCampaign({
        id: campaignId,
        name: 'Product Launch Outreach',
        description: 'Reaching out to potential customers about our new product launch',
        status: 'draft',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        user_id: 'user1',
        sequences: [],
        contacts_count: 0,
        sent_count: 0,
        opened_count: 0,
        replied_count: 0,
        bounced_count: 0,
      });

      // Mock sequences data - start with one default sequence
      setSequences([
        {
          id: '1',
          campaign_id: campaignId,
          name: 'Initial Outreach',
          subject: 'Quick question about {{company}}',
          content: `Hi {{firstName}},

I noticed that {{company}} is doing great work in the industry. I'd love to share how we've helped similar companies increase their revenue by 30% on average.

Would you be open to a quick 15-minute call this week to discuss?

Best regards,
[Your Name]`,
          delay_days: 0,
          order: 0,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
        },
      ]);

      setIsLoading(false);
    }, 1000);
  }, [campaignId, navigate]);

  const handleSave = async (updatedSequences: EmailSequence[]) => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSequences(updatedSequences);
      
      // Show success message
      alert('Email sequence saved successfully!');
      
      // Navigate back to campaigns
      navigate('/campaigns');
    } catch (error) {
      alert('Failed to save sequence. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (window.confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
      navigate('/campaigns');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading campaign...</p>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Campaign not found</h2>
          <p className="text-gray-600 mb-4">The campaign you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/campaigns')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <button
          onClick={() => navigate('/campaigns')}
          className="hover:text-blue-600 transition-colors"
        >
          Campaigns
        </button>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <span className="text-gray-900 font-medium">{campaign.name}</span>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <span className="text-gray-900">Email Sequence</span>
      </div>

      {/* Campaign Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-blue-900">{campaign.name}</h3>
            {campaign.description && (
              <p className="text-blue-800 text-sm mt-1">{campaign.description}</p>
            )}
            <p className="text-blue-700 text-xs mt-2">
              Building email sequence for this campaign
            </p>
          </div>
        </div>
      </div>

      {/* Sequence Builder */}
      <SequenceBuilder
        campaignId={campaign.id}
        sequences={sequences}
        onSave={handleSave}
        onCancel={handleCancel}
        isLoading={isSaving}
      />
    </div>
  );
};

export default SequenceBuilderPage;
