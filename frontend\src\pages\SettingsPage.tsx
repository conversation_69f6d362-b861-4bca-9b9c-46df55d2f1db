import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardHeader, CardContent, Input, showToast } from '../components/ui';
import PerformanceDashboard from '../components/ui/PerformanceDashboard';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(false);
  const [settings, setSettings] = useState({
    // General Settings
    companyName: 'EmailAI',
    companyEmail: '<EMAIL>',
    timezone: 'UTC',
    language: 'en',
    
    // SMTP Settings
    smtpHost: '',
    smtpPort: '587',
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    
    // AI Settings
    aiProvider: 'openai',
    openaiApiKey: '',
    anthropicApiKey: '',
    aiModel: 'gpt-4',
    maxTokens: 1000,
    temperature: 0.7,
    
    // Email Settings
    defaultFromName: 'EmailAI Team',
    defaultFromEmail: '<EMAIL>',
    trackOpens: true,
    trackClicks: true,
    unsubscribeLink: true,
    
    // Notification Settings
    emailNotifications: true,
    campaignUpdates: true,
    weeklyReports: true,
    securityAlerts: true,
  });

  const tabs = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'smtp', name: 'SMTP', icon: '📧' },
    { id: 'ai', name: 'AI Configuration', icon: '🤖' },
    { id: 'email', name: 'Email Settings', icon: '✉️' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'performance', name: 'Performance', icon: '📊' },
  ];

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      alert('Settings saved successfully!');
    } catch (error) {
      alert('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Company Name"
            value={settings.companyName}
            onChange={(e) => handleInputChange('companyName', e.target.value)}
            placeholder="Your Company Name"
          />
          <Input
            label="Company Email"
            type="email"
            value={settings.companyEmail}
            onChange={(e) => handleInputChange('companyEmail', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Localization</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
            <select
              value={settings.timezone}
              onChange={(e) => handleInputChange('timezone', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
              <option value="Europe/London">London</option>
              <option value="Europe/Paris">Paris</option>
              <option value="Asia/Tokyo">Tokyo</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select
              value={settings.language}
              onChange={(e) => handleInputChange('language', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSMTPSettings = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-blue-900">SMTP Configuration</h4>
            <p className="text-blue-800 text-sm mt-1">
              Configure your SMTP server to send emails. You can use services like Gmail, SendGrid, or Mailgun.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          label="SMTP Host"
          value={settings.smtpHost}
          onChange={(e) => handleInputChange('smtpHost', e.target.value)}
          placeholder="smtp.gmail.com"
        />
        <Input
          label="SMTP Port"
          value={settings.smtpPort}
          onChange={(e) => handleInputChange('smtpPort', e.target.value)}
          placeholder="587"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          label="Username"
          value={settings.smtpUsername}
          onChange={(e) => handleInputChange('smtpUsername', e.target.value)}
          placeholder="<EMAIL>"
        />
        <Input
          label="Password"
          type="password"
          value={settings.smtpPassword}
          onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
          placeholder="Your app password"
        />
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="smtpSecure"
          checked={settings.smtpSecure}
          onChange={(e) => handleInputChange('smtpSecure', e.target.checked)}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="smtpSecure" className="ml-2 text-sm text-gray-700">
          Use secure connection (TLS/SSL)
        </label>
      </div>

      <Button
        variant="outline"
        onClick={() => alert('Test email sent! Check your inbox.')}
        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
      >
        Send Test Email
      </Button>
    </div>
  );

  const renderAISettings = () => (
    <div className="space-y-6">
      <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-purple-900">AI Configuration</h4>
            <p className="text-purple-800 text-sm mt-1">
              Configure AI providers for email generation, optimization, and personalization features.
            </p>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">AI Provider</label>
        <select
          value={settings.aiProvider}
          onChange={(e) => handleInputChange('aiProvider', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        >
          <option value="openai">OpenAI</option>
          <option value="anthropic">Anthropic (Claude)</option>
          <option value="google">Google AI</option>
        </select>
      </div>

      {settings.aiProvider === 'openai' && (
        <Input
          label="OpenAI API Key"
          type="password"
          value={settings.openaiApiKey}
          onChange={(e) => handleInputChange('openaiApiKey', e.target.value)}
          placeholder="sk-..."
          helperText="Get your API key from https://platform.openai.com/api-keys"
        />
      )}

      {settings.aiProvider === 'anthropic' && (
        <Input
          label="Anthropic API Key"
          type="password"
          value={settings.anthropicApiKey}
          onChange={(e) => handleInputChange('anthropicApiKey', e.target.value)}
          placeholder="sk-ant-..."
          helperText="Get your API key from https://console.anthropic.com/"
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Model</label>
          <select
            value={settings.aiModel}
            onChange={(e) => handleInputChange('aiModel', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          >
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="claude-3-opus">Claude 3 Opus</option>
            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
          </select>
        </div>
        <Input
          label="Max Tokens"
          type="number"
          value={settings.maxTokens.toString()}
          onChange={(e) => handleInputChange('maxTokens', parseInt(e.target.value) || 1000)}
          placeholder="1000"
        />
        <Input
          label="Temperature"
          type="number"
          step="0.1"
          min="0"
          max="2"
          value={settings.temperature.toString()}
          onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value) || 0.7)}
          placeholder="0.7"
        />
      </div>
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Default Email Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Default From Name"
            value={settings.defaultFromName}
            onChange={(e) => handleInputChange('defaultFromName', e.target.value)}
            placeholder="Your Name"
          />
          <Input
            label="Default From Email"
            type="email"
            value={settings.defaultFromEmail}
            onChange={(e) => handleInputChange('defaultFromEmail', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tracking & Analytics</h3>
        <div className="space-y-3">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="trackOpens"
              checked={settings.trackOpens}
              onChange={(e) => handleInputChange('trackOpens', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="trackOpens" className="ml-2 text-sm text-gray-700">
              Track email opens
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="trackClicks"
              checked={settings.trackClicks}
              onChange={(e) => handleInputChange('trackClicks', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="trackClicks" className="ml-2 text-sm text-gray-700">
              Track link clicks
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="unsubscribeLink"
              checked={settings.unsubscribeLink}
              onChange={(e) => handleInputChange('unsubscribeLink', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="unsubscribeLink" className="ml-2 text-sm text-gray-700">
              Include unsubscribe link in emails
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Email Notifications</h3>
        <div className="space-y-3">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="emailNotifications"
              checked={settings.emailNotifications}
              onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="emailNotifications" className="ml-2 text-sm text-gray-700">
              Enable email notifications
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="campaignUpdates"
              checked={settings.campaignUpdates}
              onChange={(e) => handleInputChange('campaignUpdates', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="campaignUpdates" className="ml-2 text-sm text-gray-700">
              Campaign status updates
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="weeklyReports"
              checked={settings.weeklyReports}
              onChange={(e) => handleInputChange('weeklyReports', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="weeklyReports" className="ml-2 text-sm text-gray-700">
              Weekly performance reports
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="securityAlerts"
              checked={settings.securityAlerts}
              onChange={(e) => handleInputChange('securityAlerts', e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="securityAlerts" className="ml-2 text-sm text-gray-700">
              Security alerts
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPerformanceSettings = () => (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-green-900">Performance Monitoring</h4>
            <p className="text-green-800 text-sm mt-1">
              Monitor application performance, identify bottlenecks, and optimize user experience.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <Button
          onClick={() => setShowPerformanceDashboard(true)}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          Open Performance Dashboard
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 mb-2">Bundle Analysis</h5>
            <p className="text-sm text-gray-600 mb-3">
              Analyze bundle size and identify optimization opportunities.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => showToast.success('Bundle analysis will be available in production build')}
            >
              Analyze Bundle
            </Button>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 mb-2">Memory Usage</h5>
            <p className="text-sm text-gray-600 mb-3">
              Monitor memory consumption and detect memory leaks.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => showToast.success('Memory monitoring is active in development mode')}
            >
              View Memory Stats
            </Button>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h5 className="font-medium text-yellow-900">Development Mode</h5>
              <p className="text-sm text-yellow-800 mt-1">
                Performance monitoring is currently active in development mode.
                Some metrics may not be available in production builds.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'smtp':
        return renderSMTPSettings();
      case 'ai':
        return renderAISettings();
      case 'email':
        return renderEmailSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'performance':
        return renderPerformanceSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your account and application preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full text-left px-4 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-500'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <span className="mr-3">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-gray-900">
                {tabs.find(tab => tab.id === activeTab)?.name}
              </h2>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
              
              {/* Save Button */}
              <div className="flex justify-end pt-6 border-t border-gray-200 mt-8">
                <Button
                  onClick={handleSave}
                  isLoading={isLoading}
                  disabled={isLoading}
                  style={{
                    background: 'linear-gradient(to right, #0284c7, #a855f7)',
                    borderRadius: '1rem'
                  }}
                >
                  {isLoading ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Dashboard Modal */}
      <PerformanceDashboard
        isOpen={showPerformanceDashboard}
        onClose={() => setShowPerformanceDashboard(false)}
      />
    </div>
  );
};

export default SettingsPage;
