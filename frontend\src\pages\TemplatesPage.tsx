import React, { useState, useEffect } from 'react';
import { Button } from '../components/ui';
import { TemplateEditor, TemplateList } from '../components/templates';
import type { EmailTemplate } from '../types';

const TemplatesPage: React.FC = () => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditor, setShowEditor] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Mock data for development
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setTemplates([
        {
          id: '1',
          name: 'Welcome Email',
          subject: 'Welcome to {{company}}, {{firstName}}!',
          content: `Hi {{firstName}},

Welcome to our platform! We're excited to have {{company}} as part of our community.

Here's what you can expect:
• Personalized onboarding experience
• 24/7 customer support
• Access to premium features

If you have any questions, feel free to reach out to our team.

Best regards,
The Team`,
          category: 'Welcome',
          is_public: false,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          user_id: 'user1',
        },
        {
          id: '2',
          name: 'Follow-up Email',
          subject: 'Following up on our conversation',
          content: `Hi {{firstName}},

I wanted to follow up on our recent conversation about {{company}}'s needs.

Based on what we discussed, I believe our solution could help you:
• Increase efficiency by 40%
• Reduce costs significantly
• Improve customer satisfaction

Would you be available for a quick 15-minute call this week to discuss next steps?

Best regards,
{{senderName}}`,
          category: 'Follow-up',
          is_public: false,
          created_at: '2024-01-12T14:30:00Z',
          updated_at: '2024-01-12T14:30:00Z',
          user_id: 'user1',
        },
        {
          id: '3',
          name: 'Product Demo Invitation',
          subject: 'See how {{company}} can benefit from our solution',
          content: `Hello {{firstName}},

I hope this email finds you well. I wanted to reach out because I believe our solution could be a great fit for {{company}}.

We've helped companies similar to yours:
• Streamline their operations
• Increase revenue by an average of 25%
• Save 10+ hours per week on manual tasks

Would you be interested in a personalized demo to see how this could work for {{company}}?

I have availability this week for a 20-minute session. Let me know what works best for you.

Best regards,
{{senderName}}`,
          category: 'Demo',
          is_public: true,
          created_at: '2024-01-10T09:15:00Z',
          updated_at: '2024-01-10T09:15:00Z',
          user_id: 'user1',
        },
        {
          id: '4',
          name: 'Thank You Email',
          subject: 'Thank you for your time, {{firstName}}',
          content: `Hi {{firstName}},

Thank you for taking the time to speak with me today about {{company}}'s goals and challenges.

As discussed, I'll send over:
• The case study we mentioned
• Pricing information
• Next steps for implementation

I'll follow up early next week to answer any questions you might have.

Thanks again for your time!

Best regards,
{{senderName}}`,
          category: 'Follow-up',
          is_public: false,
          created_at: '2024-01-08T16:45:00Z',
          updated_at: '2024-01-08T16:45:00Z',
          user_id: 'user1',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleCreateTemplate = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newTemplate: EmailTemplate = {
      id: Date.now().toString(),
      name: data.name,
      subject: data.subject,
      content: data.content,
      category: data.category,
      is_public: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      user_id: 'user1',
    };

    setTemplates(prev => [newTemplate, ...prev]);
    setShowEditor(false);
  };

  const handleEditTemplate = async (data: any) => {
    if (!editingTemplate) return;
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setTemplates(prev => prev.map(template => 
      template.id === editingTemplate.id 
        ? { ...template, ...data, updated_at: new Date().toISOString() }
        : template
    ));
    setEditingTemplate(null);
    setShowEditor(false);
  };

  const handleDeleteTemplate = async (templateId: string) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    setTemplates(prev => prev.filter(template => template.id !== templateId));
  };

  const handleDuplicateTemplate = (template: EmailTemplate) => {
    const duplicatedTemplate: EmailTemplate = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (Copy)`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setTemplates(prev => [duplicatedTemplate, ...prev]);
  };

  const handleUseTemplate = (template: EmailTemplate) => {
    // Navigate to campaign creation with this template
    console.log('Use template:', template.id);
    alert(`Template "${template.name}" selected! This would typically navigate to campaign creation.`);
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const categories = Array.from(new Set(templates.map(t => t.category).filter(Boolean)));

  if (showEditor) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {editingTemplate ? 'Edit Template' : 'Create Template'}
            </h1>
            <p className="text-gray-600">
              {editingTemplate ? 'Update your email template' : 'Create a new reusable email template'}
            </p>
          </div>
        </div>

        <TemplateEditor
          template={editingTemplate || undefined}
          onSave={editingTemplate ? handleEditTemplate : handleCreateTemplate}
          onCancel={() => {
            setShowEditor(false);
            setEditingTemplate(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
          <p className="text-gray-600">Create and manage reusable email templates</p>
        </div>
        <Button
          onClick={() => setShowEditor(true)}
          style={{
            background: 'linear-gradient(to right, #0284c7, #a855f7)',
            borderRadius: '1rem'
          }}
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Template
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-600">Total Templates</p>
              <p className="text-2xl font-bold text-blue-900">{templates.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-600">Private</p>
              <p className="text-2xl font-bold text-green-900">
                {templates.filter(t => !t.is_public).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-purple-600">Public</p>
              <p className="text-2xl font-bold text-purple-900">
                {templates.filter(t => t.is_public).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div className="sm:w-48">
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Template List */}
      <TemplateList
        templates={filteredTemplates}
        onEdit={(template) => {
          setEditingTemplate(template);
          setShowEditor(true);
        }}
        onDelete={handleDeleteTemplate}
        onDuplicate={handleDuplicateTemplate}
        onUse={handleUseTemplate}
        isLoading={isLoading}
      />
    </div>
  );
};

export default TemplatesPage;
