import axios, { type AxiosInstance } from 'axios';
import { storage } from '../utils';
import type {
  User,
  AuthResponse,
  LoginCredentials,
  RegisterCredentials,
  Campaign,
  EmailSequence,
  Contact,
  ContactList,
  EmailTemplate,
  CampaignAnalytics,
  AnalyticsOverview,
  SMTPSettings,
  AISettings,
  GeneralSettings,
  EmailGenerationRequest,
  EmailGenerationResponse,
  SubjectOptimizationRequest,
  SubjectOptimizationResponse,
  AIStatus,
  PaginatedResponse,
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

    this.api = axios.create({
      baseURL: `${this.baseURL}/api/v1`,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config: any) => {
        const token = storage.get<string>('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error: any) => Promise.reject(error)
    );

    // Response interceptor to handle auth errors
    this.api.interceptors.response.use(
      (response: any) => response,
      (error: any) => {
        if (error.response?.status === 401) {
          storage.remove('auth_token');
          storage.remove('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);

    const response = await this.api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    return response.data;
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response = await this.api.post('/auth/register', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignore logout errors
    } finally {
      storage.remove('auth_token');
      storage.remove('user');
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // Campaign endpoints
  async getCampaigns(page: number = 1, perPage: number = 10): Promise<PaginatedResponse<Campaign>> {
    const response = await this.api.get('/campaigns/', {
      params: {
        skip: (page - 1) * perPage,
        limit: perPage
      },
    });

    // Transform backend response to match frontend pagination format
    return {
      data: response.data.campaigns,
      total: response.data.total,
      page: page,
      per_page: perPage,
      total_pages: Math.ceil(response.data.total / perPage),
    };
  }

  async getCampaign(id: string): Promise<Campaign> {
    const response = await this.api.get(`/campaigns/${id}`);
    return response.data;
  }

  async createCampaign(data: Partial<Campaign>): Promise<Campaign> {
    const response = await this.api.post('/campaigns/', data);
    return response.data;
  }

  async updateCampaign(id: string, data: Partial<Campaign>): Promise<Campaign> {
    const response = await this.api.put(`/campaigns/${id}`, data);
    return response.data;
  }

  async deleteCampaign(id: string): Promise<void> {
    await this.api.delete(`/campaigns/${id}`);
  }

  async startCampaign(id: string): Promise<Campaign> {
    // Use the update endpoint to change status to active
    const response = await this.api.put(`/campaigns/${id}`, { status: 'active' });
    return response.data;
  }

  async pauseCampaign(id: string): Promise<Campaign> {
    // Use the update endpoint to change status to paused
    const response = await this.api.put(`/campaigns/${id}`, { status: 'paused' });
    return response.data;
  }

  async updateCampaignStatus(id: string, status: string): Promise<Campaign> {
    const response = await this.api.put(`/campaigns/${id}`, { status });
    return response.data;
  }

  // Email Sequence endpoints
  async getSequences(campaignId?: string): Promise<EmailSequence[]> {
    const params = campaignId ? { campaign_id: campaignId } : {};
    const response = await this.api.get('/sequences/', { params });
    return response.data.sequences || response.data;
  }

  async createSequence(data: Partial<EmailSequence>): Promise<EmailSequence> {
    const response = await this.api.post('/sequences/', data);
    return response.data;
  }

  async updateSequence(sequenceId: string, data: Partial<EmailSequence>): Promise<EmailSequence> {
    const response = await this.api.put(`/sequences/${sequenceId}`, data);
    return response.data;
  }

  async deleteSequence(sequenceId: string): Promise<void> {
    await this.api.delete(`/sequences/${sequenceId}`);
  }

  // Contact endpoints
  async getContacts(page: number = 1, perPage: number = 10): Promise<PaginatedResponse<Contact>> {
    const response = await this.api.get('/contacts/', {
      params: {
        skip: (page - 1) * perPage,
        limit: perPage
      },
    });

    // Transform backend response to match frontend pagination format
    return {
      data: response.data.contacts,
      total: response.data.total,
      page: page,
      per_page: perPage,
      total_pages: Math.ceil(response.data.total / perPage),
    };
  }

  async getContact(id: string): Promise<Contact> {
    const response = await this.api.get(`/contacts/${id}`);
    return response.data;
  }

  async createContact(data: Partial<Contact>): Promise<Contact> {
    const response = await this.api.post('/contacts/', data);
    return response.data;
  }

  async updateContact(id: string, data: Partial<Contact>): Promise<Contact> {
    const response = await this.api.put(`/contacts/${id}`, data);
    return response.data;
  }

  async deleteContact(id: string): Promise<void> {
    await this.api.delete(`/contacts/${id}`);
  }

  async importContacts(file: File): Promise<{ imported: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    const response = await this.api.post('/contacts/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data.data;
  }

  // Contact List endpoints
  async getContactLists(): Promise<ContactList[]> {
    const response = await this.api.get('/contact-lists');
    return response.data.data;
  }

  async createContactList(data: Partial<ContactList>): Promise<ContactList> {
    const response = await this.api.post('/contact-lists', data);
    return response.data.data;
  }

  async updateContactList(id: string, data: Partial<ContactList>): Promise<ContactList> {
    const response = await this.api.put(`/contact-lists/${id}`, data);
    return response.data.data;
  }

  async deleteContactList(id: string): Promise<void> {
    await this.api.delete(`/contact-lists/${id}`);
  }

  // Email Template endpoints
  async getTemplates(): Promise<EmailTemplate[]> {
    const response = await this.api.get('/templates');
    return response.data.data;
  }

  async createTemplate(data: Partial<EmailTemplate>): Promise<EmailTemplate> {
    const response = await this.api.post('/templates', data);
    return response.data.data;
  }

  async updateTemplate(id: string, data: Partial<EmailTemplate>): Promise<EmailTemplate> {
    const response = await this.api.put(`/templates/${id}`, data);
    return response.data.data;
  }

  async deleteTemplate(id: string): Promise<void> {
    await this.api.delete(`/templates/${id}`);
  }

  // Analytics endpoints
  async getCampaignAnalytics(campaignId: string): Promise<CampaignAnalytics> {
    const response = await this.api.get(`/analytics/campaigns/${campaignId}`);
    return response.data;
  }

  async getDashboardStats(): Promise<AnalyticsOverview> {
    const response = await this.api.get('/analytics/dashboard');
    return response.data;
  }

  // Settings endpoints
  async getSMTPSettings(): Promise<SMTPSettings> {
    const response = await this.api.get('/settings/smtp');
    return response.data.data;
  }

  async updateSMTPSettings(data: SMTPSettings): Promise<SMTPSettings> {
    const response = await this.api.put('/settings/smtp', data);
    return response.data.data;
  }

  async getAISettings(): Promise<AISettings> {
    const response = await this.api.get('/settings/ai');
    return response.data.data;
  }

  async updateAISettings(data: AISettings): Promise<AISettings> {
    const response = await this.api.put('/settings/ai', data);
    return response.data.data;
  }

  async getGeneralSettings(): Promise<GeneralSettings> {
    const response = await this.api.get('/settings/general');
    return response.data.data;
  }

  async updateGeneralSettings(data: GeneralSettings): Promise<GeneralSettings> {
    const response = await this.api.put('/settings/general', data);
    return response.data.data;
  }

  // AI Assistant endpoints
  async generateEmailContent(request: EmailGenerationRequest): Promise<EmailGenerationResponse> {
    const response = await this.api.post('/ai-assistant/generate-email', request);
    return response.data;
  }

  async optimizeSubjectLine(request: SubjectOptimizationRequest): Promise<SubjectOptimizationResponse> {
    const response = await this.api.post('/ai-assistant/optimize-subject', request);
    return response.data;
  }

  async personalizeEmail(templateContent: string, contactId: number): Promise<{ personalized_content: string }> {
    const response = await this.api.post('/ai-assistant/personalize-email', {
      template: templateContent,
      contact_id: contactId,
      personalization_level: 'medium'
    });
    return response.data;
  }

  async analyzePerformance(sequenceId: number): Promise<any> {
    const response = await this.api.post('/ai-assistant/analyze-performance', {
      sequence_id: sequenceId
    });
    return response.data;
  }

  async getAIStatus(): Promise<AIStatus> {
    const response = await this.api.get('/ai-assistant/ai-status');
    return response.data;
  }

  async getEmailTemplates(campaignType?: string): Promise<{ templates: any[] }> {
    const params = campaignType ? { campaign_type: campaignType } : {};
    const response = await this.api.get('/ai-assistant/templates', { params });
    return response.data;
  }

  // Email Sending endpoints
  async sendEmail(data: {
    to_email: string;
    subject: string;
    content: string;
    from_name?: string;
    from_email?: string;
    reply_to?: string;
  }): Promise<{ message: string; email_id: string }> {
    const response = await this.api.post('/email/send', data);
    return response.data;
  }

  async sendBulkEmails(data: {
    emails: Array<{
      to_email: string;
      subject: string;
      content: string;
      personalization_data?: Record<string, any>;
    }>;
    from_name?: string;
    from_email?: string;
    reply_to?: string;
  }): Promise<{ message: string; batch_id: string }> {
    const response = await this.api.post('/email/send-bulk', data);
    return response.data;
  }

  async sendCampaignEmails(campaignId: number): Promise<{ message: string; batch_id: string }> {
    const response = await this.api.post(`/email/send-campaign/${campaignId}`);
    return response.data;
  }

  async getEmailStatus(emailId: string): Promise<{
    status: string;
    sent_at?: string;
    delivered_at?: string;
    opened_at?: string;
    clicked_at?: string;
    bounced_at?: string;
  }> {
    const response = await this.api.get(`/email/status/${emailId}`);
    return response.data;
  }

  // Additional Analytics endpoints
  async getPerformanceMetrics(): Promise<any> {
    const response = await this.api.get('/analytics/performance');
    return response.data;
  }

  async getEngagementAnalytics(): Promise<any> {
    const response = await this.api.get('/analytics/engagement');
    return response.data;
  }

  async exportAnalytics(format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<{ message: string }> {
    const response = await this.api.get('/analytics/reports/export', {
      params: { format }
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
