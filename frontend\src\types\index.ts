// User and Authentication Types
export interface User {
  id: number;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface LoginCredentials {
  username: string; // Backend expects 'username' field
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  password_confirm: string;
  full_name: string;
}

// Campaign Types
export interface Campaign {
  id: number;
  name: string;
  description?: string;
  campaign_type: 'outreach' | 'follow_up' | 'nurture' | 'transactional';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'archived';
  from_name: string;
  from_email: string;
  reply_to_email: string;
  daily_limit?: number;
  hourly_limit?: number;
  send_weekdays_only?: boolean;
  send_time_start?: string;
  send_time_end?: string;
  use_ai_optimization?: boolean;
  ai_personalization_level?: 'low' | 'medium' | 'high';
  ai_subject_optimization?: boolean;
  track_opens?: boolean;
  track_clicks?: boolean;
  track_replies?: boolean;
  tags?: string[];
  custom_fields?: Record<string, any>;
  created_at: string;
  updated_at: string;
  user_id: number;
  // Statistics (computed fields)
  sequences?: EmailSequence[];
  contacts_count?: number;
  emails_sent?: number;
  emails_delivered?: number;
  emails_opened?: number;
  emails_clicked?: number;
  emails_replied?: number;
  emails_bounced?: number;
}

// Email Sequence Types
export interface EmailSequence {
  id: number;
  campaign_id: number;
  name: string;
  subject_line: string;
  email_content: string;
  order: number;
  delay_days: number;
  delay_hours: number;
  sequence_type?: string;
  send_conditions?: Record<string, any>;
  personalization_fields?: string[];
  ai_optimization_enabled?: boolean;
  track_opens?: boolean;
  track_clicks?: boolean;
  custom_fields?: Record<string, any>;
  status: 'draft' | 'active' | 'paused' | 'completed';
  // Statistics
  emails_sent: number;
  emails_delivered: number;
  emails_opened: number;
  emails_clicked: number;
  emails_replied: number;
  emails_bounced: number;
  // Timestamps
  created_at: string;
  updated_at: string;
  last_sent_at?: string;
}

export interface SequenceCondition {
  id: string;
  type: 'opened' | 'not_opened' | 'replied' | 'not_replied' | 'bounced';
  action: 'continue' | 'stop' | 'move_to_sequence';
  target_sequence_id?: string;
}

// Contact Types
export interface Contact {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  company?: string;
  job_title?: string;
  phone?: string;
  website?: string;
  city?: string;
  state?: string;
  country?: string;
  custom_fields?: Record<string, any>;
  status: 'active' | 'unsubscribed' | 'bounced';
  tags?: string[];
  // Engagement statistics
  emails_sent: number;
  emails_delivered: number;
  emails_opened: number;
  emails_clicked: number;
  emails_replied: number;
  emails_bounced: number;
  last_contacted_at?: string;
  last_opened_at?: string;
  last_clicked_at?: string;
  last_replied_at?: string;
  // Timestamps
  created_at: string;
  updated_at: string;
  user_id: number;
}

export interface ContactList {
  id: number;
  name: string;
  description?: string;
  contacts_count: number;
  created_at: string;
  updated_at: string;
  user_id: number;
}

// Email Template Types
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  category?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  user_id: string;
}

// Analytics Types
export interface AnalyticsOverview {
  total_campaigns: number;
  total_contacts: number;
  total_sequences: number;
  total_emails_sent: number;
  total_emails_delivered: number;
  total_emails_opened: number;
  total_emails_clicked: number;
  total_emails_replied: number;
  overall_open_rate: number;
  overall_click_rate: number;
  overall_reply_rate: number;
  active_campaigns: number;
  recent_activity: Array<{
    type: string;
    description: string;
    timestamp: string;
    campaign_id?: number;
  }>;
}

export interface CampaignAnalytics {
  campaign_id: number;
  campaign_name: string;
  status: string;
  emails_sent: number;
  emails_delivered: number;
  emails_opened: number;
  emails_clicked: number;
  emails_replied: number;
  emails_bounced: number;
  open_rate: number;
  click_rate: number;
  reply_rate: number;
  bounce_rate: number;
  created_at: string;
  last_activity?: string;
}

export interface SequencePerformance {
  sequence_id: number;
  sequence_name: string;
  campaign_name: string;
  order: number;
  emails_sent: number;
  emails_delivered: number;
  emails_opened: number;
  emails_clicked: number;
  emails_replied: number;
  open_rate: number;
  click_rate: number;
  reply_rate: number;
}

export interface DailyStats {
  date: string;
  sent: number;
  opened: number;
  clicked: number;
  replied: number;
  bounced: number;
  unsubscribed: number;
}

// Email Tracking Types
export interface EmailTracking {
  id: string;
  email_id: string;
  contact_id: string;
  campaign_id: string;
  sequence_id: string;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'replied' | 'bounced' | 'unsubscribed';
  opened_at?: string;
  clicked_at?: string;
  replied_at?: string;
  bounced_at?: string;
  unsubscribed_at?: string;
  created_at: string;
}

// Settings Types
export interface SMTPSettings {
  server: string;
  port: number;
  username: string;
  password: string;
  from_email: string;
  from_name: string;
  use_tls: boolean;
}

export interface AISettings {
  openai_api_key?: string;
  openai_model?: string;
  openai_temperature?: number;
  openai_max_tokens?: number;
  ai_enabled: boolean;
}

// AI Assistant Types
export interface EmailGenerationRequest {
  prompt: string;
  context?: string;
  tone: string;
  length: string;
  include_subject: boolean;
  contact_data?: Record<string, any>;
}

export interface EmailGenerationResponse {
  subject: string;
  content: string;
  ai_generated: boolean;
  suggestions?: string[];
}

export interface SubjectOptimizationRequest {
  subject: string;
  context?: string;
  target_audience?: string;
}

export interface SubjectOptimizationResponse {
  original_subject: string;
  optimized_subjects: string[];
  ai_generated: boolean;
}

export interface AIStatus {
  ai_enabled: boolean;
  model: string;
  max_tokens: number;
  capabilities: {
    email_generation: boolean;
    subject_optimization: boolean;
    content_personalization: boolean;
    performance_analysis: boolean;
    template_suggestions: boolean;
  };
  fallback_mode: boolean;
}

export interface GeneralSettings {
  timezone: string;
  date_format: string;
  time_format: string;
  warmup_enabled: boolean;
  warmup_emails_per_day: number;
  rate_limit_per_minute: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Form Types
export interface CampaignFormData {
  name: string;
  description?: string;
  contact_list_ids: string[];
}

export interface SequenceFormData {
  name: string;
  subject: string;
  content: string;
  delay_days: number;
}

// UI Component Types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}
