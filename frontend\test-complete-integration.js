#!/usr/bin/env node
/**
 * Complete Frontend-Backend Integration Test
 * Tests all major features and workflows
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api/v1';

async function testCompleteIntegration() {
  console.log('🚀 Testing Complete Frontend-Backend Integration...\n');
  
  let authToken = null;
  let testCampaignId = null;
  let testContactId = null;
  let testSequenceId = null;
  
  const results = {
    authentication: false,
    campaigns: false,
    contacts: false,
    sequences: false,
    analytics: false,
    ai: false,
    email: false
  };
  
  try {
    // 1. Authentication Flow
    console.log('1. 🔐 Testing Authentication Flow...');
    
    // Register test user
    const registerData = {
      email: '<EMAIL>',
      password: 'TestPass123',
      password_confirm: 'TestPass123',
      full_name: 'Complete Test User'
    };
    
    try {
      await axios.post(`${BASE_URL}/auth/register`, registerData);
      console.log('   ✅ Registration successful');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('   ℹ️  User already exists, continuing...');
      }
    }
    
    // Login
    const loginData = new URLSearchParams();
    loginData.append('username', '<EMAIL>');
    loginData.append('password', 'TestPass123');
    
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    
    authToken = loginResponse.data.access_token;
    console.log('   ✅ Login successful');
    
    // Get current user
    const userResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
    });
    console.log(`   ✅ User info retrieved: ${userResponse.data.email}`);
    
    results.authentication = true;
    
    // 2. Campaign Management
    console.log('\n2. 📋 Testing Campaign Management...');
    
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Create campaign
    const campaignData = {
      name: `Complete Integration Test Campaign ${Date.now()}`,
      description: 'Testing complete frontend-backend integration',
      campaign_type: 'outreach',
      from_name: 'Test User',
      from_email: '<EMAIL>',
      reply_to_email: '<EMAIL>'
    };
    
    const campaignResponse = await axios.post(`${BASE_URL}/campaigns/`, campaignData, { headers });
    testCampaignId = campaignResponse.data.id;
    console.log(`   ✅ Campaign created: ID ${testCampaignId}`);
    
    // Get campaigns
    const campaignsResponse = await axios.get(`${BASE_URL}/campaigns/`, {
      headers,
      params: { skip: 0, limit: 10 }
    });
    console.log(`   ✅ Campaigns retrieved: ${campaignsResponse.data.total} total`);
    
    // Update campaign
    const updateData = { ...campaignData, description: 'Updated description' };
    await axios.put(`${BASE_URL}/campaigns/${testCampaignId}`, updateData, { headers });
    console.log('   ✅ Campaign updated successfully');
    
    results.campaigns = true;
    
    // 3. Contact Management
    console.log('\n3. 👥 Testing Contact Management...');
    
    // Create contact
    const contactData = {
      email: `test-contact-${Date.now()}@example.com`,
      first_name: 'Test',
      last_name: 'Contact',
      company: 'Test Company',
      job_title: 'Test Manager'
    };

    const contactResponse = await axios.post(`${BASE_URL}/contacts/`, contactData, { headers });
    testContactId = contactResponse.data.id;
    console.log(`   ✅ Contact created: ID ${testContactId}`);
    
    // Get contacts
    const contactsResponse = await axios.get(`${BASE_URL}/contacts/`, {
      headers,
      params: { skip: 0, limit: 10 }
    });
    console.log(`   ✅ Contacts retrieved: ${contactsResponse.data.total} total`);
    
    results.contacts = true;
    
    // 4. Email Sequences
    console.log('\n4. 📧 Testing Email Sequences...');
    
    // Create sequence
    const sequenceData = {
      campaign_id: testCampaignId,
      name: 'Test Sequence',
      subject_line: 'Test Subject Line',
      email_content: 'This is a test email content for the sequence.',
      order: 1,
      delay_days: 0,
      delay_hours: 0
    };
    
    const sequenceResponse = await axios.post(`${BASE_URL}/sequences/`, sequenceData, { headers });
    testSequenceId = sequenceResponse.data.id;
    console.log(`   ✅ Sequence created: ID ${testSequenceId}`);
    
    // Get sequences
    const sequencesResponse = await axios.get(`${BASE_URL}/sequences/`, {
      headers,
      params: { campaign_id: testCampaignId }
    });
    console.log(`   ✅ Sequences retrieved for campaign`);
    
    results.sequences = true;
    
    // 5. Analytics
    console.log('\n5. 📊 Testing Analytics...');
    
    // Dashboard analytics
    const analyticsResponse = await axios.get(`${BASE_URL}/analytics/dashboard`, { headers });
    console.log('   ✅ Dashboard analytics retrieved');
    console.log(`      Campaigns: ${analyticsResponse.data.total_campaigns}`);
    console.log(`      Contacts: ${analyticsResponse.data.total_contacts}`);
    console.log(`      Sequences: ${analyticsResponse.data.total_sequences}`);
    
    // Campaign analytics
    const campaignAnalyticsResponse = await axios.get(`${BASE_URL}/analytics/campaigns/${testCampaignId}`, { headers });
    console.log('   ✅ Campaign analytics retrieved');
    
    // Performance metrics
    const performanceResponse = await axios.get(`${BASE_URL}/analytics/performance`, { headers });
    console.log('   ✅ Performance metrics retrieved');
    
    results.analytics = true;
    
    // 6. AI Assistant
    console.log('\n6. 🤖 Testing AI Assistant...');
    
    // AI status
    const aiStatusResponse = await axios.get(`${BASE_URL}/ai-assistant/ai-status`, { headers });
    console.log(`   ✅ AI status: ${aiStatusResponse.data.ai_enabled ? 'Enabled' : 'Disabled'}`);
    
    // Email generation
    const generateRequest = {
      prompt: 'Write a professional email introducing our new product',
      context: 'Product launch campaign',
      tone: 'professional',
      length: 'medium',
      include_subject: true
    };
    
    const generateResponse = await axios.post(`${BASE_URL}/ai-assistant/generate-email`, generateRequest, { headers });
    console.log('   ✅ Email generation successful');
    console.log(`      Subject: ${generateResponse.data.subject}`);
    
    // Subject optimization
    const optimizeRequest = {
      subject: 'Test Subject Line',
      context: 'Product launch',
      target_audience: 'Business professionals'
    };
    
    const optimizeResponse = await axios.post(`${BASE_URL}/ai-assistant/optimize-subject`, optimizeRequest, { headers });
    console.log('   ✅ Subject optimization successful');
    
    results.ai = true;
    
    // 7. Email Sending (Test endpoints)
    console.log('\n7. 📨 Testing Email Infrastructure...');
    
    // Test email status endpoint
    try {
      await axios.get(`${BASE_URL}/email/status/test-email-id`, { headers });
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('   ✅ Email status endpoint accessible');
      }
    }
    
    results.email = true;
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎯 COMPLETE INTEGRATION TEST RESULTS');
    console.log('='.repeat(60));
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      const testName = test.charAt(0).toUpperCase() + test.slice(1);
      console.log(`${testName.padEnd(15)} ${status}`);
    });
    
    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log('\n📈 Summary:');
    console.log(`   ${passedCount}/${totalCount} tests passed`);
    console.log(`   Success rate: ${Math.round((passedCount / totalCount) * 100)}%`);
    
    if (passedCount === totalCount) {
      console.log('\n🎉 ALL INTEGRATION TESTS PASSED!');
      console.log('   Frontend-Backend integration is fully functional');
      console.log('   Ready for production deployment');
    } else {
      console.log('\n⚠️  Some tests failed. Check the logs above.');
    }
    
    console.log('\n🌐 Services:');
    console.log('   Frontend: http://localhost:5173');
    console.log('   Backend:  http://localhost:8000');
    console.log('   API Docs: http://localhost:8000/docs');
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure backend is running on port 8000');
    console.log('   2. Ensure frontend is running on port 5173');
    console.log('   3. Check network connectivity');
    console.log('   4. Verify API endpoints');
  }
}

// Run the complete integration test
testCompleteIntegration().catch(console.error);
