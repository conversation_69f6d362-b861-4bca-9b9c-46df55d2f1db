#!/usr/bin/env node
/**
 * Frontend-Backend Integration Test Script
 * Tests the API service integration with the backend
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api/v1';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

let authToken = null;

async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    // Test registration
    console.log('1. Testing user registration...');
    const registerData = {
      email: '<EMAIL>',
      password: 'TestPass123',
      password_confirm: 'TestPass123',
      full_name: 'Frontend Test User'
    };
    
    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, registerData);
      console.log('✅ Registration successful');
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.detail?.includes('already registered')) {
        console.log('ℹ️  User already exists, continuing with login...');
      } else {
        throw error;
      }
    }
    
    // Test login
    console.log('2. Testing user login...');
    const loginData = new URLSearchParams();
    loginData.append('username', '<EMAIL>');
    loginData.append('password', 'TestPass123');
    
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    authToken = loginResponse.data.access_token;
    console.log('✅ Login successful, token received');
    
    // Test current user
    console.log('3. Testing current user endpoint...');
    const userResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });
    
    console.log('✅ Current user retrieved:', userResponse.data.email);
    
    return true;
  } catch (error) {
    console.error('❌ Authentication test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testCampaigns() {
  console.log('\n📋 Testing Campaign Management...');
  
  try {
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Test get campaigns
    console.log('1. Testing get campaigns...');
    const campaignsResponse = await axios.get(`${BASE_URL}/campaigns/`, {
      headers,
      params: { skip: 0, limit: 10 }
    });
    console.log('✅ Campaigns retrieved:', campaignsResponse.data.total, 'total');
    
    // Test create campaign
    console.log('2. Testing create campaign...');
    const campaignData = {
      name: 'Frontend Integration Test Campaign',
      description: 'Testing frontend-backend integration',
      campaign_type: 'outreach',
      from_name: 'Test User',
      from_email: '<EMAIL>',
      reply_to_email: '<EMAIL>'
    };
    
    const createResponse = await axios.post(`${BASE_URL}/campaigns/`, campaignData, { headers });
    const campaignId = createResponse.data.id;
    console.log('✅ Campaign created with ID:', campaignId);
    
    // Test get single campaign
    console.log('3. Testing get single campaign...');
    const singleCampaignResponse = await axios.get(`${BASE_URL}/campaigns/${campaignId}`, { headers });
    console.log('✅ Single campaign retrieved:', singleCampaignResponse.data.name);
    
    return campaignId;
  } catch (error) {
    console.error('❌ Campaign test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testContacts() {
  console.log('\n👥 Testing Contact Management...');
  
  try {
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Test get contacts
    console.log('1. Testing get contacts...');
    const contactsResponse = await axios.get(`${BASE_URL}/contacts/`, {
      headers,
      params: { skip: 0, limit: 10 }
    });
    console.log('✅ Contacts retrieved:', contactsResponse.data.total, 'total');
    
    // Test create contact
    console.log('2. Testing create contact...');
    const contactData = {
      email: '<EMAIL>',
      first_name: 'Frontend',
      last_name: 'Test',
      company: 'Test Company',
      job_title: 'Test Manager'
    };
    
    const createResponse = await axios.post(`${BASE_URL}/contacts/`, contactData, { headers });
    const contactId = createResponse.data.id;
    console.log('✅ Contact created with ID:', contactId);
    
    return contactId;
  } catch (error) {
    console.error('❌ Contact test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testSequences(campaignId) {
  console.log('\n📧 Testing Email Sequences...');
  
  try {
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Test create sequence
    console.log('1. Testing create sequence...');
    const sequenceData = {
      campaign_id: campaignId,
      name: 'Frontend Test Sequence',
      subject_line: 'Test Subject from Frontend',
      email_content: 'This is a test email from the frontend integration test.',
      order: 1,
      delay_days: 0,
      delay_hours: 0
    };
    
    const createResponse = await axios.post(`${BASE_URL}/sequences/`, sequenceData, { headers });
    const sequenceId = createResponse.data.id;
    console.log('✅ Sequence created with ID:', sequenceId);
    
    // Test get sequences
    console.log('2. Testing get sequences...');
    const sequencesResponse = await axios.get(`${BASE_URL}/sequences/`, {
      headers,
      params: { campaign_id: campaignId }
    });
    console.log('✅ Sequences retrieved for campaign');
    
    return sequenceId;
  } catch (error) {
    console.error('❌ Sequence test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testAnalytics() {
  console.log('\n📊 Testing Analytics...');
  
  try {
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Test dashboard analytics
    console.log('1. Testing dashboard analytics...');
    const dashboardResponse = await axios.get(`${BASE_URL}/analytics/dashboard`, { headers });
    console.log('✅ Dashboard analytics retrieved');
    console.log('   Total Campaigns:', dashboardResponse.data.total_campaigns);
    console.log('   Total Contacts:', dashboardResponse.data.total_contacts);
    console.log('   Total Sequences:', dashboardResponse.data.total_sequences);
    
    // Test performance metrics
    console.log('2. Testing performance metrics...');
    const performanceResponse = await axios.get(`${BASE_URL}/analytics/performance`, { headers });
    console.log('✅ Performance metrics retrieved');
    
    return true;
  } catch (error) {
    console.error('❌ Analytics test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testAIAssistant() {
  console.log('\n🤖 Testing AI Assistant...');
  
  try {
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
    
    // Test AI status
    console.log('1. Testing AI status...');
    const statusResponse = await axios.get(`${BASE_URL}/ai-assistant/ai-status`, { headers });
    console.log('✅ AI status retrieved, enabled:', statusResponse.data.ai_enabled);
    
    // Test email generation
    console.log('2. Testing email generation...');
    const generateRequest = {
      prompt: 'Write a professional email introducing our new product',
      context: 'Product launch campaign',
      tone: 'professional',
      length: 'medium',
      include_subject: true
    };
    
    const generateResponse = await axios.post(`${BASE_URL}/ai-assistant/generate-email`, generateRequest, { headers });
    console.log('✅ Email generated successfully');
    console.log('   Subject:', generateResponse.data.subject);
    
    return true;
  } catch (error) {
    console.error('❌ AI Assistant test failed:', error.response?.data || error.message);
    return false;
  }
}

async function runIntegrationTests() {
  console.log('🚀 Starting Frontend-Backend Integration Tests...');
  console.log('=' * 60);
  
  const results = {
    authentication: false,
    campaigns: false,
    contacts: false,
    sequences: false,
    analytics: false,
    ai: false
  };
  
  // Test authentication first
  results.authentication = await testAuthentication();
  if (!results.authentication) {
    console.log('\n❌ Authentication failed, stopping tests');
    return;
  }
  
  // Test campaigns
  const campaignId = await testCampaigns();
  results.campaigns = campaignId !== null;
  
  // Test contacts
  const contactId = await testContacts();
  results.contacts = contactId !== null;
  
  // Test sequences (requires campaign)
  if (campaignId) {
    const sequenceId = await testSequences(campaignId);
    results.sequences = sequenceId !== null;
  }
  
  // Test analytics
  results.analytics = await testAnalytics();
  
  // Test AI assistant
  results.ai = await testAIAssistant();
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('🎯 INTEGRATION TEST RESULTS');
  console.log('=' * 60);
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${test.toUpperCase().padEnd(15)} ${status}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log('\n📈 Summary:');
  console.log(`   ${passedCount}/${totalCount} tests passed`);
  console.log(`   Success rate: ${Math.round((passedCount / totalCount) * 100)}%`);
  
  if (passedCount === totalCount) {
    console.log('\n🎉 All integration tests passed! Frontend-backend integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the backend server and API endpoints.');
  }
}

// Run the tests
runIntegrationTests().catch(console.error);
