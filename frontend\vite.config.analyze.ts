import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'ui-vendor': ['@tanstack/react-query', 'react-hot-toast'],
          'editor-vendor': ['@tiptap/react', '@tiptap/starter-kit'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          
          // Feature chunks
          'dashboard': ['./src/pages/DashboardPage.tsx'],
          'campaigns': ['./src/pages/CampaignsPage.tsx'],
          'contacts': ['./src/pages/ContactsPage.tsx'],
          'analytics': ['./src/pages/AnalyticsPage.tsx'],
          'settings': ['./src/pages/SettingsPage.tsx'],
        },
      },
    },
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
});
