AI Email Outreach & Follow-Up Tool (Free Stack Edition)
 Features
- Multi-step Cold Email Campaign Builder
- AI-generated email sequences via OpenRouter (<PERSON>, <PERSON><PERSON>, etc.)
- Conditional follow-ups (opened but not replied, bounced, etc.)
- Open & reply tracking (pixel + IMAP polling)
- Email validation using DNS and format checks
- Self-hosted warm-up simulator
- Real-time analytics dashboard (opens, replies, click-throughs)
- Inbox placement testing (manual)
- JWT-based authentication
- Free-only tools and APIs

 Tech Stack
Frontend: React, Tailwind CSS, shadcn/ui, Recharts
Backend: FastAPI (Python), Celery, SQLAlchemy
Database: Supabase (PostgreSQL)
Auth: JWT Tokens
AI Writing: OpenRouter API (Claude, Mistral, etc.)
Email Sending: Gmail SMTP (free tier)
Warm-Up: Self-hosted script
Tracking: DNS, IMAP polling, pixel opens
Hosting: Railway / Render / Vercel (Free tier)

 Project Structure
AI Email Outreach & Follow-Up Tool (Free Stack Edition)
ai-email-outreach-tool/
 frontend/ # React frontend (Vite/Next.js)
 backend/ # FastAPI backend
 .env.example # Template for env vars
 requirements.txt # Python backend deps
 README.md

 Setup Instructions
Backend:
1. cd backend
2. python -m venv venv && source venv/bin/activate
3. pip install -r requirements.txt
4. Create .env file using .env.example
5. uvicorn app.main:app --reload
6. celery -A app.worker worker --loglevel=info
Frontend:
1. cd frontend
2. npm install
3. npm run dev

 Free APIs & Tools Used
- OpenRouter AI AI email generation
- Gmail SMTP Sending emails
- Supabase Free PostgreSQL + Auth
- Mail-tester Inbox placement testing
- MXToolbox Blacklist & DNS validation
- Redis For Celery scheduling

 Auth Flow (JWT)
- Secure login/register
- JWT tokens stored in HTTP-only cookies
- Middleware to protect API routes

 To-Do / Contributions
- Gmail API fallback for higher limits
- CRM export (CSV, Notion, Google Sheets)
- Webhook support for replies
- UI refinements and templates
