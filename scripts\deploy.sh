#!/bin/bash

# =============================================================================
# AI Email Outreach Tool - Production Deployment Script
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-email-outreach"
BACKUP_DIR="/var/backups/${PROJECT_NAME}"
LOG_FILE="/var/log/${PROJECT_NAME}/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env.production exists
    if [[ ! -f ".env.production" ]]; then
        error ".env.production file not found. Please copy .env.production.example and configure it."
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    sudo mkdir -p "$BACKUP_DIR"
    sudo mkdir -p "/var/log/${PROJECT_NAME}"
    sudo mkdir -p "./ssl"
    sudo mkdir -p "./backend/uploads"
    sudo mkdir -p "./backend/logs"
    
    # Set permissions
    sudo chown -R $USER:$USER "./backend/uploads"
    sudo chown -R $USER:$USER "./backend/logs"
    sudo chown -R $USER:$USER "/var/log/${PROJECT_NAME}"
    
    success "Directories created"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="${BACKUP_DIR}/backup_${BACKUP_TIMESTAMP}"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database if container exists
    if docker ps -a | grep -q "email-outreach-db"; then
        log "Backing up database..."
        docker exec email-outreach-db pg_dump -U email_user email_outreach_prod > "${BACKUP_PATH}/database.sql"
        success "Database backup created"
    fi
    
    # Backup uploads
    if [[ -d "./backend/uploads" ]]; then
        cp -r "./backend/uploads" "${BACKUP_PATH}/"
        success "Uploads backup created"
    fi
    
    # Backup logs
    if [[ -d "./backend/logs" ]]; then
        cp -r "./backend/logs" "${BACKUP_PATH}/"
        success "Logs backup created"
    fi
    
    success "Backup completed: $BACKUP_PATH"
}

# Pull latest code
update_code() {
    log "Updating code from repository..."
    
    # Stash any local changes
    git stash
    
    # Pull latest changes
    git pull origin main
    
    success "Code updated"
}

# Build and deploy containers
deploy_containers() {
    log "Building and deploying containers..."
    
    # Stop existing containers
    docker-compose -f docker-compose.production.yml down
    
    # Remove old images (optional)
    docker image prune -f
    
    # Build and start containers
    docker-compose -f docker-compose.production.yml build --no-cache
    docker-compose -f docker-compose.production.yml up -d
    
    success "Containers deployed"
}

# Wait for services to be ready
wait_for_services() {
    log "Waiting for services to be ready..."
    
    # Wait for database
    log "Waiting for database..."
    timeout 60 bash -c 'until docker exec email-outreach-db pg_isready -U email_user; do sleep 2; done'
    
    # Wait for backend
    log "Waiting for backend..."
    timeout 60 bash -c 'until curl -f http://localhost:8000/health; do sleep 2; done'
    
    # Wait for frontend
    log "Waiting for frontend..."
    timeout 60 bash -c 'until curl -f http://localhost/health; do sleep 2; done'
    
    success "All services are ready"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    docker exec email-outreach-backend alembic upgrade head
    
    success "Database migrations completed"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check container status
    if ! docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        error "Some containers are not running properly"
    fi
    
    # Check API health
    if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
        error "Backend health check failed"
    fi
    
    # Check frontend
    if ! curl -f http://localhost > /dev/null 2>&1; then
        error "Frontend health check failed"
    fi
    
    success "Deployment verification passed"
}

# Setup SSL certificates (Let's Encrypt)
setup_ssl() {
    if [[ "$1" == "--ssl" ]]; then
        log "Setting up SSL certificates..."
        
        # Install certbot if not present
        if ! command -v certbot &> /dev/null; then
            sudo apt update
            sudo apt install -y certbot
        fi
        
        # Get domain from environment
        DOMAIN=$(grep DOMAIN .env.production | cut -d '=' -f2)
        API_DOMAIN=$(grep API_DOMAIN .env.production | cut -d '=' -f2)
        
        if [[ -z "$DOMAIN" || -z "$API_DOMAIN" ]]; then
            warning "Domain not configured in .env.production. Skipping SSL setup."
            return
        fi
        
        # Stop nginx temporarily
        docker-compose -f docker-compose.production.yml stop nginx
        
        # Get certificates
        sudo certbot certonly --standalone -d "$DOMAIN" -d "www.$DOMAIN" -d "$API_DOMAIN"
        
        # Copy certificates to ssl directory
        sudo cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "./ssl/"
        sudo cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "./ssl/"
        sudo chown $USER:$USER "./ssl/"*
        
        # Restart nginx
        docker-compose -f docker-compose.production.yml start nginx
        
        success "SSL certificates configured"
    fi
}

# Setup monitoring (optional)
setup_monitoring() {
    if [[ "$1" == "--monitoring" ]]; then
        log "Setting up monitoring..."
        
        docker-compose -f docker-compose.production.yml --profile monitoring up -d
        
        success "Monitoring services started"
        log "Grafana available at: http://localhost:3000"
        log "Prometheus available at: http://localhost:9090"
    fi
}

# Cleanup old backups
cleanup_backups() {
    log "Cleaning up old backups..."
    
    # Keep only last 7 days of backups
    find "$BACKUP_DIR" -type d -name "backup_*" -mtime +7 -exec rm -rf {} \;
    
    success "Old backups cleaned up"
}

# Main deployment function
main() {
    log "Starting deployment of AI Email Outreach Tool..."
    
    check_root
    check_prerequisites
    create_directories
    backup_data
    update_code
    deploy_containers
    wait_for_services
    run_migrations
    verify_deployment
    setup_ssl "$@"
    setup_monitoring "$@"
    cleanup_backups
    
    success "🎉 Deployment completed successfully!"
    log "Frontend: http://localhost (or https://$DOMAIN if SSL configured)"
    log "Backend API: http://localhost:8000 (or https://$API_DOMAIN if SSL configured)"
    log "API Documentation: http://localhost:8000/docs"
    
    if [[ "$*" == *"--monitoring"* ]]; then
        log "Grafana: http://localhost:3000"
        log "Prometheus: http://localhost:9090"
    fi
}

# Help function
show_help() {
    echo "AI Email Outreach Tool - Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --ssl         Setup SSL certificates with Let's Encrypt"
    echo "  --monitoring  Enable monitoring with Prometheus and Grafana"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Basic deployment"
    echo "  $0 --ssl             # Deployment with SSL"
    echo "  $0 --ssl --monitoring # Full deployment with SSL and monitoring"
}

# Parse command line arguments
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
