#!/usr/bin/env python3
"""
Migration script to move data from SQLite to Supabase
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

import asyncpg
import aiosqlite
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupabaseMigrator:
    def __init__(self, sqlite_path: str, supabase_url: str):
        self.sqlite_path = sqlite_path
        self.supabase_url = supabase_url
        
    async def migrate_data(self):
        """Main migration function"""
        logger.info("🚀 Starting migration from SQLite to Supabase...")
        
        try:
            # Connect to both databases
            sqlite_conn = await aiosqlite.connect(self.sqlite_path)
            supabase_engine = create_async_engine(self.supabase_url)
            
            # Get table data from SQLite
            tables_data = await self._extract_sqlite_data(sqlite_conn)
            
            # Insert data into Supabase
            async with supabase_engine.begin() as conn:
                await self._insert_supabase_data(conn, tables_data)
            
            await sqlite_conn.close()
            await supabase_engine.dispose()
            
            logger.info("✅ Migration completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            raise
    
    async def _extract_sqlite_data(self, conn):
        """Extract data from SQLite database"""
        logger.info("📤 Extracting data from SQLite...")
        
        tables_data = {}
        
        # Define tables in dependency order (foreign keys)
        tables = ['users', 'campaigns', 'contacts', 'email_sequences', 'email_logs']
        
        for table in tables:
            try:
                cursor = await conn.execute(f"SELECT * FROM {table}")
                rows = await cursor.fetchall()
                
                # Get column names
                cursor = await conn.execute(f"PRAGMA table_info({table})")
                columns_info = await cursor.fetchall()
                columns = [col[1] for col in columns_info]
                
                # Convert rows to dictionaries
                table_data = []
                for row in rows:
                    row_dict = dict(zip(columns, row))
                    table_data.append(row_dict)
                
                tables_data[table] = table_data
                logger.info(f"   📋 {table}: {len(table_data)} records")
                
            except Exception as e:
                logger.warning(f"   ⚠️  Table {table} not found or error: {e}")
                tables_data[table] = []
        
        return tables_data
    
    async def _insert_supabase_data(self, conn, tables_data):
        """Insert data into Supabase"""
        logger.info("📥 Inserting data into Supabase...")
        
        # Insert in dependency order
        for table_name, data in tables_data.items():
            if not data:
                logger.info(f"   📋 {table_name}: No data to migrate")
                continue
                
            try:
                await self._insert_table_data(conn, table_name, data)
                logger.info(f"   ✅ {table_name}: {len(data)} records migrated")
            except Exception as e:
                logger.error(f"   ❌ {table_name}: Migration failed - {e}")
                raise
    
    async def _insert_table_data(self, conn, table_name, data):
        """Insert data for a specific table"""
        if not data:
            return
        
        # Get the first record to determine columns
        first_record = data[0]
        columns = list(first_record.keys())
        
        # Create placeholders for the query
        placeholders = ', '.join([f':{col}' for col in columns])
        columns_str = ', '.join(columns)
        
        # Handle special cases for different tables
        if table_name == 'users':
            # For users, we might need to handle password hashing
            query = f"""
                INSERT INTO {table_name} ({columns_str})
                VALUES ({placeholders})
                ON CONFLICT (email) DO UPDATE SET
                    full_name = EXCLUDED.full_name,
                    updated_at = NOW()
            """
        else:
            query = f"""
                INSERT INTO {table_name} ({columns_str})
                VALUES ({placeholders})
                ON CONFLICT (id) DO UPDATE SET
                    updated_at = NOW()
            """
        
        # Insert data in batches
        batch_size = 100
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            
            for record in batch:
                # Clean up the record data
                cleaned_record = self._clean_record(record, table_name)
                await conn.execute(text(query), cleaned_record)
    
    def _clean_record(self, record, table_name):
        """Clean and prepare record data for insertion"""
        cleaned = {}
        
        for key, value in record.items():
            # Handle None values
            if value is None:
                cleaned[key] = None
                continue
            
            # Handle datetime strings
            if key in ['created_at', 'updated_at', 'sent_at', 'delivered_at', 
                      'opened_at', 'clicked_at', 'replied_at', 'bounced_at']:
                if isinstance(value, str):
                    try:
                        # Try to parse the datetime string
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        cleaned[key] = dt
                    except:
                        cleaned[key] = datetime.now()
                else:
                    cleaned[key] = value
            else:
                cleaned[key] = value
        
        return cleaned

async def main():
    """Main function to run the migration"""
    
    # Configuration
    SQLITE_PATH = "backend/email_outreach.db"  # Adjust path as needed
    
    # Get Supabase URL from environment or prompt user
    supabase_url = os.getenv("DATABASE_URL")
    
    if not supabase_url:
        print("❌ DATABASE_URL environment variable not found!")
        print("Please set your Supabase database URL:")
        print("export DATABASE_URL='postgresql+asyncpg://postgres:<EMAIL>:5432/postgres'")
        return
    
    if not os.path.exists(SQLITE_PATH):
        print(f"❌ SQLite database not found at: {SQLITE_PATH}")
        print("Please check the path to your SQLite database file.")
        return
    
    # Confirm migration
    print("🔄 AI Email Outreach Tool - Database Migration")
    print("=" * 50)
    print(f"From: SQLite ({SQLITE_PATH})")
    print(f"To:   Supabase ({supabase_url.split('@')[1].split('/')[0]})")
    print()
    
    confirm = input("Do you want to proceed with the migration? (y/N): ")
    if confirm.lower() != 'y':
        print("Migration cancelled.")
        return
    
    # Run migration
    migrator = SupabaseMigrator(SQLITE_PATH, supabase_url)
    await migrator.migrate_data()
    
    print()
    print("🎉 Migration completed!")
    print("Your data has been successfully migrated to Supabase.")
    print()
    print("Next steps:")
    print("1. Update your .env file with Supabase credentials")
    print("2. Test your application with the new database")
    print("3. Update your deployment configuration")

if __name__ == "__main__":
    asyncio.run(main())
