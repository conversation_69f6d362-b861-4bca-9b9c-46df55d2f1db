#!/usr/bin/env python3
"""
Setup script to configure Supabase for AI Email Outreach Tool
"""

import os
import re
from pathlib import Path

def update_env_file():
    """Update the .env file with Supabase configuration"""
    
    print("🔧 Supabase Configuration Setup")
    print("=" * 40)
    print()
    
    # Get Supabase credentials from user
    print("Please provide your Supabase project credentials:")
    print("(You can find these in your Supabase Dashboard → Settings → API)")
    print()
    
    project_url = input("Supabase Project URL (https://your-project-id.supabase.co): ").strip()
    anon_key = input("Supabase Anon Key: ").strip()
    service_role_key = input("Supabase Service Role Key: ").strip()
    db_password = input("Database Password: ").strip()
    
    if not all([project_url, anon_key, service_role_key, db_password]):
        print("❌ All fields are required!")
        return False
    
    # Extract project ID from URL
    project_id_match = re.search(r'https://([^.]+)\.supabase\.co', project_url)
    if not project_id_match:
        print("❌ Invalid Supabase URL format!")
        return False
    
    project_id = project_id_match.group(1)
    
    # Construct database URL
    db_url = f"postgresql+asyncpg://postgres:{db_password}@db.{project_id}.supabase.co:5432/postgres"
    
    # Read current .env file
    env_path = Path("backend/.env")
    if not env_path.exists():
        print("❌ Backend .env file not found!")
        return False
    
    with open(env_path, 'r') as f:
        content = f.read()
    
    # Update Supabase configuration
    content = re.sub(
        r'SUPABASE_URL=.*',
        f'SUPABASE_URL={project_url}',
        content
    )
    content = re.sub(
        r'SUPABASE_ANON_KEY=.*',
        f'SUPABASE_ANON_KEY={anon_key}',
        content
    )
    content = re.sub(
        r'SUPABASE_SERVICE_ROLE_KEY=.*',
        f'SUPABASE_SERVICE_ROLE_KEY={service_role_key}',
        content
    )
    content = re.sub(
        r'DATABASE_URL=postgresql\+asyncpg://.*',
        f'DATABASE_URL={db_url}',
        content
    )
    
    # Write updated content
    with open(env_path, 'w') as f:
        f.write(content)
    
    print()
    print("✅ Environment file updated successfully!")
    print()
    print("Updated configuration:")
    print(f"  Project URL: {project_url}")
    print(f"  Project ID: {project_id}")
    print(f"  Database URL: {db_url}")
    print()
    
    return True

def create_frontend_env():
    """Create frontend .env file with Supabase config"""
    
    # Read backend .env to get Supabase URL and anon key
    backend_env_path = Path("backend/.env")
    if not backend_env_path.exists():
        print("❌ Backend .env file not found!")
        return False
    
    with open(backend_env_path, 'r') as f:
        backend_content = f.read()
    
    # Extract Supabase URL and anon key
    supabase_url_match = re.search(r'SUPABASE_URL=(.*)', backend_content)
    anon_key_match = re.search(r'SUPABASE_ANON_KEY=(.*)', backend_content)
    
    if not supabase_url_match or not anon_key_match:
        print("❌ Supabase configuration not found in backend .env!")
        return False
    
    supabase_url = supabase_url_match.group(1).strip()
    anon_key = anon_key_match.group(1).strip()
    
    # Create or update frontend .env
    frontend_env_path = Path("frontend/.env")
    
    frontend_env_content = f"""# Frontend Environment Configuration

# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1

# Supabase Configuration
VITE_SUPABASE_URL={supabase_url}
VITE_SUPABASE_ANON_KEY={anon_key}

# Application
VITE_APP_NAME=AI Email Outreach Tool
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development
VITE_DEBUG=true
"""
    
    with open(frontend_env_path, 'w') as f:
        f.write(frontend_env_content)
    
    print("✅ Frontend environment file created!")
    return True

def install_dependencies():
    """Install required dependencies"""
    
    print("📦 Installing Supabase dependencies...")
    
    try:
        import subprocess
        
        # Install backend dependencies
        result = subprocess.run([
            "pip", "install", "asyncpg==0.29.0", "supabase==2.0.2", "postgrest-py==0.13.2"
        ], capture_output=True, text=True, cwd="backend")
        
        if result.returncode == 0:
            print("✅ Backend dependencies installed successfully!")
        else:
            print(f"❌ Failed to install backend dependencies: {result.stderr}")
            return False
        
        # Install frontend dependencies
        result = subprocess.run([
            "npm", "install", "@supabase/supabase-js@^2.38.0"
        ], capture_output=True, text=True, cwd="frontend")
        
        if result.returncode == 0:
            print("✅ Frontend dependencies installed successfully!")
        else:
            print(f"❌ Failed to install frontend dependencies: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def test_connection():
    """Test the Supabase connection"""
    
    print("🔍 Testing Supabase connection...")
    
    try:
        import subprocess
        
        # Test backend connection
        result = subprocess.run([
            "python", "-c", 
            "import asyncio; from app.database import engine; print('✅ Database connection successful!')"
        ], capture_output=True, text=True, cwd="backend")
        
        if result.returncode == 0:
            print("✅ Backend connection test passed!")
            return True
        else:
            print(f"❌ Backend connection test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Main setup function"""
    
    print("🚀 AI Email Outreach Tool - Supabase Setup")
    print("=" * 50)
    print()
    print("This script will help you configure Supabase for your application.")
    print()
    
    # Step 1: Update environment configuration
    if not update_env_file():
        print("❌ Setup failed at environment configuration step.")
        return
    
    # Step 2: Create frontend environment
    if not create_frontend_env():
        print("❌ Setup failed at frontend configuration step.")
        return
    
    # Step 3: Install dependencies
    install_deps = input("Install Supabase dependencies? (y/N): ").strip().lower()
    if install_deps == 'y':
        if not install_dependencies():
            print("⚠️  Dependency installation failed, but you can install them manually.")
    
    # Step 4: Test connection
    test_conn = input("Test database connection? (y/N): ").strip().lower()
    if test_conn == 'y':
        test_connection()
    
    print()
    print("🎉 Supabase setup completed!")
    print()
    print("Next steps:")
    print("1. Create your database schema in Supabase Dashboard → SQL Editor")
    print("2. Run the SQL script from QUICK_SUPABASE_SETUP.md")
    print("3. Start your backend: cd backend && python -m uvicorn app.main:app --reload")
    print("4. Start your frontend: cd frontend && npm run dev")
    print()
    print("📚 Documentation:")
    print("- Quick Setup: QUICK_SUPABASE_SETUP.md")
    print("- Full Guide: SUPABASE_INTEGRATION_GUIDE.md")
    print("- Migration: scripts/migrate_to_supabase.py")

if __name__ == "__main__":
    main()
