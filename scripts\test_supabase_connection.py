#!/usr/bin/env python3
"""
Test script to verify Supabase connection
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

async def test_connection():
    """Test the Supabase database connection"""
    
    print("🔍 Testing Supabase Connection...")
    print("=" * 40)
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv(backend_dir / ".env")
        
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ DATABASE_URL not found in environment variables")
            return False
        
        print(f"📡 Connecting to: {database_url.split('@')[1].split('/')[0] if '@' in database_url else 'Unknown'}")
        
        # Test SQLAlchemy connection
        from sqlalchemy.ext.asyncio import create_async_engine
        
        engine = create_async_engine(database_url)
        
        async with engine.begin() as conn:
            result = await conn.execute("SELECT version()")
            version = result.fetchone()[0]
            print(f"✅ Database connection successful!")
            print(f"📊 PostgreSQL version: {version}")
        
        await engine.dispose()
        
        # Test basic table queries
        print("\n🔍 Testing table access...")
        
        engine = create_async_engine(database_url)
        async with engine.begin() as conn:
            # Test if we can query basic tables
            tables_to_test = ['users', 'campaigns', 'contacts', 'email_sequences']
            
            for table in tables_to_test:
                try:
                    result = await conn.execute(f"SELECT COUNT(*) FROM {table}")
                    count = result.fetchone()[0]
                    print(f"✅ Table '{table}': {count} records")
                except Exception as e:
                    print(f"⚠️  Table '{table}': {str(e)}")
        
        await engine.dispose()
        
        # Test Supabase client (if available)
        print("\n🔍 Testing Supabase client...")
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_ANON_KEY")
            
            if supabase_url and supabase_key:
                from supabase import create_client
                
                supabase = create_client(supabase_url, supabase_key)
                
                # Test a simple query
                response = supabase.table('users').select('count', count='exact').execute()
                print(f"✅ Supabase client connection successful!")
                print(f"📊 Users table accessible via Supabase client")
            else:
                print("⚠️  Supabase client credentials not found (optional)")
                
        except ImportError:
            print("⚠️  Supabase client not installed (optional)")
        except Exception as e:
            print(f"⚠️  Supabase client test failed: {e}")
        
        print("\n🎉 Connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your DATABASE_URL in backend/.env")
        print("2. Verify your Supabase project is running")
        print("3. Ensure your database password is correct")
        print("4. Check if your IP is allowed in Supabase settings")
        return False

async def test_crud_operations():
    """Test basic CRUD operations"""
    
    print("\n🔧 Testing CRUD Operations...")
    print("=" * 40)
    
    try:
        from dotenv import load_dotenv
        load_dotenv(backend_dir / ".env")
        
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        
        database_url = os.getenv("DATABASE_URL")
        engine = create_async_engine(database_url)
        
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            # Test user creation
            result = await session.execute("""
                INSERT INTO users (email, password_hash, full_name, is_active) 
                VALUES ('<EMAIL>', 'test_hash', 'Test User', true)
                ON CONFLICT (email) DO UPDATE SET full_name = EXCLUDED.full_name
                RETURNING id, email
            """)
            user = result.fetchone()
            print(f"✅ User CRUD test: Created/Updated user {user.email} (ID: {user.id})")
            
            # Test campaign creation
            result = await session.execute("""
                INSERT INTO campaigns (user_id, name, description, campaign_type, from_name, from_email) 
                VALUES (%s, 'Test Campaign', 'Supabase test campaign', 'outreach', 'Test User', '<EMAIL>')
                RETURNING id, name
            """, (user.id,))
            campaign = result.fetchone()
            print(f"✅ Campaign CRUD test: Created campaign '{campaign.name}' (ID: {campaign.id})")
            
            # Clean up test data
            await session.execute("DELETE FROM campaigns WHERE name = 'Test Campaign'")
            await session.execute("DELETE FROM users WHERE email = '<EMAIL>'")
            await session.commit()
            
            print("✅ CRUD operations test completed successfully!")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ CRUD operations test failed: {e}")
        return False

def main():
    """Main test function"""
    
    print("🧪 Supabase Connection Test")
    print("=" * 50)
    print()
    
    # Check if backend directory exists
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return
    
    # Check if .env file exists
    env_file = backend_dir / ".env"
    if not env_file.exists():
        print("❌ Backend .env file not found!")
        print("Please run the setup script first: python scripts/setup_supabase.py")
        return
    
    # Run tests
    success = asyncio.run(test_connection())
    
    if success:
        run_crud = input("\nRun CRUD operations test? (y/N): ").strip().lower()
        if run_crud == 'y':
            asyncio.run(test_crud_operations())
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Your Supabase integration is working correctly.")
        print("\nYou can now:")
        print("1. Start your backend: cd backend && python -m uvicorn app.main:app --reload")
        print("2. Start your frontend: cd frontend && npm run dev")
        print("3. Access your app at: http://localhost:5173")
    else:
        print("❌ Tests failed. Please check your configuration and try again.")
        print("\n📚 Help:")
        print("- Setup Guide: QUICK_SUPABASE_SETUP.md")
        print("- Configuration: python scripts/setup_supabase.py")

if __name__ == "__main__":
    main()
