#!/usr/bin/env python3
"""
Test if AI button and complete flow is working
"""

import requests

def test_complete_system():
    """Test the complete system including AI generation"""
    
    print("🧪 Testing Complete AI System")
    print("=" * 60)
    
    # Test frontend
    print("\n🌐 Testing Frontend...")
    try:
        frontend_response = requests.get("http://localhost:5174", timeout=5)
        if frontend_response.status_code == 200:
            print("✅ Frontend accessible on http://localhost:5174")
        else:
            print(f"❌ Frontend error: {frontend_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False
    
    # Test backend
    print("\n🔧 Testing Backend...")
    try:
        backend_response = requests.get("http://localhost:8000/health", timeout=5)
        if backend_response.status_code == 200:
            print("✅ Backend accessible on http://localhost:8000")
        else:
            print(f"❌ Backend error: {backend_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False
    
    # Test login
    print("\n🔐 Testing Login...")
    try:
        login_response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPass123"},
            timeout=10
        )
        if login_response.status_code == 200:
            login_data = login_response.json()
            if "error" not in login_data:
                print("✅ Login working")
            else:
                print(f"❌ Login failed: {login_data['error']}")
                return False
        else:
            print(f"❌ Login HTTP error: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test AI generation
    print("\n🤖 Testing AI Generation...")
    try:
        ai_response = requests.post(
            "http://localhost:8000/api/v1/ai/generate-email",
            json={
                "prompt": "Create a professional outreach email",
                "context": "Testing AI generation for sequence builder",
                "tone": "professional",
                "length": "medium",
                "include_subject": True
            },
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        if ai_response.status_code == 200:
            ai_data = ai_response.json()
            if "error" not in ai_data:
                print("✅ AI generation working")
                print(f"📧 Sample subject: {ai_data.get('subject', 'N/A')}")
                print(f"📝 Content preview: {ai_data.get('content', '')[:100]}...")
            else:
                print(f"❌ AI failed: {ai_data['error']}")
                return False
        else:
            print(f"❌ AI HTTP error: {ai_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing AI Button and Complete System")
    print("=" * 70)
    
    success = test_complete_system()
    
    print("\n" + "=" * 70)
    print("📊 System Status:")
    print(f"  Complete System:  {'✅ WORKING' if success else '❌ BROKEN'}")
    
    if success:
        print(f"\n🎉 Complete system is working!")
        print("\n📝 AI Generate button should now work:")
        print("   1. Go to: http://localhost:5174")
        print("   2. Login with: <EMAIL> / TestPass123")
        print("   3. Create/edit a campaign")
        print("   4. Go to sequence builder")
        print("   5. Click '🤖 Generate with AI' button")
        print("   6. Fill out the form and generate!")
        print("\n🎯 The button should open a modal with AI form!")
    else:
        print(f"\n⚠️  System has issues.")
        print("   Check the logs for more details.")
