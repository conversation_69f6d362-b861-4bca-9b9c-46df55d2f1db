#!/usr/bin/env python3
"""
Test campaign creation endpoint
"""

import requests

def test_campaign_endpoints():
    """Test campaign creation and retrieval"""
    
    api_url = "http://localhost:8000"
    
    print("📋 Testing Campaign Endpoints")
    print("=" * 50)
    
    # Test 1: Get campaigns (should work even if empty)
    print("\n1️⃣ Testing GET /api/v1/campaigns/")
    try:
        response = requests.get(f"{api_url}/api/v1/campaigns/", timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GET campaigns successful")
            print(f"📊 Total campaigns: {data.get('total', 0)}")
            print(f"📋 Campaigns: {len(data.get('campaigns', []))}")
        else:
            print(f"❌ GET failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Create a new campaign
    print("\n2️⃣ Testing POST /api/v1/campaigns/")
    
    campaign_data = {
        "name": "Test Campaign",
        "description": "A test campaign created via API",
        "subject_line": "Test Subject",
        "from_name": "Test Sender",
        "from_email": "<EMAIL>",
        "reply_to": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/campaigns/",
            json=campaign_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Campaign creation successful!")
            print(f"📋 Campaign ID: {data.get('id')}")
            print(f"📝 Campaign Name: {data.get('name')}")
            return data.get('id')
        else:
            print(f"❌ Campaign creation failed")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_health_check():
    """Test if backend is healthy"""
    
    api_url = "http://localhost:8000"
    
    print("🏥 Testing Backend Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend is healthy")
            print(f"🗄️  Database: {data.get('database', {}).get('status')}")
            return True
        else:
            print(f"⚠️  Backend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Campaign Creation")
    print("=" * 60)
    
    # Test backend health
    health_ok = test_health_check()
    
    if health_ok:
        # Test campaign endpoints
        campaign_id = test_campaign_endpoints()
        
        print("\n" + "=" * 60)
        print("📊 Test Results:")
        print(f"  Backend Health:     {'✅ PASS' if health_ok else '❌ FAIL'}")
        print(f"  Campaign Creation:  {'✅ PASS' if campaign_id else '❌ FAIL'}")
        
        if campaign_id:
            print(f"\n🎉 Campaign creation is working!")
            print(f"📋 Created campaign ID: {campaign_id}")
            print("\n📝 Try creating a campaign in the frontend now!")
        else:
            print(f"\n⚠️  Campaign creation has issues.")
    else:
        print("\n❌ Backend is not healthy - cannot test campaigns")
