#!/usr/bin/env python3
"""
Test campaign status update functionality
"""

import requests

def test_campaign_status_update():
    """Test creating a campaign and updating its status"""
    
    api_url = "http://localhost:8000"
    
    print("📋 Testing Campaign Status Update")
    print("=" * 50)
    
    # Step 1: Create a campaign
    print("\n1️⃣ Creating a test campaign...")
    campaign_data = {
        "name": "Status Update Test Campaign",
        "description": "Testing status updates",
        "from_name": "Test Sender",
        "from_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/campaigns/",
            json=campaign_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            campaign = response.json()
            if "error" in campaign:
                print(f"❌ Campaign creation failed: {campaign['error']}")
                return False
            
            campaign_id = campaign.get('id')
            initial_status = campaign.get('status')
            print(f"✅ Campaign created successfully!")
            print(f"📋 Campaign ID: {campaign_id}")
            print(f"📊 Initial Status: {initial_status}")
        else:
            print(f"❌ Campaign creation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating campaign: {e}")
        return False
    
    # Step 2: Update campaign status to active
    print(f"\n2️⃣ Updating campaign {campaign_id} status to 'active'...")
    
    try:
        update_data = {"status": "active"}
        response = requests.put(
            f"{api_url}/api/v1/campaigns/{campaign_id}",
            json=update_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Update Status: {response.status_code}")
        print(f"📄 Update Response: {response.text}")
        
        if response.status_code == 200:
            updated_campaign = response.json()
            if "error" in updated_campaign:
                print(f"❌ Status update failed: {updated_campaign['error']}")
                return False
            
            new_status = updated_campaign.get('status')
            print(f"✅ Status update successful!")
            print(f"📊 New Status: {new_status}")
            
            if new_status == "active":
                print(f"🎉 Status correctly updated to 'active'!")
                return True
            else:
                print(f"⚠️  Status update didn't work as expected. Got: {new_status}")
                return False
        else:
            print(f"❌ Status update failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating status: {e}")
        return False

def test_get_updated_campaign():
    """Test retrieving campaigns to verify status persisted"""
    
    api_url = "http://localhost:8000"
    
    print("\n3️⃣ Verifying status persisted in database...")
    
    try:
        response = requests.get(
            f"{api_url}/api/v1/campaigns/",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            campaigns = data.get('campaigns', [])
            
            # Find campaigns with active status
            active_campaigns = [c for c in campaigns if c.get('status') == 'active']
            
            print(f"✅ Retrieved {len(campaigns)} total campaigns")
            print(f"🟢 Found {len(active_campaigns)} active campaigns")
            
            if active_campaigns:
                for campaign in active_campaigns:
                    print(f"   📋 {campaign.get('name')} - Status: {campaign.get('status')}")
                return True
            else:
                print(f"⚠️  No active campaigns found in database")
                return False
        else:
            print(f"❌ Failed to retrieve campaigns: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error retrieving campaigns: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Campaign Status Update")
    print("=" * 60)
    
    # Test status update
    update_success = test_campaign_status_update()
    
    # Test persistence
    persistence_success = test_get_updated_campaign()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  Status Update:    {'✅ PASS' if update_success else '❌ FAIL'}")
    print(f"  Status Persistence: {'✅ PASS' if persistence_success else '❌ FAIL'}")
    
    if update_success and persistence_success:
        print(f"\n🎉 Campaign status updates are working!")
        print("\n📝 Try activating campaigns in the frontend now!")
        print("   The status should update in the database correctly.")
    else:
        print(f"\n⚠️  Campaign status updates have issues.")
        print("   Check the backend logs for more details.")
