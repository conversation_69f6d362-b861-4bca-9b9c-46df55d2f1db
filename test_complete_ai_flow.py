#!/usr/bin/env python3
"""
Test the complete AI sequence generation flow
"""

import requests

def test_complete_ai_flow():
    """Test the complete AI sequence generation flow"""
    
    api_url = "http://localhost:8000"
    
    print("🤖 Testing Complete AI Sequence Generation Flow")
    print("=" * 60)
    
    # Step 1: Login
    print("\n1️⃣ Testing Login...")
    login_data = {
        "username": "<EMAIL>",
        "password": "TestPass123"
    }
    
    try:
        login_response = requests.post(
            f"{api_url}/api/v1/auth/login",
            data=login_data,
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if "error" not in login_result:
                print("✅ Login successful!")
                token = login_result.get('access_token')
            else:
                print(f"❌ Login failed: {login_result['error']}")
                return False
        else:
            print(f"❌ Login HTTP error: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 2: Create a campaign
    print("\n2️⃣ Testing Campaign Creation...")
    campaign_data = {
        "name": "AI Test Campaign",
        "description": "Testing AI sequence generation",
        "from_name": "AI Tester",
        "from_email": "<EMAIL>"
    }
    
    try:
        campaign_response = requests.post(
            f"{api_url}/api/v1/campaigns/",
            json=campaign_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if campaign_response.status_code == 200:
            campaign = campaign_response.json()
            if "error" not in campaign:
                print("✅ Campaign created successfully!")
                campaign_id = campaign.get('id')
                print(f"📋 Campaign ID: {campaign_id}")
            else:
                print(f"❌ Campaign creation failed: {campaign['error']}")
                return False
        else:
            print(f"❌ Campaign HTTP error: {campaign_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Campaign error: {e}")
        return False
    
    # Step 3: Test AI email generation
    print("\n3️⃣ Testing AI Email Generation...")
    ai_data = {
        "prompt": "Create a professional outreach email for a SaaS product targeting marketing managers",
        "context": "We're launching a new email marketing automation tool that helps businesses increase their email engagement rates",
        "tone": "professional",
        "length": "medium",
        "include_subject": True
    }
    
    try:
        ai_response = requests.post(
            f"{api_url}/api/v1/ai/generate-email",
            json=ai_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if ai_response.status_code == 200:
            ai_result = ai_response.json()
            if "error" not in ai_result:
                print("✅ AI email generation successful!")
                print(f"📧 Subject: {ai_result.get('subject', 'N/A')}")
                print(f"📝 Content Preview: {ai_result.get('content', '')[:100]}...")
            else:
                print(f"❌ AI generation failed: {ai_result['error']}")
                return False
        else:
            print(f"❌ AI HTTP error: {ai_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI error: {e}")
        return False
    
    # Step 4: Test multiple AI generations (sequence)
    print("\n4️⃣ Testing AI Sequence Generation...")
    sequence_types = ["initial outreach", "follow-up", "final follow-up"]
    
    for i, email_type in enumerate(sequence_types):
        print(f"\n   📧 Generating {email_type} email...")
        
        sequence_data = {
            "prompt": f"Create a {email_type} email for a SaaS product targeting marketing managers",
            "context": f"Email {i+1} of 3 in the sequence. Previous emails focused on introducing our email marketing automation tool.",
            "tone": "professional",
            "length": "medium",
            "include_subject": True
        }
        
        try:
            seq_response = requests.post(
                f"{api_url}/api/v1/ai/generate-email",
                json=sequence_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if seq_response.status_code == 200:
                seq_result = seq_response.json()
                if "error" not in seq_result:
                    print(f"   ✅ {email_type.title()} generated!")
                    print(f"   📧 Subject: {seq_result.get('subject', 'N/A')}")
                else:
                    print(f"   ❌ Failed: {seq_result['error']}")
            else:
                print(f"   ❌ HTTP error: {seq_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Complete AI Sequence Generation Flow")
    print("=" * 70)
    
    success = test_complete_ai_flow()
    
    print("\n" + "=" * 70)
    print("📊 Test Results:")
    print(f"  Complete Flow:  {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print(f"\n🎉 Complete AI flow is working!")
        print("\n📝 Frontend should now work perfectly:")
        print("   1. Go to: http://localhost:5174")
        print("   2. Login with: <EMAIL> / TestPass123")
        print("   3. Create/edit a campaign")
        print("   4. Go to sequence builder")
        print("   5. Click '🤖 Generate with AI'")
        print("   6. Fill out the form and generate!")
    else:
        print(f"\n⚠️  Some parts of the AI flow have issues.")
        print("   Check the backend logs for more details.")
