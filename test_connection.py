import asyncio
import asyncpg

async def test_connection():
    try:
        print("🔍 Testing Supabase connection...")
        conn = await asyncpg.connect(
            "postgresql://postgres:<EMAIL>:5432/postgres"
        )
        
        # Test basic query
        result = await conn.fetchval("SELECT version()")
        print(f"✅ Connection successful!")
        print(f"📊 PostgreSQL version: {result}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_connection())
