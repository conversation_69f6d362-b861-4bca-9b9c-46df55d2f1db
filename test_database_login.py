#!/usr/bin/env python3
"""
Test database lookup login for any registered user
"""

import requests
import time

def test_database_login():
    """Test login using database lookup for any registered user"""
    
    api_url = "http://localhost:8000"
    
    print("🔐 Testing Database Lookup Login")
    print("=" * 50)
    
    # Test with existing users
    test_users = [
        {"email": "<EMAIL>", "password": "password123"},
        {"email": "<EMAIL>", "password": "password123"},
    ]
    
    for user_creds in test_users:
        print(f"\n👤 Testing user: {user_creds['email']}")
        
        try:
            # Send as multipart form data (like frontend does)
            files = {}  # Empty files dict to force multipart
            response = requests.post(
                f"{api_url}/api/v1/auth/login",
                data=user_creds,  # Use 'data' for form data
                files=files,      # This forces multipart/form-data
                timeout=10
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("access_token"):
                    print(f"✅ Login successful for {user_creds['email']}")
                    print(f"🔑 Token: {data.get('access_token')[:30]}...")
                    print(f"👤 User ID: {data.get('user', {}).get('id')}")
                else:
                    print(f"❌ Login failed: {data.get('error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def create_and_test_new_user():
    """Create a new user and test login"""
    
    api_url = "http://localhost:8000"
    
    print("\n🆕 Testing New User Creation and Login")
    print("=" * 50)
    
    # Generate unique email for this test
    timestamp = int(time.time())
    new_user = {
        "email": f"testuser{timestamp}@example.com",
        "password": "password123",
        "full_name": "Test User Database",
        "username": f"testuser{timestamp}"
    }
    
    print(f"📧 Creating user: {new_user['email']}")
    
    try:
        # 1. Register new user
        response = requests.post(
            f"{api_url}/api/v1/auth/register",
            json=new_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ Registration successful for {new_user['email']}")
                
                # 2. Try to login with the new user
                print(f"🔐 Testing login for new user...")
                
                login_data = {
                    "username": new_user["email"],
                    "password": new_user["password"]
                }
                
                files = {}  # Force multipart
                login_response = requests.post(
                    f"{api_url}/api/v1/auth/login",
                    data=login_data,
                    files=files,
                    timeout=10
                )
                
                print(f"📊 Login Status: {login_response.status_code}")
                
                if login_response.status_code == 200:
                    login_data = login_response.json()
                    if login_data.get("access_token"):
                        print(f"✅ Login successful for new user!")
                        print(f"🔑 Token: {login_data.get('access_token')[:30]}...")
                        return True
                    else:
                        print(f"❌ Login failed: {login_data.get('error')}")
                        return False
                else:
                    print(f"❌ Login HTTP error: {login_response.text}")
                    return False
            else:
                print(f"❌ Registration failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Registration HTTP error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Database Lookup Login")
    print("=" * 60)
    
    # Test existing users
    test_database_login()
    
    # Test new user creation and login
    new_user_success = create_and_test_new_user()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  New User Login: {'✅ PASS' if new_user_success else '❌ FAIL'}")
    
    if new_user_success:
        print("\n🎉 Database lookup login is working!")
        print("✅ Any newly registered user can now login!")
    else:
        print("\n⚠️  Database lookup login has issues.")
