#!/usr/bin/env python3
"""
Test frontend authentication endpoints
"""

import requests
import json

def test_registration():
    """Test user registration endpoint that frontend expects"""
    
    api_url = "http://localhost:8000"
    
    # Test data matching frontend format
    registration_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "full_name": "Frontend Test User",
        "username": "frontenduser"
    }
    
    print("🧪 Testing Frontend Registration Endpoint")
    print("=" * 50)
    print(f"📧 Email: {registration_data['email']}")
    print(f"👤 Username: {registration_data['username']}")
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/auth/register",
            json=registration_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Registration successful!")
                print(f"🔑 Access Token: {data.get('access_token', 'N/A')[:20]}...")
                print(f"👤 User ID: {data.get('user', {}).get('id')}")
                return True, data.get('access_token')
            else:
                print(f"❌ Registration failed: {data.get('error')}")
                return False, None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def test_login(username, password):
    """Test user login endpoint that frontend expects"""
    
    api_url = "http://localhost:8000"
    
    # Frontend sends form data for login
    login_data = {
        "username": username,
        "password": password
    }
    
    print(f"\n🔐 Testing Frontend Login Endpoint")
    print("=" * 50)
    print(f"👤 Username: {username}")
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            json=login_data,  # Note: frontend might send as form data
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("access_token"):
                print("✅ Login successful!")
                print(f"🔑 Access Token: {data.get('access_token')[:20]}...")
                print(f"👤 User: {data.get('user', {}).get('email')}")
                return True, data.get('access_token')
            else:
                print(f"❌ Login failed: {data.get('error')}")
                return False, None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def test_protected_endpoint(token):
    """Test accessing a protected endpoint with token"""
    
    api_url = "http://localhost:8000"
    
    print(f"\n🔒 Testing Protected Endpoint")
    print("=" * 50)
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{api_url}/api/v1/auth/me",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Frontend Authentication Test")
    print("=" * 60)
    
    # Test registration
    reg_success, token = test_registration()
    
    if reg_success and token:
        # Test login with the same credentials
        login_success, login_token = test_login("<EMAIL>", "password123")
        
        if login_success and login_token:
            # Test protected endpoint
            protected_success = test_protected_endpoint(login_token)
            
            print("\n" + "=" * 60)
            print("📊 Test Results:")
            print(f"  Registration: {'✅ PASS' if reg_success else '❌ FAIL'}")
            print(f"  Login:        {'✅ PASS' if login_success else '❌ FAIL'}")
            print(f"  Protected:    {'✅ PASS' if protected_success else '❌ FAIL'}")
            
            if all([reg_success, login_success]):
                print("\n🎉 Frontend authentication is working!")
                print("Your login page should now work correctly.")
            else:
                print("\n⚠️  Some authentication features need fixing.")
        else:
            print("\n❌ Login test failed")
    else:
        print("\n❌ Registration test failed")
