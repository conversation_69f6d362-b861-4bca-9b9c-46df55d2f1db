#!/usr/bin/env python3
"""
Test frontend demo credentials
"""

import requests

def test_frontend_demo_credentials():
    """Test the exact credentials shown in the frontend demo banner"""
    
    api_url = "http://localhost:8000"
    
    print("🎯 Testing Frontend Demo Credentials")
    print("=" * 50)
    
    # These are the exact credentials shown in the frontend demo banner
    demo_credentials = {
        "username": "<EMAIL>",
        "password": "TestPass123"
    }
    
    print(f"📝 Demo Credentials from Frontend:")
    print(f"   Email: {demo_credentials['username']}")
    print(f"   Password: {demo_credentials['password']}")
    
    try:
        # Send multipart form data like the frontend does
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            data=demo_credentials,  # This sends as multipart/form-data
            timeout=10
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if "error" in data:
                print(f"❌ Login failed: {data['error']}")
                return False
            
            print(f"✅ Frontend demo login successful!")
            print(f"🎫 Token: {data.get('access_token', 'N/A')[:30]}...")
            print(f"👤 User: {data.get('user', {}).get('email', 'N/A')}")
            print(f"📛 Name: {data.get('user', {}).get('full_name', 'N/A')}")
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_all_valid_combinations():
    """Test all valid username/password combinations"""
    
    api_url = "http://localhost:8000"
    
    print("\n🧪 Testing All Valid Combinations")
    print("=" * 50)
    
    usernames = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    passwords = ["password123", "TestPass123"]
    
    success_count = 0
    total_tests = len(usernames) * len(passwords)
    
    for username in usernames:
        for password in passwords:
            print(f"\n🔍 Testing: {username} / {password}")
            
            try:
                response = requests.post(
                    f"{api_url}/api/v1/auth/login",
                    data={"username": username, "password": password},
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if "error" not in data:
                        print(f"   ✅ Success!")
                        success_count += 1
                    else:
                        print(f"   ❌ Failed: {data['error']}")
                else:
                    print(f"   ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Results: {success_count}/{total_tests} combinations work")
    return success_count > 0

if __name__ == "__main__":
    print("🧪 Testing Frontend Demo Login")
    print("=" * 60)
    
    # Test the exact frontend demo credentials
    demo_success = test_frontend_demo_credentials()
    
    # Test all combinations
    all_success = test_all_valid_combinations()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  Frontend Demo:  {'✅ PASS' if demo_success else '❌ FAIL'}")
    print(f"  All Combos:     {'✅ PASS' if all_success else '❌ FAIL'}")
    
    if demo_success:
        print(f"\n🎉 Frontend demo login is working!")
        print("\n📝 You can now login with:")
        print("   Email: <EMAIL>")
        print("   Password: TestPass123")
        print("\n🌐 Go to: http://localhost:5174")
    else:
        print(f"\n⚠️  Frontend demo login still has issues.")
        print("   Check the backend logs for more details.")
