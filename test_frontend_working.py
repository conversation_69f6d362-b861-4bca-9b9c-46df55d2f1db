#!/usr/bin/env python3
"""
Test if frontend is working without errors
"""

import requests
import time

def test_frontend_health():
    """Test if frontend is accessible"""
    
    frontend_url = "http://localhost:5173"
    
    print("🌐 Testing Frontend Health")
    print("=" * 50)
    
    try:
        response = requests.get(frontend_url, timeout=5)
        
        if response.status_code == 200:
            print("✅ Frontend is accessible!")
            print(f"📊 Status: {response.status_code}")
            return True
        else:
            print(f"❌ Frontend error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend connection error: {e}")
        return False

def test_backend_health():
    """Test if backend is accessible"""
    
    backend_url = "http://localhost:8000"
    
    print("\n🔧 Testing Backend Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{backend_url}/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ Backend is accessible!")
            print(f"📊 Status: {response.status_code}")
            return True
        else:
            print(f"❌ Backend error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

def test_ai_endpoint():
    """Test AI generation endpoint"""
    
    backend_url = "http://localhost:8000"
    
    print("\n🤖 Testing AI Generation Endpoint")
    print("=" * 50)
    
    ai_data = {
        "prompt": "Create a test email",
        "context": "Testing AI generation",
        "tone": "professional",
        "length": "medium",
        "include_subject": True
    }
    
    try:
        response = requests.post(
            f"{backend_url}/api/v1/ai/generate-email",
            json=ai_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if "error" not in data:
                print("✅ AI generation working!")
                print(f"📧 Subject: {data.get('subject', 'N/A')}")
                return True
            else:
                print(f"❌ AI error: {data['error']}")
                return False
        else:
            print(f"❌ AI HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI connection error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Complete System Health")
    print("=" * 60)
    
    # Test frontend
    frontend_ok = test_frontend_health()
    
    # Test backend
    backend_ok = test_backend_health()
    
    # Test AI
    ai_ok = test_ai_endpoint()
    
    print("\n" + "=" * 60)
    print("📊 System Health Results:")
    print(f"  Frontend:       {'✅ HEALTHY' if frontend_ok else '❌ DOWN'}")
    print(f"  Backend:        {'✅ HEALTHY' if backend_ok else '❌ DOWN'}")
    print(f"  AI Generation:  {'✅ WORKING' if ai_ok else '❌ BROKEN'}")
    
    if frontend_ok and backend_ok and ai_ok:
        print(f"\n🎉 Complete system is working!")
        print("\n📝 Ready to test AI sequence generation:")
        print("   1. Go to: http://localhost:5173")
        print("   2. Login with: <EMAIL> / TestPass123")
        print("   3. Create/edit a campaign")
        print("   4. Go to sequence builder")
        print("   5. Click '🤖 Generate with AI'")
        print("   6. Fill out the form and generate!")
    else:
        print(f"\n⚠️  Some parts of the system have issues.")
        print("   Check the logs for more details.")
