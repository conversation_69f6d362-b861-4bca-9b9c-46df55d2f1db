#!/usr/bin/env python3
"""
Test the login fix with FormData
"""

import requests

def test_login_with_formdata():
    """Test login using FormData like the frontend sends"""
    
    api_url = "http://localhost:8000"
    
    print("🔐 Testing Login with FormData (like frontend)")
    print("=" * 50)
    
    # Use an existing user (from previous registrations)
    login_data = {
        "username": "jeu<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com",  # Use existing user
        "password": "password123"
    }
    
    print(f"👤 Username: {login_data['username']}")
    print(f"🔑 Password: {login_data['password']}")
    
    try:
        # Send as FormData (like frontend does)
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            data=login_data,  # Use 'data' instead of 'json' for FormData
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("access_token"):
                print("✅ Login successful!")
                print(f"🔑 Token: {data.get('access_token')[:30]}...")
                print(f"👤 User: {data.get('user', {}).get('email')}")
                return True
            else:
                print(f"❌ Login failed: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Error details: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_login_with_json():
    """Test login using JSON (alternative method)"""
    
    api_url = "http://localhost:8000"
    
    print("\n🔐 Testing Login with JSON")
    print("=" * 50)
    
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        # Send as JSON
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            json=login_data,  # Use 'json' for JSON data
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("access_token"):
                print("✅ Login successful!")
                return True
            else:
                print(f"❌ Login failed: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Login Fix")
    print("=" * 60)
    
    # Test FormData (like frontend)
    formdata_success = test_login_with_formdata()
    
    # Test JSON (alternative)
    json_success = test_login_with_json()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  FormData Login: {'✅ PASS' if formdata_success else '❌ FAIL'}")
    print(f"  JSON Login:     {'✅ PASS' if json_success else '❌ FAIL'}")
    
    if formdata_success:
        print("\n🎉 FormData login is working! Frontend should work now.")
    else:
        print("\n⚠️  FormData login still has issues.")
