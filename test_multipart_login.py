#!/usr/bin/env python3
"""
Test multipart form data login
"""

import requests

def test_multipart_login():
    """Test login using multipart form data like the frontend sends"""
    
    api_url = "http://localhost:8000"
    
    print("🔐 Testing Multipart Form Data Login")
    print("=" * 50)
    
    # Test with the newly created user
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    
    print(f"👤 Username: {login_data['username']}")
    print(f"🔑 Password: {login_data['password']}")
    
    try:
        # Send as multipart form data (like frontend does)
        files = {}  # Empty files dict to force multipart
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            data=login_data,  # Use 'data' for form data
            files=files,      # This forces multipart/form-data
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("access_token"):
                print("✅ Multipart login successful!")
                print(f"🔑 Token: {data.get('access_token')[:30]}...")
                print(f"👤 User: {data.get('user', {}).get('email')}")
                return True
            else:
                print(f"❌ Login failed: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Error details: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Multipart Form Data Login Fix")
    print("=" * 60)
    
    success = test_multipart_login()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  Multipart Login: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 Multipart login is working! Frontend should work now.")
        print("\n📝 Try logging in with:")
        print("  Email: <EMAIL>")
        print("  Password: password123")
    else:
        print("\n⚠️  Multipart login still has issues.")
