#!/usr/bin/env python3
"""
Test the complete registration flow
"""

import requests
import time

def test_registration_flow():
    """Test the complete registration and login flow"""
    
    api_url = "http://localhost:8000"
    
    # Generate unique email for this test
    timestamp = int(time.time())
    test_email = f"testuser{timestamp}@example.com"
    test_password = "password123"
    
    print("🧪 Testing Complete Registration Flow")
    print("=" * 60)
    print(f"📧 Test Email: {test_email}")
    print(f"🔑 Test Password: {test_password}")
    
    # Step 1: Register new user
    print("\n📝 Step 1: Registering new user...")
    registration_data = {
        "email": test_email,
        "password": test_password,
        "full_name": "Test User Flow",
        "username": f"testuser{timestamp}"
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/auth/register",
            json=registration_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Registration Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Registration successful!")
                print(f"👤 User ID: {data.get('user', {}).get('id')}")
                print(f"📧 Email: {data.get('user', {}).get('email')}")
            else:
                print(f"❌ Registration failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Registration HTTP error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False
    
    # Step 2: Login with the new credentials
    print("\n🔐 Step 2: Logging in with new credentials...")
    login_data = {
        "username": test_email,
        "password": test_password
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("access_token"):
                print("✅ Login successful!")
                print(f"🔑 Token: {data.get('access_token')[:20]}...")
                print(f"👤 User: {data.get('user', {}).get('email')}")
                return True
            else:
                print(f"❌ Login failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Login HTTP error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

def test_frontend_availability():
    """Test if frontend is accessible"""
    
    print("\n🌐 Testing Frontend Availability...")
    
    try:
        response = requests.get("http://localhost:5174", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible at http://localhost:5174")
            return True
        else:
            print(f"⚠️  Frontend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend not accessible: {e}")
        return False

def test_backend_availability():
    """Test if backend is accessible"""
    
    print("\n🔧 Testing Backend Availability...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend is healthy")
            print(f"🗄️  Database: {data.get('database', {}).get('status')}")
            return True
        else:
            print(f"⚠️  Backend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Complete Registration Flow Test")
    print("=" * 70)
    
    # Test backend
    backend_ok = test_backend_availability()
    
    # Test frontend
    frontend_ok = test_frontend_availability()
    
    # Test registration flow
    if backend_ok:
        flow_ok = test_registration_flow()
    else:
        flow_ok = False
    
    print("\n" + "=" * 70)
    print("📊 Test Results:")
    print(f"  Backend:      {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"  Frontend:     {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    print(f"  Registration: {'✅ PASS' if flow_ok else '❌ FAIL'}")
    
    if all([backend_ok, frontend_ok, flow_ok]):
        print("\n🎉 All systems working! Your registration flow is perfect!")
        print("\n📝 Instructions for testing in browser:")
        print("1. Go to: http://localhost:5174")
        print("2. Click 'Create Account' tab")
        print("3. Fill out the form and click 'Create Account'")
        print("4. You should see success message and switch to login")
        print("5. Login with the same credentials")
    else:
        print("\n⚠️  Some issues detected. Check the errors above.")
