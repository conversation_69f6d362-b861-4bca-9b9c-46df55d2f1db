#!/usr/bin/env python3
"""
Test simple campaign creation with minimal fields
"""

import requests

def test_simple_campaign():
    """Test campaign creation with minimal fields"""
    
    api_url = "http://localhost:8000"
    
    print("📋 Testing Simple Campaign Creation")
    print("=" * 50)
    
    # Test with minimal campaign data (only required fields)
    campaign_data = {
        "name": "Simple Test Campaign",
        "status": "draft"
    }
    
    print(f"📝 Campaign data: {campaign_data}")
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/campaigns/",
            json=campaign_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if it's an error response
            if "error" in data:
                print(f"❌ Campaign creation failed: {data['error']}")
                return False
            else:
                print(f"✅ Campaign creation successful!")
                print(f"📋 Campaign ID: {data.get('id')}")
                print(f"📝 Campaign Name: {data.get('name')}")
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_even_simpler_campaign():
    """Test with just name field"""
    
    api_url = "http://localhost:8000"
    
    print("\n📋 Testing Ultra Simple Campaign (name only)")
    print("=" * 50)
    
    # Test with just name
    campaign_data = {
        "name": "Ultra Simple Campaign"
    }
    
    print(f"📝 Campaign data: {campaign_data}")
    
    try:
        response = requests.post(
            f"{api_url}/api/v1/campaigns/",
            json=campaign_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if it's an error response
            if "error" in data:
                print(f"❌ Campaign creation failed: {data['error']}")
                return False
            else:
                print(f"✅ Campaign creation successful!")
                print(f"📋 Campaign ID: {data.get('id')}")
                print(f"📝 Campaign Name: {data.get('name')}")
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Simple Campaign Creation")
    print("=" * 60)
    
    # Test with minimal fields
    simple_success = test_simple_campaign()
    
    # Test with just name
    ultra_simple_success = test_even_simpler_campaign()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  Simple Campaign:      {'✅ PASS' if simple_success else '❌ FAIL'}")
    print(f"  Ultra Simple Campaign: {'✅ PASS' if ultra_simple_success else '❌ FAIL'}")
    
    if simple_success or ultra_simple_success:
        print(f"\n🎉 Campaign creation is working!")
        print("\n📝 Try creating a campaign in the frontend now!")
        print("   Use minimal fields like: name, description")
    else:
        print(f"\n⚠️  Campaign creation still has issues.")
        print("   The campaigns table might have different column names.")
