import requests
import json

def test_supabase_api():
    """Test Supabase connection via REST API"""
    
    # Your Supabase credentials
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    
    print("🔍 Testing Supabase REST API connection...")
    print(f"📡 Project URL: {supabase_url}")
    
    try:
        # Test basic API endpoint
        headers = {
            "apikey": anon_key,
            "Authorization": f"Bearer {anon_key}",
            "Content-Type": "application/json"
        }
        
        # Try to access the health endpoint
        response = requests.get(f"{supabase_url}/rest/v1/", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ Supabase REST API connection successful!")
            print(f"📊 Response: {response.status_code}")
            return True
        else:
            print(f"❌ API connection failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_project_connectivity():
    """Test if we can reach the Supabase project at all"""
    
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    
    print("🌐 Testing basic project connectivity...")
    
    try:
        response = requests.get(supabase_url, timeout=10)
        print(f"✅ Project is reachable! Status: {response.status_code}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot reach project: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Supabase Connectivity Test")
    print("=" * 40)
    
    # Test 1: Basic project connectivity
    if test_project_connectivity():
        print()
        # Test 2: REST API
        test_supabase_api()
    
    print("\n" + "=" * 40)
    print("💡 If tests fail:")
    print("1. Check your internet connection")
    print("2. Verify project ID in Supabase dashboard")
    print("3. Ensure project is not paused/suspended")
    print("4. Check if your firewall blocks outbound connections")
