import asyncio
import asyncpg
import socket

async def test_direct_connection():
    """Test direct connection to Supabase"""
    
    print("🔍 Testing Direct Supabase Connection...")
    print("=" * 50)
    
    # Connection parameters
    host = "db.qavtsyuneoqwqmsbunef.supabase.co"
    port = 5432
    user = "postgres"
    password = "Shreyashj@123"
    database = "postgres"
    
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"User: {user}")
    print(f"Database: {database}")
    print()
    
    # Test 1: Basic network connectivity
    print("1. Testing network connectivity...")
    try:
        # Try to resolve hostname
        ip_address = socket.gethostbyname(host)
        print(f"   ✅ Hostname resolved: {host} -> {ip_address}")
        
        # Try to connect to port
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ Port {port} is accessible")
        else:
            print(f"   ❌ Port {port} is not accessible (error: {result})")
            return False
            
    except socket.gaierror as e:
        print(f"   ❌ Hostname resolution failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Network test failed: {e}")
        return False
    
    # Test 2: PostgreSQL connection
    print("\n2. Testing PostgreSQL connection...")
    try:
        # Try to connect with asyncpg
        conn = await asyncio.wait_for(
            asyncpg.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database,
                ssl='require'
            ),
            timeout=10.0
        )
        
        # Test basic query
        version = await conn.fetchval("SELECT version()")
        print(f"   ✅ PostgreSQL connection successful!")
        print(f"   📊 Version: {version[:60]}...")
        
        # Test if we can create a simple table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS test_connection (
                id SERIAL PRIMARY KEY,
                test_data TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        print("   ✅ Can create tables")
        
        # Clean up test table
        await conn.execute("DROP TABLE IF EXISTS test_connection")
        print("   ✅ Can drop tables")
        
        await conn.close()
        return True
        
    except asyncio.TimeoutError:
        print("   ❌ Connection timeout (10 seconds)")
        return False
    except Exception as e:
        print(f"   ❌ PostgreSQL connection failed: {e}")
        return False

async def test_with_url():
    """Test connection using URL format"""
    print("\n3. Testing with URL format...")
    
    url = "postgresql://postgres:<EMAIL>:5432/postgres"
    print(f"URL: {url}")
    
    try:
        conn = await asyncio.wait_for(
            asyncpg.connect(url, ssl='require'),
            timeout=10.0
        )
        
        result = await conn.fetchval("SELECT 'Connection successful!'")
        print(f"   ✅ URL connection successful: {result}")
        
        await conn.close()
        return True
        
    except asyncio.TimeoutError:
        print("   ❌ URL connection timeout")
        return False
    except Exception as e:
        print(f"   ❌ URL connection failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 Supabase Direct Connection Test")
    print("=" * 50)
    
    # Test direct connection
    direct_success = await test_direct_connection()
    
    # Test URL connection
    url_success = await test_with_url()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Direct connection: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"   URL connection:    {'✅ PASS' if url_success else '❌ FAIL'}")
    
    if direct_success or url_success:
        print("\n🎉 Supabase connection is working!")
        print("Your backend should be able to connect to Supabase.")
    else:
        print("\n❌ Connection failed.")
        print("\n🔧 Possible issues:")
        print("1. Network/firewall blocking connections")
        print("2. Incorrect hostname or credentials")
        print("3. Supabase project is paused/inactive")
        print("4. DNS resolution issues")
        
        print("\n💡 Try these steps:")
        print("1. Check your Supabase project status")
        print("2. Verify credentials in Supabase dashboard")
        print("3. Try connecting from Supabase SQL editor")
        print("4. Check your internet connection")

if __name__ == "__main__":
    asyncio.run(main())
