#!/usr/bin/env python3
"""
Test Supabase endpoints properly
"""

import requests
import json

def test_supabase_endpoints():
    """Test various Supabase endpoints"""
    
    # Your Supabase configuration
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.DM2_9DZoh_NlV7qya5tWtRKQh4zE8qvGwlS8cFOAhB8"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing Supabase Endpoints")
    print("=" * 50)
    print(f"🌐 Project URL: {supabase_url}")
    print(f"🔑 Using anon key: {anon_key[:20]}...")
    print()
    
    # Test endpoints
    endpoints = [
        {
            "name": "Auth Health",
            "url": f"{supabase_url}/auth/v1/health",
            "method": "GET"
        },
        {
            "name": "REST API Root",
            "url": f"{supabase_url}/rest/v1/",
            "method": "GET"
        },
        {
            "name": "Storage Health",
            "url": f"{supabase_url}/storage/v1/",
            "method": "GET"
        },
        {
            "name": "Functions Health",
            "url": f"{supabase_url}/functions/v1/",
            "method": "GET"
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        print(f"🔍 Testing {endpoint['name']}...")
        print(f"   URL: {endpoint['url']}")
        
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(endpoint['url'], headers=headers, timeout=10)
            else:
                response = requests.post(endpoint['url'], headers=headers, timeout=10)
            
            print(f"   ✅ Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   📄 Response: {json.dumps(data, indent=2)[:100]}...")
                except:
                    print(f"   📄 Response: {response.text[:100]}...")
            else:
                print(f"   📄 Response: {response.text[:100]}...")
            
            results.append({
                "endpoint": endpoint['name'],
                "status": response.status_code,
                "success": 200 <= response.status_code < 300
            })
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Error: {e}")
            results.append({
                "endpoint": endpoint['name'],
                "status": "error",
                "success": False,
                "error": str(e)
            })
        
        print()
    
    # Summary
    print("=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    successful = 0
    for result in results:
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        print(f"{result['endpoint']:20} {status}")
        if result['success']:
            successful += 1
    
    print(f"\n🎯 Overall: {successful}/{len(results)} endpoints working")
    
    if successful > 0:
        print("\n🎉 Supabase is reachable and responding!")
        print("The connection issue was likely due to testing the wrong endpoints.")
        return True
    else:
        print("\n❌ All endpoints failed - there may be a connectivity issue.")
        return False

def test_database_connection():
    """Test if we can reach the database through Supabase"""
    
    supabase_url = "https://qavtsyuneoqwqmsbunef.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.DM2_9DZoh_NlV7qya5tWtRKQh4zE8qvGwlS8cFOAhB8"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    print("\n🗄️  Testing Database Access via REST API")
    print("=" * 50)
    
    try:
        # Try to access a table (this will fail if table doesn't exist, but shows connectivity)
        response = requests.get(
            f"{supabase_url}/rest/v1/users?select=count",
            headers=headers,
            timeout=10
        )
        
        print(f"Database query status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code in [200, 404, 406]:  # 404 = table not found, 406 = no select permission
            print("✅ Database is accessible via REST API!")
            return True
        else:
            print("❌ Database access failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Supabase Connectivity Test")
    print("Testing correct API endpoints...")
    print()
    
    # Test API endpoints
    api_success = test_supabase_endpoints()
    
    # Test database access
    db_success = test_database_connection()
    
    print("\n" + "=" * 50)
    print("🏁 Final Results")
    print("=" * 50)
    
    if api_success:
        print("✅ Supabase API endpoints are working!")
        print("✅ Your project is active and reachable!")
        
        if db_success:
            print("✅ Database is accessible!")
            print("\n🎉 Everything looks good! You can now:")
            print("1. Update your backend to use Supabase")
            print("2. Create database tables")
            print("3. Start using your application")
        else:
            print("⚠️  Database needs setup (tables may not exist yet)")
            print("\n📝 Next steps:")
            print("1. Create database tables in Supabase")
            print("2. Set up Row Level Security policies")
            print("3. Test your backend connection")
    else:
        print("❌ Supabase connectivity issues remain")
        print("Please check your network connection and project status")
