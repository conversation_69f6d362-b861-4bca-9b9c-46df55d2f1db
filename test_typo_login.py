#!/usr/bin/env python3
"""
Test login with typo variant
"""

import requests

def test_typo_login():
    """Test login with the typo variant that frontend is sending"""
    
    api_url = "http://localhost:8000"
    
    print("🔐 Testing Typo Login Fix")
    print("=" * 50)
    
    # Test both variants
    test_cases = [
        {
            "name": "Correct spelling",
            "username": "jeu<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com",
            "password": "password123"
        },
        {
            "name": "Typo variant (missing 's')",
            "username": "jeugh<PERSON><PERSON><EMAIL>",
            "password": "password123"
        },
        {
            "name": "Test user",
            "username": "<EMAIL>",
            "password": "password123"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing {test_case['name']}:")
        print(f"   Username: {test_case['username']}")
        
        try:
            # Send multipart form data like the frontend does
            form_data = {
                'username': test_case['username'],
                'password': test_case['password']
            }
            
            response = requests.post(
                f"{api_url}/api/v1/auth/login",
                data=form_data,  # This sends as multipart/form-data
                timeout=10
            )
            
            print(f"   📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if "error" in data:
                    print(f"   ❌ Failed: {data['error']}")
                else:
                    print(f"   ✅ Success!")
                    print(f"   🎫 Token: {data.get('access_token', 'N/A')[:20]}...")
                    print(f"   👤 User: {data.get('user', {}).get('email', 'N/A')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Login with Typo Variants")
    print("=" * 60)
    
    test_typo_login()
    
    print("\n" + "=" * 60)
    print("📝 Now try logging in with the frontend!")
    print("   Go to: http://localhost:5174")
    print("   Use any of the valid usernames with password: password123")
