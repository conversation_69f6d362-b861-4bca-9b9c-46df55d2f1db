#!/usr/bin/env python3
"""
Test user creation via API
"""

import requests
import json

def test_user_creation():
    """Test creating a user via the API"""
    
    api_url = "http://localhost:8000"
    
    print("🧪 Testing User Creation API")
    print("=" * 40)
    
    # Test data
    test_user = {
        "email": "<EMAIL>",
        "full_name": "Test User",
        "username": "testuser",
        "hashed_password": "hashed_password_here"
    }
    
    print(f"📝 Creating user: {test_user['email']}")
    
    try:
        # Create user
        response = requests.post(
            f"{api_url}/api/users",
            json=test_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ User created successfully!")
                user_id = data.get("user", {}).get("id")
                print(f"👤 User ID: {user_id}")
                return True
            else:
                print(f"❌ User creation failed: {data.get('error')}")
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_get_users():
    """Test getting users from the API"""
    
    api_url = "http://localhost:8000"
    
    print("\n🔍 Testing Get Users API")
    print("=" * 40)
    
    try:
        response = requests.get(f"{api_url}/api/users", timeout=10)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            users = data.get("users", [])
            count = data.get("count", 0)
            
            print(f"👥 Found {count} users")
            for i, user in enumerate(users):
                print(f"  {i+1}. {user.get('email')} (ID: {user.get('id')})")
            
            return True
        else:
            print(f"❌ API request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_supabase_direct():
    """Test Supabase connection directly"""
    
    api_url = "http://localhost:8000"
    
    print("\n🔗 Testing Supabase Connection")
    print("=" * 40)
    
    try:
        response = requests.get(f"{api_url}/test-supabase", timeout=10)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"🔧 Configured: {data.get('configured')}")
            print(f"🔌 Connection: {data.get('connection')}")
            
            tables = data.get('tables', {})
            print("📋 Tables:")
            for table, status in tables.items():
                print(f"  {table}: {status}")
            
            return data.get('connection') == 'success'
        else:
            print(f"❌ Test failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 API Testing Suite")
    print("=" * 50)
    
    # Test Supabase connection first
    supabase_ok = test_supabase_direct()
    
    if not supabase_ok:
        print("\n❌ Supabase connection failed - cannot proceed with user tests")
        exit(1)
    
    # Test user creation
    creation_ok = test_user_creation()
    
    # Test getting users
    get_ok = test_get_users()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Supabase Connection: {'✅ PASS' if supabase_ok else '❌ FAIL'}")
    print(f"  User Creation:       {'✅ PASS' if creation_ok else '❌ FAIL'}")
    print(f"  Get Users:           {'✅ PASS' if get_ok else '❌ FAIL'}")
    
    if all([supabase_ok, creation_ok, get_ok]):
        print("\n🎉 All tests passed! Your API is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
